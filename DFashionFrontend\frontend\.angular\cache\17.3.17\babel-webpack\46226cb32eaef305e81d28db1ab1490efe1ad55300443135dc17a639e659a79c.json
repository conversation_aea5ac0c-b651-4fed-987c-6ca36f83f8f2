{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-a1a47f01.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionStart, b as hapticSelectionChanged } from './haptic-554688a5.js';\nimport { createGesture } from './index-2cf77112.js';\nconst createButtonActiveGesture = (el, isButton) => {\n  let currentTouchedButton;\n  let initialTouchedButton;\n  const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    const target = document.elementFromPoint(x, y);\n    if (!target || !isButton(target)) {\n      clearActiveButton();\n      return;\n    }\n    if (target !== currentTouchedButton) {\n      clearActiveButton();\n      setActiveButton(target, hapticFeedbackFn);\n    }\n  };\n  const setActiveButton = (button, hapticFeedbackFn) => {\n    currentTouchedButton = button;\n    if (!initialTouchedButton) {\n      initialTouchedButton = currentTouchedButton;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.add('ion-activated'));\n    hapticFeedbackFn();\n  };\n  const clearActiveButton = (dispatchClick = false) => {\n    if (!currentTouchedButton) {\n      return;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.remove('ion-activated'));\n    /**\n     * Clicking on one button, but releasing on another button\n     * does not dispatch a click event in browsers, so we\n     * need to do it manually here. Some browsers will\n     * dispatch a click if clicking on one button, dragging over\n     * another button, and releasing on the original button. In that\n     * case, we need to make sure we do not cause a double click there.\n     */\n    if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n      currentTouchedButton.click();\n    }\n    currentTouchedButton = undefined;\n  };\n  return createGesture({\n    el,\n    gestureName: 'buttonActiveDrag',\n    threshold: 0,\n    onStart: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n    onMove: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n    onEnd: () => {\n      clearActiveButton(true);\n      hapticSelectionEnd();\n      initialTouchedButton = undefined;\n    }\n  });\n};\nexport { createButtonActiveGesture as c };", "map": {"version": 3, "names": ["w", "writeTask", "h", "hapticSelectionEnd", "a", "hapticSelectionStart", "b", "hapticSelectionChanged", "createGesture", "createButtonActiveGesture", "el", "isButton", "currentTouchedButton", "initialTouchedButton", "activateButtonAtPoint", "x", "y", "hapticFeedbackFn", "document", "target", "elementFromPoint", "clearActiveButton", "setActiveButton", "button", "buttonToModify", "classList", "add", "dispatchClick", "remove", "click", "undefined", "<PERSON><PERSON><PERSON>", "threshold", "onStart", "ev", "currentX", "currentY", "onMove", "onEnd", "c"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/button-active-414be235.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-a1a47f01.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionStart, b as hapticSelectionChanged } from './haptic-554688a5.js';\nimport { createGesture } from './index-2cf77112.js';\n\nconst createButtonActiveGesture = (el, isButton) => {\n    let currentTouchedButton;\n    let initialTouchedButton;\n    const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n        if (typeof document === 'undefined') {\n            return;\n        }\n        const target = document.elementFromPoint(x, y);\n        if (!target || !isButton(target)) {\n            clearActiveButton();\n            return;\n        }\n        if (target !== currentTouchedButton) {\n            clearActiveButton();\n            setActiveButton(target, hapticFeedbackFn);\n        }\n    };\n    const setActiveButton = (button, hapticFeedbackFn) => {\n        currentTouchedButton = button;\n        if (!initialTouchedButton) {\n            initialTouchedButton = currentTouchedButton;\n        }\n        const buttonToModify = currentTouchedButton;\n        writeTask(() => buttonToModify.classList.add('ion-activated'));\n        hapticFeedbackFn();\n    };\n    const clearActiveButton = (dispatchClick = false) => {\n        if (!currentTouchedButton) {\n            return;\n        }\n        const buttonToModify = currentTouchedButton;\n        writeTask(() => buttonToModify.classList.remove('ion-activated'));\n        /**\n         * Clicking on one button, but releasing on another button\n         * does not dispatch a click event in browsers, so we\n         * need to do it manually here. Some browsers will\n         * dispatch a click if clicking on one button, dragging over\n         * another button, and releasing on the original button. In that\n         * case, we need to make sure we do not cause a double click there.\n         */\n        if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n            currentTouchedButton.click();\n        }\n        currentTouchedButton = undefined;\n    };\n    return createGesture({\n        el,\n        gestureName: 'buttonActiveDrag',\n        threshold: 0,\n        onStart: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n        onMove: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n        onEnd: () => {\n            clearActiveButton(true);\n            hapticSelectionEnd();\n            initialTouchedButton = undefined;\n        },\n    });\n};\n\nexport { createButtonActiveGesture as c };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,SAAS,QAAQ,qBAAqB;AACpD,SAASC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,sBAAsB;AACtH,SAASC,aAAa,QAAQ,qBAAqB;AAEnD,MAAMC,yBAAyB,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;EAChD,IAAIC,oBAAoB;EACxB,IAAIC,oBAAoB;EACxB,MAAMC,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,gBAAgB,KAAK;IACtD,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC;IACJ;IACA,MAAMC,MAAM,GAAGD,QAAQ,CAACE,gBAAgB,CAACL,CAAC,EAAEC,CAAC,CAAC;IAC9C,IAAI,CAACG,MAAM,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAAC,EAAE;MAC9BE,iBAAiB,CAAC,CAAC;MACnB;IACJ;IACA,IAAIF,MAAM,KAAKP,oBAAoB,EAAE;MACjCS,iBAAiB,CAAC,CAAC;MACnBC,eAAe,CAACH,MAAM,EAAEF,gBAAgB,CAAC;IAC7C;EACJ,CAAC;EACD,MAAMK,eAAe,GAAGA,CAACC,MAAM,EAAEN,gBAAgB,KAAK;IAClDL,oBAAoB,GAAGW,MAAM;IAC7B,IAAI,CAACV,oBAAoB,EAAE;MACvBA,oBAAoB,GAAGD,oBAAoB;IAC/C;IACA,MAAMY,cAAc,GAAGZ,oBAAoB;IAC3CX,SAAS,CAAC,MAAMuB,cAAc,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC9DT,gBAAgB,CAAC,CAAC;EACtB,CAAC;EACD,MAAMI,iBAAiB,GAAGA,CAACM,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI,CAACf,oBAAoB,EAAE;MACvB;IACJ;IACA,MAAMY,cAAc,GAAGZ,oBAAoB;IAC3CX,SAAS,CAAC,MAAMuB,cAAc,CAACC,SAAS,CAACG,MAAM,CAAC,eAAe,CAAC,CAAC;IACjE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAID,aAAa,IAAId,oBAAoB,KAAKD,oBAAoB,EAAE;MAChEA,oBAAoB,CAACiB,KAAK,CAAC,CAAC;IAChC;IACAjB,oBAAoB,GAAGkB,SAAS;EACpC,CAAC;EACD,OAAOtB,aAAa,CAAC;IACjBE,EAAE;IACFqB,WAAW,EAAE,kBAAkB;IAC/BC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAGC,EAAE,IAAKpB,qBAAqB,CAACoB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAE/B,oBAAoB,CAAC;IACtFgC,MAAM,EAAGH,EAAE,IAAKpB,qBAAqB,CAACoB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAE7B,sBAAsB,CAAC;IACvF+B,KAAK,EAAEA,CAAA,KAAM;MACTjB,iBAAiB,CAAC,IAAI,CAAC;MACvBlB,kBAAkB,CAAC,CAAC;MACpBU,oBAAoB,GAAGiB,SAAS;IACpC;EACJ,CAAC,CAAC;AACN,CAAC;AAED,SAASrB,yBAAyB,IAAI8B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}