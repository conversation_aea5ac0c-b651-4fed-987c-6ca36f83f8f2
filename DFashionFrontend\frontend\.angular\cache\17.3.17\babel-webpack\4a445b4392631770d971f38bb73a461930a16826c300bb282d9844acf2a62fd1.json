{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../core/services/cart.service\";\nimport * as i3 from \"../../core/services/wishlist.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction PostDetailComponent_div_0_i_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 43);\n  }\n}\nfunction PostDetailComponent_div_0_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵtext(1, \"Follow\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PostDetailComponent_div_0_div_21_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n  }\n  if (rf & 2) {\n    const media_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r3.url, i0.ɵɵsanitizeUrl)(\"alt\", media_r3.alt);\n  }\n}\nfunction PostDetailComponent_div_0_div_21_video_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 49);\n  }\n  if (rf & 2) {\n    const media_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r3.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PostDetailComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, PostDetailComponent_div_0_div_21_img_1_Template, 1, 2, \"img\", 46)(2, PostDetailComponent_div_0_div_21_video_2_Template, 1, 1, \"video\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const media_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r3.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r3.type === \"video\");\n  }\n}\nfunction PostDetailComponent_div_0_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_22_div_1_Template_div_click_0_listener() {\n      const productTag_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.showProductDetails(productTag_r5.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 53);\n    i0.ɵɵelement(2, \"i\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const productTag_r5 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r5.position.x, \"%\")(\"top\", productTag_r5.position.y, \"%\");\n  }\n}\nfunction PostDetailComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtemplate(1, PostDetailComponent_div_0_div_22_div_1_Template, 3, 4, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.products);\n  }\n}\nfunction PostDetailComponent_div_0_div_42_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_42_span_1_Template_span_click_0_listener() {\n      const hashtag_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.searchHashtag(hashtag_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hashtag_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" #\", hashtag_r7, \" \");\n  }\n}\nfunction PostDetailComponent_div_0_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, PostDetailComponent_div_0_div_42_span_1_Template, 2, 1, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.hashtags);\n  }\n}\nfunction PostDetailComponent_div_0_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_43_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 60);\n    i0.ɵɵtext(3, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_43_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(5, \"i\", 62);\n    i0.ɵɵtext(6, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_43_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(8, \"i\", 25);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PostDetailComponent_div_0_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"img\", 65);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_48_Template_img_click_1_listener() {\n      const comment_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProfile(comment_r10.user._id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 67)(4, \"span\", 68);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_48_Template_span_click_4_listener() {\n      const comment_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProfile(comment_r10.user._id));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 69);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 70);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const comment_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", comment_r10.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", comment_r10.user.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", comment_r10.user.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(comment_r10.commentedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.text);\n  }\n}\nfunction PostDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"header\", 4)(2, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(3, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h1\");\n    i0.ɵɵtext(5, \"Post\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showMenu());\n    });\n    i0.ɵɵelement(7, \"i\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"div\", 10)(10, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewProfile(ctx_r1.post.user._id));\n    });\n    i0.ɵɵelement(11, \"img\", 12);\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"div\", 14)(14, \"span\", 15);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, PostDetailComponent_div_0_i_16_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 17);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(19, PostDetailComponent_div_0_button_19_Template, 2, 0, \"button\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 19);\n    i0.ɵɵtemplate(21, PostDetailComponent_div_0_div_21_Template, 3, 2, \"div\", 20)(22, PostDetailComponent_div_0_div_22_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 22)(24, \"div\", 23)(25, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleLike());\n    });\n    i0.ɵɵelement(26, \"i\", 25);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.focusCommentInput());\n    });\n    i0.ɵɵelement(30, \"i\", 27);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sharePost());\n    });\n    i0.ɵɵelement(34, \"i\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSave());\n    });\n    i0.ɵɵelement(36, \"i\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 32)(38, \"span\", 15);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\", 33);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, PostDetailComponent_div_0_div_42_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, PostDetailComponent_div_0_div_43_Template, 10, 0, \"div\", 35);\n    i0.ɵɵelementStart(44, \"div\", 36)(45, \"h3\");\n    i0.ɵɵtext(46, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 37);\n    i0.ɵɵtemplate(48, PostDetailComponent_div_0_div_48_Template, 10, 5, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 39);\n    i0.ɵɵelement(50, \"img\", 40);\n    i0.ɵɵelementStart(51, \"input\", 41, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PostDetailComponent_div_0_Template_input_ngModelChange_51_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newComment, $event) || (ctx_r1.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function PostDetailComponent_div_0_Template_input_keyup_enter_51_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵtext(54, \" Post \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"src\", ctx_r1.post.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.post.user.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.post.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.user.isVerified);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.post.createdAt));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isOwnPost);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.media);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.products.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.post.isLiked);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.post.likes.length);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.post.comments.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"saved\", ctx_r1.post.isSaved);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.post.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.post.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.hashtags.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.products.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.comments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentUser == null ? null : ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newComment);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newComment || !ctx_r1.newComment.trim());\n  }\n}\nfunction PostDetailComponent_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r1.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction PostDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 73)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 76);\n    i0.ɵɵelement(8, \"img\", 77);\n    i0.ɵɵelementStart(9, \"div\", 78)(10, \"p\", 79);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 80)(13, \"span\", 81);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, PostDetailComponent_div_1_span_16_Template, 3, 4, \"span\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 83)(18, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.images[0] == null ? null : ctx_r1.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r1.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedProduct.originalPrice);\n  }\n}\nexport let PostDetailComponent = /*#__PURE__*/(() => {\n  class PostDetailComponent {\n    constructor(route, router, cartService, wishlistService) {\n      this.route = route;\n      this.router = router;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.post = null;\n      this.selectedProduct = null;\n      this.newComment = '';\n      this.currentUser = null;\n      this.isOwnPost = false;\n      this.loading = true;\n    }\n    ngOnInit() {\n      this.loadCurrentUser();\n      this.route.params.subscribe(params => {\n        if (params['id']) {\n          this.loadPost(params['id']);\n        }\n      });\n    }\n    loadCurrentUser() {\n      // TODO: Get from auth service\n      this.currentUser = {\n        _id: 'current-user',\n        username: 'you',\n        fullName: 'Your Name',\n        avatar: ''\n      };\n    }\n    loadPost(postId) {\n      this.loading = true;\n      // Load post from real API\n      fetch(`${environment.apiUrl}/posts/${postId}`).then(response => response.json()).then(data => {\n        if (data.success) {\n          this.post = {\n            ...data.post,\n            isLiked: false,\n            isSaved: false // TODO: Check if current user saved this post\n          };\n          this.isOwnPost = this.post?.user?._id === this.currentUser?._id;\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error('Error loading post:', error);\n        this.loading = false;\n      });\n    }\n    goBack() {\n      this.router.navigate(['/social']);\n    }\n    showMenu() {\n      // TODO: Show post menu\n      console.log('Show post menu');\n    }\n    viewProfile(userId) {\n      this.router.navigate(['/profile', userId]);\n    }\n    toggleLike() {\n      if (!this.post) return;\n      this.post.isLiked = !this.post.isLiked;\n      if (this.post.isLiked) {\n        this.post.likes.push({\n          user: this.currentUser._id,\n          likedAt: new Date()\n        });\n      } else {\n        this.post.likes = this.post.likes.filter(like => like.user !== this.currentUser._id);\n      }\n      // TODO: Update like status via API\n      console.log('Toggle like for post:', this.post._id, this.post.isLiked);\n    }\n    toggleSave() {\n      if (!this.post) return;\n      this.post.isSaved = !this.post.isSaved;\n      if (this.post.isSaved) {\n        this.post.saves.push({\n          user: this.currentUser._id,\n          savedAt: new Date()\n        });\n      } else {\n        this.post.saves = this.post.saves.filter(save => save.user !== this.currentUser._id);\n      }\n      // TODO: Update save status via API\n      console.log('Toggle save for post:', this.post._id, this.post.isSaved);\n    }\n    sharePost() {\n      if (!this.post) return;\n      // TODO: Implement share functionality\n      console.log('Share post:', this.post);\n      if (navigator.share) {\n        navigator.share({\n          title: `${this.post.user.username}'s post`,\n          text: this.post.caption,\n          url: window.location.href\n        });\n      } else {\n        // Fallback: copy to clipboard\n        navigator.clipboard.writeText(window.location.href);\n        alert('Link copied to clipboard!');\n      }\n    }\n    focusCommentInput() {\n      const commentInput = document.querySelector('.comment-input');\n      if (commentInput) {\n        commentInput.focus();\n      }\n    }\n    addComment() {\n      if (!this.post || !this.newComment?.trim()) return;\n      const newComment = {\n        _id: Date.now().toString(),\n        user: {\n          _id: this.currentUser._id,\n          username: this.currentUser.username,\n          fullName: this.currentUser.fullName,\n          avatar: this.currentUser.avatar\n        },\n        text: this.newComment.trim(),\n        commentedAt: new Date()\n      };\n      this.post.comments.push(newComment);\n      this.newComment = '';\n      // TODO: Add comment via API\n      console.log('Add comment to post:', this.post._id, newComment);\n    }\n    searchHashtag(hashtag) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          hashtag\n        }\n      });\n    }\n    // E-commerce actions\n    buyNow() {\n      if (this.post && this.post.products.length > 0) {\n        const product = this.post.products[0].product;\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            productId: product._id,\n            source: 'post'\n          }\n        });\n      }\n    }\n    addToCart() {\n      if (this.post && this.post.products.length > 0) {\n        const product = this.post.products[0].product;\n        console.log('Add to cart from post:', product);\n        this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n          next: response => {\n            if (response.success) {\n              alert(`${product.name} added to cart!`);\n            } else {\n              alert('Failed to add product to cart');\n            }\n          },\n          error: error => {\n            console.error('Error adding to cart:', error);\n            alert('Error adding product to cart');\n          }\n        });\n      }\n    }\n    addToWishlist() {\n      if (this.post && this.post.products.length > 0) {\n        const product = this.post.products[0].product;\n        console.log('Add to wishlist from post:', product);\n        this.wishlistService.addToWishlist(product._id).subscribe({\n          next: response => {\n            if (response.success) {\n              alert(`${product.name} added to wishlist!`);\n            } else {\n              alert('Failed to add product to wishlist');\n            }\n          },\n          error: error => {\n            console.error('Error adding to wishlist:', error);\n            alert('Error adding product to wishlist');\n          }\n        });\n      }\n    }\n    // Product modal\n    showProductDetails(product) {\n      this.selectedProduct = product;\n    }\n    closeProductModal() {\n      this.selectedProduct = null;\n    }\n    buyProductNow() {\n      if (this.selectedProduct) {\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            productId: this.selectedProduct._id,\n            source: 'post'\n          }\n        });\n      }\n    }\n    addProductToCart() {\n      if (this.selectedProduct) {\n        console.log('Add product to cart:', this.selectedProduct);\n        this.cartService.addToCart(this.selectedProduct._id, 1, undefined, undefined).subscribe({\n          next: response => {\n            if (response.success) {\n              alert(`${this.selectedProduct.name} added to cart!`);\n              this.closeProductModal();\n            } else {\n              alert('Failed to add product to cart');\n            }\n          },\n          error: error => {\n            console.error('Error adding to cart:', error);\n            alert('Error adding product to cart');\n          }\n        });\n      }\n    }\n    addProductToWishlist() {\n      if (this.selectedProduct) {\n        // TODO: Add to wishlist via service\n        console.log('Add product to wishlist:', this.selectedProduct);\n        alert(`${this.selectedProduct.name} added to wishlist!`);\n        this.closeProductModal();\n      }\n    }\n    getTimeAgo(date) {\n      const now = new Date();\n      const diffMs = now.getTime() - new Date(date).getTime();\n      const diffMinutes = Math.floor(diffMs / (1000 * 60));\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n      if (diffMinutes < 1) return 'now';\n      if (diffMinutes < 60) return `${diffMinutes}m`;\n      if (diffHours < 24) return `${diffHours}h`;\n      if (diffDays < 7) return `${diffDays}d`;\n      return new Date(date).toLocaleDateString();\n    }\n    static {\n      this.ɵfac = function PostDetailComponent_Factory(t) {\n        return new (t || PostDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.WishlistService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PostDetailComponent,\n        selectors: [[\"app-post-detail\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 2,\n        consts: [[\"commentInput\", \"\"], [\"class\", \"post-detail-container\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"post-detail-container\"], [1, \"detail-header\"], [1, \"btn-back\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"btn-menu\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-detail-content\"], [1, \"post-header\"], [1, \"user-info\", 3, \"click\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username-row\"], [1, \"username\"], [\"class\", \"fas fa-check-circle verified\", 4, \"ngIf\"], [1, \"post-time\"], [\"class\", \"btn-follow\", 4, \"ngIf\"], [1, \"post-media\"], [\"class\", \"media-container\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"action-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"action-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"action-btn\", \"save\", 3, \"click\"], [1, \"fas\", \"fa-bookmark\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"comments-section\"], [1, \"comments-list\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"add-comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"btn-post-comment\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-check-circle\", \"verified\"], [1, \"btn-follow\"], [1, \"media-container\"], [\"class\", \"post-image\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"post-video\", \"controls\", \"\", 3, \"src\", 4, \"ngIf\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"controls\", \"\", 1, \"post-video\", 3, \"src\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\", 3, \"click\"], [1, \"ecommerce-actions\"], [1, \"ecom-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"ecom-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecom-btn\", \"wishlist\", 3, \"click\"], [1, \"comment\"], [1, \"comment-avatar\", 3, \"click\", \"src\", \"alt\"], [1, \"comment-content\"], [1, \"comment-header\"], [1, \"comment-username\", 3, \"click\"], [1, \"comment-time\"], [1, \"comment-text\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"]],\n        template: function PostDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, PostDetailComponent_div_0_Template, 55, 23, \"div\", 1)(1, PostDetailComponent_div_1_Template, 24, 9, \"div\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.post);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".post-detail-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto;background:#fff;min-height:100vh}.detail-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px 20px;border-bottom:1px solid #eee;position:sticky;top:0;background:#fff;z-index:100}.detail-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600;color:#333}.btn-back[_ngcontent-%COMP%], .btn-menu[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;color:#333;cursor:pointer;padding:8px;border-radius:50%}.btn-back[_ngcontent-%COMP%]:hover, .btn-menu[_ngcontent-%COMP%]:hover{background:#f8f9fa}.post-detail-content[_ngcontent-%COMP%]{padding:0}.post-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;cursor:pointer}.user-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%}.user-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.username-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.username[_ngcontent-%COMP%]{font-weight:600;color:#333;font-size:.9rem}.verified[_ngcontent-%COMP%]{color:#1da1f2;font-size:.8rem}.post-time[_ngcontent-%COMP%]{font-size:.8rem;color:#666}.btn-follow[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:8px 16px;border-radius:6px;font-weight:500;cursor:pointer}.btn-follow[_ngcontent-%COMP%]:hover{background:#0056b3}.post-media[_ngcontent-%COMP%]{position:relative;background:#000}.media-container[_ngcontent-%COMP%]{width:100%;aspect-ratio:1;overflow:hidden}.post-image[_ngcontent-%COMP%], .post-video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.product-tags[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.product-tag[_ngcontent-%COMP%]{position:absolute;pointer-events:all;cursor:pointer;transform:translate(-50%,-50%)}.product-tag-icon[_ngcontent-%COMP%]{width:32px;height:32px;background:#ffffffe6;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#333;animation:_ngcontent-%COMP%_pulse 2s infinite;box-shadow:0 2px 8px #0003}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:translate(-50%,-50%) scale(1)}50%{transform:translate(-50%,-50%) scale(1.1)}}.post-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:12px 20px;border-bottom:1px solid #f0f0f0}.primary-actions[_ngcontent-%COMP%]{display:flex;gap:20px}.action-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;color:#333;cursor:pointer;display:flex;align-items:center;gap:6px;padding:8px;border-radius:50%;transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa}.action-btn.liked[_ngcontent-%COMP%]{color:#ff6b6b}.action-btn.saved[_ngcontent-%COMP%]{color:#333}.action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.9rem;font-weight:500}.post-caption[_ngcontent-%COMP%]{padding:16px 20px;line-height:1.4;border-bottom:1px solid #f0f0f0}.caption-text[_ngcontent-%COMP%]{margin-left:8px;color:#333}.hashtags[_ngcontent-%COMP%]{margin-top:8px;display:flex;flex-wrap:wrap;gap:8px}.hashtag[_ngcontent-%COMP%]{color:#1da1f2;cursor:pointer;font-size:.9rem}.hashtag[_ngcontent-%COMP%]:hover{text-decoration:underline}.ecommerce-actions[_ngcontent-%COMP%]{display:flex;gap:8px;padding:16px 20px;border-bottom:1px solid #f0f0f0}.ecom-btn[_ngcontent-%COMP%]{flex:1;padding:12px;border:none;border-radius:6px;font-weight:600;font-size:.9rem;cursor:pointer;display:flex;align-items:center;justify-content:center;gap:6px;transition:all .2s ease}.buy-now[_ngcontent-%COMP%]{background:#ff6b6b;color:#fff}.add-cart[_ngcontent-%COMP%]{background:#4ecdc4;color:#fff}.wishlist[_ngcontent-%COMP%]{background:#ff9ff3;color:#fff}.ecom-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 8px #0003}.comments-section[_ngcontent-%COMP%]{padding:20px}.comments-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 16px;font-size:1.1rem;font-weight:600;color:#333}.comments-list[_ngcontent-%COMP%]{margin-bottom:20px}.comment[_ngcontent-%COMP%]{display:flex;gap:12px;margin-bottom:16px}.comment-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;cursor:pointer}.comment-content[_ngcontent-%COMP%]{flex:1}.comment-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:4px}.comment-username[_ngcontent-%COMP%]{font-weight:600;color:#333;cursor:pointer;font-size:.9rem}.comment-username[_ngcontent-%COMP%]:hover{text-decoration:underline}.comment-time[_ngcontent-%COMP%]{font-size:.8rem;color:#666}.comment-text[_ngcontent-%COMP%]{margin:0;color:#333;line-height:1.4;font-size:.9rem}.add-comment[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding-top:16px;border-top:1px solid #f0f0f0}.comment-input[_ngcontent-%COMP%]{flex:1;border:1px solid #ddd;border-radius:20px;padding:8px 16px;font-size:.9rem;outline:none}.comment-input[_ngcontent-%COMP%]:focus{border-color:#007bff}.btn-post-comment[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:8px 16px;border-radius:20px;font-weight:500;cursor:pointer;font-size:.9rem}.btn-post-comment[_ngcontent-%COMP%]:disabled{background:#ccc;cursor:not-allowed}.btn-post-comment[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.product-modal[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000c;z-index:1000;display:flex;align-items:center;justify-content:center;padding:20px}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;max-width:400px;width:100%;max-height:80vh;overflow-y:auto}.modal-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;border-bottom:1px solid #eee}.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600}.btn-close[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;cursor:pointer;color:#666;padding:4px}.modal-body[_ngcontent-%COMP%]{padding:20px}.product-image[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover;border-radius:8px;margin-bottom:16px}.product-info[_ngcontent-%COMP%]{margin-bottom:20px}.brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:8px}.price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#333}.original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#999;text-decoration:line-through}.modal-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%]{padding:12px;border:none;border-radius:6px;font-weight:600;cursor:pointer;transition:all .2s ease}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-secondary[_ngcontent-%COMP%]{background:#6c757d;color:#fff}.btn-outline[_ngcontent-%COMP%]{background:transparent;color:#007bff;border:1px solid #007bff}@media (max-width: 768px){.post-detail-container[_ngcontent-%COMP%]{margin:0}.detail-header[_ngcontent-%COMP%], .post-header[_ngcontent-%COMP%]{padding:12px 16px}.post-actions[_ngcontent-%COMP%]{padding:8px 16px}.post-caption[_ngcontent-%COMP%]{padding:12px 16px}.ecommerce-actions[_ngcontent-%COMP%]{padding:12px 16px;flex-direction:column;gap:8px}.comments-section[_ngcontent-%COMP%]{padding:16px}}\"]\n      });\n    }\n  }\n  return PostDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}