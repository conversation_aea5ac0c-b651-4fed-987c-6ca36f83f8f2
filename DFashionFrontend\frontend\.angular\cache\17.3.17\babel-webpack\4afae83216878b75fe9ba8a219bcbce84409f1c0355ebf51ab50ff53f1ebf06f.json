{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class WishlistService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://********:5000/api'; // Direct IP for testing\n    this.wishlistItemsSubject = new BehaviorSubject([]);\n    this.wishlistCountSubject = new BehaviorSubject(0);\n    // Public observables\n    this.wishlistItems$ = this.wishlistItemsSubject.asObservable();\n    this.wishlistCount$ = this.wishlistCountSubject.asObservable();\n    this.initializeWishlist();\n  }\n  initializeWishlist() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is authenticated, load from API\n      this.loadWishlist();\n    } else {\n      // Guest user, load from local storage only\n      console.log('🔄 Guest user detected, loading wishlist from local storage only...');\n      this.loadWishlistFromLocalStorage();\n    }\n  }\n  getWishlist(page = 1, limit = 12) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/wishlist?page=${page}&limit=${limit}`, options).pipe(tap(response => {\n      if (response.success) {\n        this.wishlistItemsSubject.next(response.data.items);\n        this.wishlistCountSubject.next(response.data.pagination.totalItems);\n      }\n    }));\n  }\n  addToWishlist(productId) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.post(`${this.API_URL}/wishlist`, {\n      productId\n    }, options).pipe(tap(() => {\n      this.loadWishlist(); // Refresh wishlist after adding\n    }));\n  }\n  removeFromWishlist(productId) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/wishlist/${productId}`, options).pipe(tap(() => {\n      this.loadWishlist(); // Refresh wishlist after removing\n    }));\n  }\n  clearWishlist() {\n    return this.http.delete(`${this.API_URL}/wishlist`).pipe(tap(() => {\n      this.wishlistItemsSubject.next([]);\n      this.wishlistCountSubject.next(0);\n    }));\n  }\n  moveToCart(productId, quantity = 1, size, color) {\n    return this.http.post(`${this.API_URL}/wishlist/move-to-cart/${productId}`, {\n      quantity,\n      size,\n      color\n    }).pipe(tap(() => {\n      this.loadWishlist(); // Refresh wishlist after moving\n    }));\n  }\n  getWishlistCount() {\n    return this.wishlistCountSubject.value;\n  }\n  isInWishlist(productId) {\n    const items = this.wishlistItemsSubject.value;\n    return items.some(item => item.product._id === productId);\n  }\n  toggleWishlist(productId) {\n    if (this.isInWishlist(productId)) {\n      return this.removeFromWishlist(productId);\n    } else {\n      return this.addToWishlist(productId);\n    }\n  }\n  loadWishlist() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadWishlistFromLocalStorage();\n      return;\n    }\n    this.getWishlist().subscribe({\n      next: response => {\n        // Wishlist is already updated in the tap operator\n      },\n      error: error => {\n        console.error('Failed to load wishlist:', error);\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n        }\n        // Use localStorage as fallback\n        this.loadWishlistFromLocalStorage();\n      }\n    });\n  }\n  loadWishlistFromLocalStorage() {\n    try {\n      const savedWishlist = localStorage.getItem('wishlist');\n      if (savedWishlist) {\n        const wishlistData = JSON.parse(savedWishlist);\n        this.wishlistItemsSubject.next(wishlistData.items || []);\n        this.wishlistCountSubject.next(wishlistData.items?.length || 0);\n      }\n    } catch (error) {\n      console.error('Failed to load wishlist from localStorage:', error);\n    }\n  }\n  // Get current wishlist count\n  getCurrentCount() {\n    return this.wishlistCountSubject.value;\n  }\n  // Sync with server when online\n  syncWithServer() {\n    return this.getWishlist().pipe(tap(response => {\n      if (response && response.success) {\n        const items = response.data?.items || [];\n        this.wishlistItemsSubject.next(items);\n        this.wishlistCountSubject.next(items.length);\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function WishlistService_Factory(t) {\n      return new (t || WishlistService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WishlistService,\n      factory: WishlistService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "WishlistService", "constructor", "http", "API_URL", "wishlistItemsSubject", "wishlistCountSubject", "wishlistItems$", "asObservable", "wishlistCount$", "initializeWishlist", "token", "localStorage", "getItem", "loadWishlist", "console", "log", "loadWishlistFromLocalStorage", "getWishlist", "page", "limit", "options", "headers", "get", "pipe", "response", "success", "next", "data", "items", "pagination", "totalItems", "addToWishlist", "productId", "post", "removeFromWishlist", "delete", "clearWishlist", "moveToCart", "quantity", "size", "color", "getWishlistCount", "value", "isInWishlist", "some", "item", "product", "_id", "toggleWishlist", "subscribe", "error", "status", "removeItem", "savedWishlist", "wishlistData", "JSON", "parse", "length", "getCurrentCount", "syncWithServer", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\wishlist.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { tap } from 'rxjs/operators';\n\nimport { environment } from '../../../environments/environment';\n\nexport interface WishlistItem {\n  _id: string;\n  product: {\n    _id: string;\n    name: string;\n    price: number;\n    originalPrice?: number;\n    images: Array<{ url: string; alt?: string; isPrimary: boolean }>;\n    brand: string;\n    discount: number;\n    rating: {\n      average: number;\n      count: number;\n    };\n    analytics: {\n      views: number;\n      likes: number;\n    };\n  };\n  addedAt: Date;\n}\n\nexport interface WishlistResponse {\n  success: boolean;\n  data: {\n    items: WishlistItem[];\n    pagination: {\n      currentPage: number;\n      totalPages: number;\n      totalItems: number;\n      hasNextPage: boolean;\n      hasPrevPage: boolean;\n    };\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class WishlistService {\n  private readonly API_URL = 'http://********:5000/api'; // Direct IP for testing\n  private wishlistItemsSubject = new BehaviorSubject<WishlistItem[]>([]);\n  private wishlistCountSubject = new BehaviorSubject<number>(0);\n\n  // Public observables\n  public wishlistItems$ = this.wishlistItemsSubject.asObservable();\n  public wishlistCount$ = this.wishlistCountSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    this.initializeWishlist();\n  }\n\n  private initializeWishlist(): void {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is authenticated, load from API\n      this.loadWishlist();\n    } else {\n      // Guest user, load from local storage only\n      console.log('🔄 Guest user detected, loading wishlist from local storage only...');\n      this.loadWishlistFromLocalStorage();\n    }\n  }\n\n  getWishlist(page: number = 1, limit: number = 12): Observable<WishlistResponse> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n\n    return this.http.get<WishlistResponse>(`${this.API_URL}/wishlist?page=${page}&limit=${limit}`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          this.wishlistItemsSubject.next(response.data.items);\n          this.wishlistCountSubject.next(response.data.pagination.totalItems);\n        }\n      })\n    );\n  }\n\n  addToWishlist(productId: string): Observable<any> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n\n    return this.http.post(`${this.API_URL}/wishlist`, {\n      productId\n    }, options).pipe(\n      tap(() => {\n        this.loadWishlist(); // Refresh wishlist after adding\n      })\n    );\n  }\n\n  removeFromWishlist(productId: string): Observable<any> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n\n    return this.http.delete(`${this.API_URL}/wishlist/${productId}`, options).pipe(\n      tap(() => {\n        this.loadWishlist(); // Refresh wishlist after removing\n      })\n    );\n  }\n\n  clearWishlist(): Observable<any> {\n    return this.http.delete(`${this.API_URL}/wishlist`).pipe(\n      tap(() => {\n        this.wishlistItemsSubject.next([]);\n        this.wishlistCountSubject.next(0);\n      })\n    );\n  }\n\n  moveToCart(productId: string, quantity: number = 1, size?: string, color?: string): Observable<any> {\n    return this.http.post(`${this.API_URL}/wishlist/move-to-cart/${productId}`, {\n      quantity,\n      size,\n      color\n    }).pipe(\n      tap(() => {\n        this.loadWishlist(); // Refresh wishlist after moving\n      })\n    );\n  }\n\n  getWishlistCount(): number {\n    return this.wishlistCountSubject.value;\n  }\n\n  isInWishlist(productId: string): boolean {\n    const items = this.wishlistItemsSubject.value;\n    return items.some(item => item.product._id === productId);\n  }\n\n  toggleWishlist(productId: string): Observable<any> {\n    if (this.isInWishlist(productId)) {\n      return this.removeFromWishlist(productId);\n    } else {\n      return this.addToWishlist(productId);\n    }\n  }\n\n  private loadWishlist(): void {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadWishlistFromLocalStorage();\n      return;\n    }\n\n    this.getWishlist().subscribe({\n      next: (response) => {\n        // Wishlist is already updated in the tap operator\n      },\n      error: (error) => {\n        console.error('Failed to load wishlist:', error);\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n        }\n        // Use localStorage as fallback\n        this.loadWishlistFromLocalStorage();\n      }\n    });\n  }\n\n  private loadWishlistFromLocalStorage(): void {\n    try {\n      const savedWishlist = localStorage.getItem('wishlist');\n      if (savedWishlist) {\n        const wishlistData = JSON.parse(savedWishlist);\n        this.wishlistItemsSubject.next(wishlistData.items || []);\n        this.wishlistCountSubject.next(wishlistData.items?.length || 0);\n      }\n    } catch (error) {\n      console.error('Failed to load wishlist from localStorage:', error);\n    }\n  }\n\n  // Get current wishlist count\n  getCurrentCount(): number {\n    return this.wishlistCountSubject.value;\n  }\n\n  // Sync with server when online\n  syncWithServer(): Observable<any> {\n    return this.getWishlist().pipe(\n      tap((response: any) => {\n        if (response && response.success) {\n          const items = response.data?.items || [];\n          this.wishlistItemsSubject.next(items);\n          this.wishlistCountSubject.next(items.length);\n        }\n      })\n    );\n  }\n}\n"], "mappings": "AAEA,SAAqBA,eAAe,QAAQ,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;;;AA2CpC,OAAM,MAAOC,eAAe;EAS1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IARP,KAAAC,OAAO,GAAG,0BAA0B,CAAC,CAAC;IAC/C,KAAAC,oBAAoB,GAAG,IAAIN,eAAe,CAAiB,EAAE,CAAC;IAC9D,KAAAO,oBAAoB,GAAG,IAAIP,eAAe,CAAS,CAAC,CAAC;IAE7D;IACO,KAAAQ,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;IACzD,KAAAC,cAAc,GAAG,IAAI,CAACH,oBAAoB,CAACE,YAAY,EAAE;IAG9D,IAAI,CAACE,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT;MACA,IAAI,CAACG,YAAY,EAAE;KACpB,MAAM;MACL;MACAC,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;MAClF,IAAI,CAACC,4BAA4B,EAAE;;EAEvC;EAEAC,WAAWA,CAACC,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IAC9C,MAAMT,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMQ,OAAO,GAAGV,KAAK,GAAG;MACtBW,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUX,KAAK;MAAE;KAC9C,GAAG,EAAE;IAEN,OAAO,IAAI,CAACR,IAAI,CAACoB,GAAG,CAAmB,GAAG,IAAI,CAACnB,OAAO,kBAAkBe,IAAI,UAAUC,KAAK,EAAE,EAAEC,OAAO,CAAC,CAACG,IAAI,CAC1GxB,GAAG,CAACyB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAI,CAACrB,oBAAoB,CAACsB,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;QACnD,IAAI,CAACvB,oBAAoB,CAACqB,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACE,UAAU,CAACC,UAAU,CAAC;;IAEvE,CAAC,CAAC,CACH;EACH;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMtB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMQ,OAAO,GAAGV,KAAK,GAAG;MACtBW,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUX,KAAK;MAAE;KAC9C,GAAG,EAAE;IAEN,OAAO,IAAI,CAACR,IAAI,CAAC+B,IAAI,CAAC,GAAG,IAAI,CAAC9B,OAAO,WAAW,EAAE;MAChD6B;KACD,EAAEZ,OAAO,CAAC,CAACG,IAAI,CACdxB,GAAG,CAAC,MAAK;MACP,IAAI,CAACc,YAAY,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC,CACH;EACH;EAEAqB,kBAAkBA,CAACF,SAAiB;IAClC,MAAMtB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMQ,OAAO,GAAGV,KAAK,GAAG;MACtBW,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUX,KAAK;MAAE;KAC9C,GAAG,EAAE;IAEN,OAAO,IAAI,CAACR,IAAI,CAACiC,MAAM,CAAC,GAAG,IAAI,CAAChC,OAAO,aAAa6B,SAAS,EAAE,EAAEZ,OAAO,CAAC,CAACG,IAAI,CAC5ExB,GAAG,CAAC,MAAK;MACP,IAAI,CAACc,YAAY,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC,CACH;EACH;EAEAuB,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClC,IAAI,CAACiC,MAAM,CAAC,GAAG,IAAI,CAAChC,OAAO,WAAW,CAAC,CAACoB,IAAI,CACtDxB,GAAG,CAAC,MAAK;MACP,IAAI,CAACK,oBAAoB,CAACsB,IAAI,CAAC,EAAE,CAAC;MAClC,IAAI,CAACrB,oBAAoB,CAACqB,IAAI,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CACH;EACH;EAEAW,UAAUA,CAACL,SAAiB,EAAEM,QAAA,GAAmB,CAAC,EAAEC,IAAa,EAAEC,KAAc;IAC/E,OAAO,IAAI,CAACtC,IAAI,CAAC+B,IAAI,CAAC,GAAG,IAAI,CAAC9B,OAAO,0BAA0B6B,SAAS,EAAE,EAAE;MAC1EM,QAAQ;MACRC,IAAI;MACJC;KACD,CAAC,CAACjB,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,IAAI,CAACc,YAAY,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC,CACH;EACH;EAEA4B,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACpC,oBAAoB,CAACqC,KAAK;EACxC;EAEAC,YAAYA,CAACX,SAAiB;IAC5B,MAAMJ,KAAK,GAAG,IAAI,CAACxB,oBAAoB,CAACsC,KAAK;IAC7C,OAAOd,KAAK,CAACgB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAACC,GAAG,KAAKf,SAAS,CAAC;EAC3D;EAEAgB,cAAcA,CAAChB,SAAiB;IAC9B,IAAI,IAAI,CAACW,YAAY,CAACX,SAAS,CAAC,EAAE;MAChC,OAAO,IAAI,CAACE,kBAAkB,CAACF,SAAS,CAAC;KAC1C,MAAM;MACL,OAAO,IAAI,CAACD,aAAa,CAACC,SAAS,CAAC;;EAExC;EAEQnB,YAAYA,CAAA;IAClB,MAAMH,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVI,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,IAAI,CAACC,4BAA4B,EAAE;MACnC;;IAGF,IAAI,CAACC,WAAW,EAAE,CAACgC,SAAS,CAAC;MAC3BvB,IAAI,EAAGF,QAAQ,IAAI;QACjB;MAAA,CACD;MACD0B,KAAK,EAAGA,KAAK,IAAI;QACfpC,OAAO,CAACoC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UACxBrC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtDJ,YAAY,CAACyC,UAAU,CAAC,OAAO,CAAC;;QAElC;QACA,IAAI,CAACpC,4BAA4B,EAAE;MACrC;KACD,CAAC;EACJ;EAEQA,4BAA4BA,CAAA;IAClC,IAAI;MACF,MAAMqC,aAAa,GAAG1C,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACtD,IAAIyC,aAAa,EAAE;QACjB,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC;QAC9C,IAAI,CAACjD,oBAAoB,CAACsB,IAAI,CAAC4B,YAAY,CAAC1B,KAAK,IAAI,EAAE,CAAC;QACxD,IAAI,CAACvB,oBAAoB,CAACqB,IAAI,CAAC4B,YAAY,CAAC1B,KAAK,EAAE6B,MAAM,IAAI,CAAC,CAAC;;KAElE,CAAC,OAAOP,KAAK,EAAE;MACdpC,OAAO,CAACoC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;;EAEtE;EAEA;EACAQ,eAAeA,CAAA;IACb,OAAO,IAAI,CAACrD,oBAAoB,CAACqC,KAAK;EACxC;EAEA;EACAiB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC1C,WAAW,EAAE,CAACM,IAAI,CAC5BxB,GAAG,CAAEyB,QAAa,IAAI;MACpB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;QAChC,MAAMG,KAAK,GAAGJ,QAAQ,CAACG,IAAI,EAAEC,KAAK,IAAI,EAAE;QACxC,IAAI,CAACxB,oBAAoB,CAACsB,IAAI,CAACE,KAAK,CAAC;QACrC,IAAI,CAACvB,oBAAoB,CAACqB,IAAI,CAACE,KAAK,CAAC6B,MAAM,CAAC;;IAEhD,CAAC,CAAC,CACH;EACH;;;uBAhKWzD,eAAe,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAf/D,eAAe;MAAAgE,OAAA,EAAfhE,eAAe,CAAAiE,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}