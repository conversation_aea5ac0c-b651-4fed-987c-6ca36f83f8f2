{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = [\"videoPreview\"];\nfunction StoryCreateComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_8_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectFromGallery());\n    });\n    i0.ɵɵelement(3, \"i\", 13);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Gallery\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_8_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.takePhoto());\n    });\n    i0.ɵɵelement(7, \"i\", 14);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"Camera\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_8_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.recordVideo());\n    });\n    i0.ɵɵelement(11, \"i\", 15);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Video\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"input\", 16, 0);\n    i0.ɵɵlistener(\"change\", function StoryCreateComponent_div_8_Template_input_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoryCreateComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"img\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.mediaPreview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StoryCreateComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"video\", 31, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.mediaPreview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StoryCreateComponent_div_9_div_17_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_9_div_17_div_10_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectProduct(product_r6));\n    });\n    i0.ɵɵelement(1, \"img\", 42);\n    i0.ɵɵelementStart(2, \"div\", 43)(3, \"span\", 44);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r6.images[0] == null ? null : product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r6.price, \"\");\n  }\n}\nfunction StoryCreateComponent_div_9_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_9_div_17_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_9_div_17_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 34)(3, \"h3\");\n    i0.ɵɵtext(4, \"Tag Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_9_div_17_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 37)(8, \"input\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoryCreateComponent_div_9_div_17_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.productSearchQuery, $event) || (ctx_r1.productSearchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function StoryCreateComponent_div_9_div_17_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.searchProducts());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵtemplate(10, StoryCreateComponent_div_9_div_17_div_10_Template, 7, 4, \"div\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.productSearchQuery);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.searchResults);\n  }\n}\nfunction StoryCreateComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, StoryCreateComponent_div_9_div_1_Template, 2, 1, \"div\", 18)(2, StoryCreateComponent_div_9_div_2_Template, 3, 1, \"div\", 18);\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"div\", 20)(5, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTool(\"text\"));\n    });\n    i0.ɵɵelement(6, \"i\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 20)(8, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_9_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTool(\"product\"));\n    });\n    i0.ɵɵelement(9, \"i\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function StoryCreateComponent_div_9_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleTool(\"sticker\"));\n    });\n    i0.ɵɵelement(12, \"i\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 25)(14, \"textarea\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoryCreateComponent_div_9_Template_textarea_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.caption, $event) || (ctx_r1.caption = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 27);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, StoryCreateComponent_div_9_div_17_Template, 11, 2, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.mediaType === \"video\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTools === \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTools === \"product\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTools === \"sticker\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.caption);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.caption.length, \"/500\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showProductModal);\n  }\n}\nfunction StoryCreateComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵelement(2, \"div\", 48);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Sharing your story...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let StoryCreateComponent = /*#__PURE__*/(() => {\n  class StoryCreateComponent {\n    constructor(router) {\n      this.router = router;\n      this.selectedMedia = null;\n      this.mediaPreview = '';\n      this.mediaType = 'image';\n      this.caption = '';\n      this.activeTools = '';\n      this.uploading = false;\n      this.showProductModal = false;\n      this.productSearchQuery = '';\n      this.searchResults = [];\n      this.selectedProducts = [];\n    }\n    ngOnInit() {}\n    goBack() {\n      this.router.navigate(['/social']);\n    }\n    selectFromGallery() {\n      this.fileInput.nativeElement.click();\n    }\n    takePhoto() {\n      // For web, this will open file picker with camera option\n      this.fileInput.nativeElement.setAttribute('capture', 'environment');\n      this.fileInput.nativeElement.click();\n    }\n    recordVideo() {\n      // For web, this will open file picker with video option\n      this.fileInput.nativeElement.setAttribute('accept', 'video/*');\n      this.fileInput.nativeElement.setAttribute('capture', 'camcorder');\n      this.fileInput.nativeElement.click();\n    }\n    onFileSelected(event) {\n      const file = event.target.files[0];\n      if (file) {\n        this.selectedMedia = file;\n        this.mediaType = file.type.startsWith('video/') ? 'video' : 'image';\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.mediaPreview = e.target?.result;\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    toggleTool(tool) {\n      if (this.activeTools === tool) {\n        this.activeTools = '';\n      } else {\n        this.activeTools = tool;\n        if (tool === 'product') {\n          this.showProductModal = true;\n          this.searchProducts();\n        }\n      }\n    }\n    closeProductModal() {\n      this.showProductModal = false;\n      this.activeTools = '';\n    }\n    searchProducts() {\n      // Search products from API\n      const query = this.productSearchQuery || '';\n      fetch(`http://localhost:5000/api/products/search?q=${encodeURIComponent(query)}&limit=20`).then(response => response.json()).then(data => {\n        if (data.success) {\n          this.searchResults = data.products;\n        }\n      }).catch(error => {\n        console.error('Error searching products:', error);\n      });\n    }\n    selectProduct(product) {\n      this.selectedProducts.push(product);\n      this.closeProductModal();\n      // TODO: Add product tag to story\n    }\n    shareStory() {\n      if (!this.selectedMedia) return;\n      this.uploading = true;\n      const formData = new FormData();\n      formData.append('media', this.selectedMedia);\n      formData.append('caption', this.caption);\n      formData.append('products', JSON.stringify(this.selectedProducts));\n      fetch('http://localhost:5000/api/stories', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: formData\n      }).then(response => response.json()).then(data => {\n        this.uploading = false;\n        if (data.success) {\n          this.router.navigate(['/social']);\n        } else {\n          alert('Failed to share story. Please try again.');\n        }\n      }).catch(error => {\n        this.uploading = false;\n        console.error('Error sharing story:', error);\n        alert('Failed to share story. Please try again.');\n      });\n    }\n    static {\n      this.ɵfac = function StoryCreateComponent_Factory(t) {\n        return new (t || StoryCreateComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: StoryCreateComponent,\n        selectors: [[\"app-story-create\"]],\n        viewQuery: function StoryCreateComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoPreview = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 11,\n        vars: 5,\n        consts: [[\"fileInput\", \"\"], [\"videoPreview\", \"\"], [1, \"story-create-container\"], [1, \"create-header\"], [1, \"btn-back\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"btn-share\", 3, \"click\", \"disabled\"], [\"class\", \"media-selection\", 4, \"ngIf\"], [\"class\", \"media-preview\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"media-selection\"], [1, \"selection-options\"], [1, \"option-card\", 3, \"click\"], [1, \"fas\", \"fa-images\"], [1, \"fas\", \"fa-camera\"], [1, \"fas\", \"fa-video\"], [\"type\", \"file\", \"accept\", \"image/*,video/*\", 2, \"display\", \"none\", 3, \"change\"], [1, \"media-preview\"], [\"class\", \"preview-container\", 4, \"ngIf\"], [1, \"story-tools\"], [1, \"tool-section\"], [1, \"tool-btn\", 3, \"click\"], [1, \"fas\", \"fa-font\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"fas\", \"fa-smile\"], [1, \"caption-section\"], [\"placeholder\", \"Write a caption...\", \"maxlength\", \"500\", 1, \"caption-input\", 3, \"ngModelChange\", \"ngModel\"], [1, \"char-count\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"preview-container\"], [\"alt\", \"Story preview\", 1, \"preview-media\", 3, \"src\"], [\"controls\", \"\", 1, \"preview-media\", 3, \"src\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"product-search\"], [\"type\", \"text\", \"placeholder\", \"Search products...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"loading-overlay\"], [1, \"loading-content\"], [1, \"loading-spinner\"]],\n        template: function StoryCreateComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function StoryCreateComponent_Template_button_click_2_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵelement(3, \"i\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"h2\");\n            i0.ɵɵtext(5, \"Create Story\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function StoryCreateComponent_Template_button_click_6_listener() {\n              return ctx.shareStory();\n            });\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(8, StoryCreateComponent_div_8_Template, 16, 0, \"div\", 7)(9, StoryCreateComponent_div_9_Template, 18, 11, \"div\", 8)(10, StoryCreateComponent_div_10_Template, 5, 0, \"div\", 9);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedMedia || ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.uploading ? \"Sharing...\" : \"Share\", \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.selectedMedia);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedMedia);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.uploading);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.MaxLengthValidator, i3.NgModel],\n        styles: [\".story-create-container[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000;z-index:1000;display:flex;flex-direction:column}.create-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;background:#000c;color:#fff}.btn-back[_ngcontent-%COMP%], .btn-share[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:1rem;cursor:pointer;padding:8px 16px;border-radius:20px;transition:background .2s ease}.btn-back[_ngcontent-%COMP%]:hover{background:#ffffff1a}.btn-share[_ngcontent-%COMP%]{background:#007bff;font-weight:600}.btn-share[_ngcontent-%COMP%]:disabled{background:#666;cursor:not-allowed}.btn-share[_ngcontent-%COMP%]:not(:disabled):hover{background:#0056b3}.create-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:1.2rem;font-weight:600}.media-selection[_ngcontent-%COMP%]{flex:1;display:flex;align-items:center;justify-content:center;padding:40px}.selection-options[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:20px;max-width:400px;width:100%}.option-card[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px;padding:30px 20px;background:#ffffff1a;border-radius:16px;color:#fff;cursor:pointer;transition:all .3s ease;border:2px solid transparent}.option-card[_ngcontent-%COMP%]:hover{background:#fff3;border-color:#007bff;transform:translateY(-4px)}.option-card[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem}.option-card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}.media-preview[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;position:relative}.preview-container[_ngcontent-%COMP%]{flex:1;display:flex;align-items:center;justify-content:center;padding:20px}.preview-media[_ngcontent-%COMP%]{max-width:100%;max-height:100%;object-fit:contain;border-radius:8px}.story-tools[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:16px;padding:16px;background:#00000080}.tool-btn[_ngcontent-%COMP%]{width:48px;height:48px;border:none;border-radius:50%;background:#fff3;color:#fff;font-size:1.2rem;cursor:pointer;transition:all .2s ease}.tool-btn[_ngcontent-%COMP%]:hover, .tool-btn.active[_ngcontent-%COMP%]{background:#007bff;transform:scale(1.1)}.caption-section[_ngcontent-%COMP%]{padding:16px 20px;background:#000c;position:relative}.caption-input[_ngcontent-%COMP%]{width:100%;min-height:60px;background:#ffffff1a;border:1px solid rgba(255,255,255,.2);border-radius:12px;padding:12px;color:#fff;font-size:.9rem;resize:none;outline:none}.caption-input[_ngcontent-%COMP%]::placeholder{color:#fff9}.char-count[_ngcontent-%COMP%]{position:absolute;bottom:20px;right:24px;font-size:.8rem;color:#fff9}.product-modal[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000c;z-index:1100;display:flex;align-items:center;justify-content:center;padding:20px}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;max-width:500px;width:100%;max-height:80vh;overflow:hidden;display:flex;flex-direction:column}.modal-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;border-bottom:1px solid #eee}.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600}.btn-close[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;cursor:pointer;padding:4px}.product-search[_ngcontent-%COMP%]{padding:16px 20px;border-bottom:1px solid #eee}.search-input[_ngcontent-%COMP%]{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:8px;font-size:.9rem;outline:none}.search-input[_ngcontent-%COMP%]:focus{border-color:#007bff}.products-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:16px 20px}.product-item[_ngcontent-%COMP%]{display:flex;gap:12px;padding:12px;border-radius:8px;cursor:pointer;transition:background .2s ease}.product-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.product-image[_ngcontent-%COMP%]{width:48px;height:48px;object-fit:cover;border-radius:6px}.product-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.product-name[_ngcontent-%COMP%]{font-weight:500;font-size:.9rem}.product-price[_ngcontent-%COMP%]{color:#007bff;font-weight:600;font-size:.9rem}.loading-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000c;z-index:1200;display:flex;align-items:center;justify-content:center}.loading-content[_ngcontent-%COMP%]{text-align:center;color:#fff}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid rgba(255,255,255,.3);border-top:3px solid #fff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 16px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.create-header[_ngcontent-%COMP%]{padding:12px 16px}.create-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.1rem}.selection-options[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr);gap:16px}.option-card[_ngcontent-%COMP%]{padding:24px 16px}.option-card[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.8rem}.story-tools[_ngcontent-%COMP%]{gap:12px;padding:12px}.tool-btn[_ngcontent-%COMP%]{width:44px;height:44px;font-size:1.1rem}.caption-section[_ngcontent-%COMP%]{padding:12px 16px}}@media (max-width: 480px){.selection-options[_ngcontent-%COMP%]{grid-template-columns:1fr;max-width:200px}.option-card[_ngcontent-%COMP%]{padding:20px}.modal-content[_ngcontent-%COMP%]{margin:10px;max-width:calc(100vw - 20px)}}\"]\n      });\n    }\n  }\n  return StoryCreateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}