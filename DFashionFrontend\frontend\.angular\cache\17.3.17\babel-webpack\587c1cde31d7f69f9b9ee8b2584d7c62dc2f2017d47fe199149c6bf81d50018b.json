{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/search.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = [\"fileInput\"];\nfunction VisualSearchComponent_ion_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 3);\n    i0.ɵɵlistener(\"click\", function VisualSearchComponent_ion_button_8_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.scanBarcode());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 11);\n    i0.ɵɵtext(2, \" Scan Code \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isProcessing);\n  }\n}\nfunction VisualSearchComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"ion-spinner\", 13);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.processingMessage);\n  }\n}\nfunction VisualSearchComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"img\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16)(3, \"ion-button\", 17);\n    i0.ɵɵlistener(\"click\", function VisualSearchComponent_div_12_Template_ion_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearPreview());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ion-button\", 19);\n    i0.ɵɵlistener(\"click\", function VisualSearchComponent_div_12_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchWithImage());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 20);\n    i0.ɵɵtext(7, \" Search \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.previewImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction VisualSearchComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nexport let VisualSearchComponent = /*#__PURE__*/(() => {\n  class VisualSearchComponent {\n    constructor(searchService) {\n      this.searchService = searchService;\n      this.searchResults = new EventEmitter();\n      this.searchError = new EventEmitter();\n      this.isProcessing = false;\n      this.processingMessage = '';\n      this.previewImage = null;\n      this.selectedFile = null;\n      this.errorMessage = '';\n      this.supportsBarcodeScanner = false;\n      this.checkBarcodeSupport();\n    }\n    checkBarcodeSupport() {\n      // Check if device supports barcode scanning\n      this.supportsBarcodeScanner = 'BarcodeDetector' in window || navigator.mediaDevices?.getUserMedia !== undefined;\n    }\n    openCamera() {\n      if (this.fileInput) {\n        this.fileInput.nativeElement.setAttribute('capture', 'environment');\n        this.fileInput.nativeElement.click();\n      }\n    }\n    selectImage() {\n      if (this.fileInput) {\n        this.fileInput.nativeElement.removeAttribute('capture');\n        this.fileInput.nativeElement.click();\n      }\n    }\n    onFileSelected(event) {\n      const file = event.target.files?.[0];\n      if (!file) return;\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        this.showError('Please select a valid image file');\n        return;\n      }\n      // Validate file size (max 10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        this.showError('Image size must be less than 10MB');\n        return;\n      }\n      this.selectedFile = file;\n      this.createPreview(file);\n      this.clearError();\n    }\n    createPreview(file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.previewImage = e.target?.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    searchWithImage() {\n      if (!this.selectedFile) return;\n      this.isProcessing = true;\n      this.processingMessage = 'Analyzing image...';\n      this.clearError();\n      this.searchService.searchByImage(this.selectedFile).subscribe({\n        next: result => {\n          this.isProcessing = false;\n          this.searchResults.emit(result);\n          this.clearPreview();\n        },\n        error: error => {\n          this.isProcessing = false;\n          const errorMsg = 'Visual search failed. Please try again.';\n          this.showError(errorMsg);\n          this.searchError.emit(errorMsg);\n        }\n      });\n    }\n    scanBarcode() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!_this.supportsBarcodeScanner) {\n          _this.showError('Barcode scanning is not supported on this device');\n          return;\n        }\n        try {\n          _this.isProcessing = true;\n          _this.processingMessage = 'Starting barcode scanner...';\n          // Use modern Barcode Detection API if available\n          if ('BarcodeDetector' in window) {\n            yield _this.scanWithBarcodeDetector();\n          } else {\n            // Fallback to camera-based scanning\n            yield _this.scanWithCamera();\n          }\n        } catch (error) {\n          console.error('Barcode scanning error:', error);\n          _this.showError('Barcode scanning failed. Please try again.');\n        } finally {\n          _this.isProcessing = false;\n        }\n      })();\n    }\n    scanWithBarcodeDetector() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        // Implementation would use BarcodeDetector API\n        // This is a placeholder for the actual implementation\n        _this2.showError('Barcode scanning feature coming soon!');\n      })();\n    }\n    scanWithCamera() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        // Implementation would use camera stream for barcode detection\n        // This is a placeholder for the actual implementation\n        _this3.showError('Camera-based barcode scanning feature coming soon!');\n      })();\n    }\n    clearPreview() {\n      this.previewImage = null;\n      this.selectedFile = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = '';\n      }\n    }\n    showError(message) {\n      this.errorMessage = message;\n      setTimeout(() => this.clearError(), 5000);\n    }\n    clearError() {\n      this.errorMessage = '';\n    }\n    static {\n      this.ɵfac = function VisualSearchComponent_Factory(t) {\n        return new (t || VisualSearchComponent)(i0.ɵɵdirectiveInject(i1.SearchService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VisualSearchComponent,\n        selectors: [[\"app-visual-search\"]],\n        viewQuery: function VisualSearchComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        outputs: {\n          searchResults: \"searchResults\",\n          searchError: \"searchError\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 14,\n        vars: 6,\n        consts: [[\"fileInput\", \"\"], [1, \"visual-search-container\"], [1, \"search-options\"], [\"fill\", \"outline\", \"size\", \"small\", 3, \"click\", \"disabled\"], [\"name\", \"camera\", \"slot\", \"start\"], [\"name\", \"image\", \"slot\", \"start\"], [\"fill\", \"outline\", \"size\", \"small\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/*\", \"capture\", \"environment\", 2, \"display\", \"none\", 3, \"change\"], [\"class\", \"processing-indicator\", 4, \"ngIf\"], [\"class\", \"image-preview\", 4, \"ngIf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"name\", \"qr-code\", \"slot\", \"start\"], [1, \"processing-indicator\"], [\"name\", \"crescent\"], [1, \"image-preview\"], [\"alt\", \"Search preview\", 1, \"preview-img\", 3, \"src\"], [1, \"preview-actions\"], [\"size\", \"small\", \"fill\", \"clear\", 3, \"click\"], [\"name\", \"close\", \"slot\", \"icon-only\"], [\"size\", \"small\", 3, \"click\"], [\"name\", \"search\", \"slot\", \"start\"], [1, \"error-message\"], [\"name\", \"alert-circle\", \"color\", \"danger\"]],\n        template: function VisualSearchComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"ion-button\", 3);\n            i0.ɵɵlistener(\"click\", function VisualSearchComponent_Template_ion_button_click_2_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.openCamera());\n            });\n            i0.ɵɵelement(3, \"ion-icon\", 4);\n            i0.ɵɵtext(4, \" Take Photo \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"ion-button\", 3);\n            i0.ɵɵlistener(\"click\", function VisualSearchComponent_Template_ion_button_click_5_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectImage());\n            });\n            i0.ɵɵelement(6, \"ion-icon\", 5);\n            i0.ɵɵtext(7, \" Upload Image \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, VisualSearchComponent_ion_button_8_Template, 3, 1, \"ion-button\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"input\", 7, 0);\n            i0.ɵɵlistener(\"change\", function VisualSearchComponent_Template_input_change_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelected($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(11, VisualSearchComponent_div_11_Template, 4, 1, \"div\", 8)(12, VisualSearchComponent_div_12_Template, 8, 1, \"div\", 9)(13, VisualSearchComponent_div_13_Template, 4, 1, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isProcessing);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.isProcessing);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.supportsBarcodeScanner);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isProcessing);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.previewImage && !ctx.isProcessing);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, IonicModule, i3.IonButton, i3.IonIcon, i3.IonSpinner],\n        styles: [\".visual-search-container[_ngcontent-%COMP%]{padding:1rem;border-radius:12px;background:var(--ion-color-light);border:2px dashed var(--ion-color-medium);text-align:center;transition:all .3s ease}.visual-search-container[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-primary);background:var(--ion-color-primary-tint)}.search-options[_ngcontent-%COMP%]{display:flex;gap:.5rem;justify-content:center;flex-wrap:wrap;margin-bottom:1rem}.search-options[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 20px;font-size:.875rem}.processing-indicator[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1rem;padding:2rem}.processing-indicator[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.875rem}.image-preview[_ngcontent-%COMP%]{position:relative;max-width:300px;margin:0 auto;border-radius:8px;overflow:hidden;box-shadow:0 4px 12px #0000001a}.preview-img[_ngcontent-%COMP%]{width:100%;height:auto;max-height:200px;object-fit:cover}.preview-actions[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;display:flex;gap:.5rem}.preview-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .9);--color: var(--ion-color-dark);--border-radius: 50%;width:36px;height:36px}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:1rem;background:var(--ion-color-danger-tint);border-radius:8px;margin-top:1rem}.error-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--ion-color-danger);font-size:.875rem}@media (max-width: 768px){.search-options[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.search-options[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:100%;max-width:200px}}\"]\n      });\n    }\n  }\n  return VisualSearchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}