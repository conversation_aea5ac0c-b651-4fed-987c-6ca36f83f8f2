{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AnalyticsService = /*#__PURE__*/(() => {\n  class AnalyticsService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl;\n      this.analyticsData$ = new BehaviorSubject(null);\n    }\n    // Core Analytics\n    getAnalyticsOverview() {\n      return this.http.get(`${this.apiUrl}/analytics/overview`).pipe(map(response => response.success ? response.data : this.getFallbackAnalytics()), catchError(error => {\n        console.error('Error fetching analytics:', error);\n        return [this.getFallbackAnalytics()];\n      }));\n    }\n    // Social Media Analytics\n    getSocialMediaMetrics() {\n      return this.http.get(`${this.apiUrl}/analytics/social-media`).pipe(map(response => response.success ? response.data : this.getFallbackSocialMetrics()), catchError(error => {\n        console.error('Error fetching social media metrics:', error);\n        return [this.getFallbackSocialMetrics()];\n      }));\n    }\n    // Search Engine Analytics\n    getSearchEngineData() {\n      return this.http.get(`${this.apiUrl}/analytics/search-engine`).pipe(map(response => response.success ? response.data : this.getFallbackSearchData()), catchError(error => {\n        console.error('Error fetching search engine data:', error);\n        return [this.getFallbackSearchData()];\n      }));\n    }\n    // Competitor Analysis\n    getCompetitorAnalysis() {\n      return this.http.get(`${this.apiUrl}/analytics/competitors`).pipe(map(response => response.success ? response.data : this.getFallbackCompetitorData()), catchError(error => {\n        console.error('Error fetching competitor analysis:', error);\n        return [this.getFallbackCompetitorData()];\n      }));\n    }\n    // Real-time Data Scraping\n    scrapeInstagramData(username) {\n      return this.http.post(`${this.apiUrl}/analytics/scrape/instagram`, {\n        username\n      }).pipe(map(response => response.success ? response.data : null), catchError(error => {\n        console.error('Error scraping Instagram data:', error);\n        return [null];\n      }));\n    }\n    scrapeGoogleTrends(keyword) {\n      return this.http.post(`${this.apiUrl}/analytics/scrape/google-trends`, {\n        keyword\n      }).pipe(map(response => response.success ? response.data : null), catchError(error => {\n        console.error('Error scraping Google Trends:', error);\n        return [null];\n      }));\n    }\n    // User Behavior Analytics\n    trackUserBehavior(event, data) {\n      return this.http.post(`${this.apiUrl}/analytics/track`, {\n        event,\n        data,\n        timestamp: new Date()\n      }).pipe(catchError(error => {\n        console.error('Error tracking user behavior:', error);\n        return [];\n      }));\n    }\n    // Export Analytics Data\n    exportAnalyticsData(format, dateRange) {\n      return this.http.post(`${this.apiUrl}/analytics/export`, {\n        format,\n        dateRange\n      }, {\n        responseType: 'blob'\n      }).pipe(catchError(error => {\n        console.error('Error exporting analytics data:', error);\n        throw error;\n      }));\n    }\n    // Fallback Data Methods\n    getFallbackAnalytics() {\n      return {\n        totalUsers: 15847,\n        activeUsers: 3421,\n        totalProducts: 1250,\n        totalOrders: 2847,\n        totalRevenue: 1247500,\n        conversionRate: 3.2,\n        averageOrderValue: 438,\n        topCategories: [{\n          name: 'Women',\n          count: 1247,\n          revenue: 547200\n        }, {\n          name: 'Men',\n          count: 892,\n          revenue: 389400\n        }, {\n          name: 'Accessories',\n          count: 634,\n          revenue: 278100\n        }, {\n          name: 'Footwear',\n          count: 521,\n          revenue: 228700\n        }, {\n          name: 'Beauty',\n          count: 387,\n          revenue: 169800\n        }],\n        userGrowth: this.generateGrowthData(),\n        searchTrends: [{\n          query: 'summer dress',\n          count: 1247,\n          trend: 'up'\n        }, {\n          query: 'casual wear',\n          count: 892,\n          trend: 'stable'\n        }, {\n          query: 'ethnic wear',\n          count: 634,\n          trend: 'up'\n        }, {\n          query: 'formal shoes',\n          count: 521,\n          trend: 'down'\n        }, {\n          query: 'handbags',\n          count: 387,\n          trend: 'up'\n        }],\n        engagementMetrics: {\n          pageViews: 45672,\n          sessionDuration: 4.2,\n          bounceRate: 32.5,\n          clickThroughRate: 2.8\n        }\n      };\n    }\n    getFallbackSocialMetrics() {\n      return [{\n        platform: 'Instagram',\n        followers: 125000,\n        engagement: 8.5,\n        reach: 89000,\n        impressions: 234000,\n        mentions: 1247,\n        sentiment: 'positive',\n        topPosts: [{\n          id: '1',\n          content: 'Summer collection launch',\n          likes: 2847,\n          shares: 234,\n          comments: 156\n        }, {\n          id: '2',\n          content: 'Behind the scenes',\n          likes: 1923,\n          shares: 189,\n          comments: 98\n        }, {\n          id: '3',\n          content: 'Customer spotlight',\n          likes: 1654,\n          shares: 145,\n          comments: 87\n        }]\n      }, {\n        platform: 'Facebook',\n        followers: 89000,\n        engagement: 6.2,\n        reach: 67000,\n        impressions: 178000,\n        mentions: 892,\n        sentiment: 'positive',\n        topPosts: [{\n          id: '1',\n          content: 'New arrivals showcase',\n          likes: 1847,\n          shares: 167,\n          comments: 123\n        }, {\n          id: '2',\n          content: 'Style tips and tricks',\n          likes: 1234,\n          shares: 134,\n          comments: 89\n        }, {\n          id: '3',\n          content: 'Flash sale announcement',\n          likes: 987,\n          shares: 98,\n          comments: 67\n        }]\n      }, {\n        platform: 'Twitter',\n        followers: 45000,\n        engagement: 4.8,\n        reach: 34000,\n        impressions: 89000,\n        mentions: 567,\n        sentiment: 'neutral',\n        topPosts: [{\n          id: '1',\n          content: 'Fashion week highlights',\n          likes: 892,\n          shares: 234,\n          comments: 67\n        }, {\n          id: '2',\n          content: 'Sustainable fashion tips',\n          likes: 634,\n          shares: 156,\n          comments: 45\n        }, {\n          id: '3',\n          content: 'Trend predictions',\n          likes: 521,\n          shares: 123,\n          comments: 34\n        }]\n      }];\n    }\n    getFallbackSearchData() {\n      return {\n        keywords: [{\n          keyword: 'online fashion store',\n          position: 3,\n          searchVolume: 12000,\n          difficulty: 65,\n          trend: 'up'\n        }, {\n          keyword: 'women clothing',\n          position: 7,\n          searchVolume: 8900,\n          difficulty: 72,\n          trend: 'stable'\n        }, {\n          keyword: 'ethnic wear online',\n          position: 5,\n          searchVolume: 5600,\n          difficulty: 58,\n          trend: 'up'\n        }, {\n          keyword: 'designer clothes',\n          position: 12,\n          searchVolume: 4300,\n          difficulty: 78,\n          trend: 'down'\n        }, {\n          keyword: 'fashion accessories',\n          position: 8,\n          searchVolume: 3200,\n          difficulty: 62,\n          trend: 'stable'\n        }],\n        organicTraffic: 23456,\n        clickThroughRate: 3.2,\n        averagePosition: 7.2,\n        impressions: 156789,\n        clicks: 5023\n      };\n    }\n    getFallbackCompetitorData() {\n      return [{\n        competitor: 'Myntra',\n        marketShare: 28.5,\n        priceComparison: 105,\n        trafficEstimate: 2500000,\n        topKeywords: ['online fashion', 'ethnic wear', 'designer clothes'],\n        socialFollowing: 1200000,\n        engagementRate: 6.8\n      }, {\n        competitor: 'Ajio',\n        marketShare: 18.2,\n        priceComparison: 98,\n        trafficEstimate: 1800000,\n        topKeywords: ['trendy fashion', 'casual wear', 'footwear'],\n        socialFollowing: 890000,\n        engagementRate: 5.4\n      }, {\n        competitor: 'Nykaa Fashion',\n        marketShare: 12.7,\n        priceComparison: 112,\n        trafficEstimate: 1200000,\n        topKeywords: ['beauty fashion', 'luxury brands', 'accessories'],\n        socialFollowing: 650000,\n        engagementRate: 7.2\n      }];\n    }\n    generateGrowthData() {\n      const data = [];\n      const today = new Date();\n      for (let i = 29; i >= 0; i--) {\n        const date = new Date(today);\n        date.setDate(date.getDate() - i);\n        data.push({\n          date: date.toISOString().split('T')[0],\n          users: Math.floor(Math.random() * 200) + 100,\n          orders: Math.floor(Math.random() * 50) + 20,\n          revenue: Math.floor(Math.random() * 10000) + 5000\n        });\n      }\n      return data;\n    }\n    // Update analytics data locally\n    updateAnalyticsData(data) {\n      this.analyticsData$.next(data);\n    }\n    // Get current analytics data\n    getCurrentAnalyticsData() {\n      return this.analyticsData$.asObservable();\n    }\n    static {\n      this.ɵfac = function AnalyticsService_Factory(t) {\n        return new (t || AnalyticsService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AnalyticsService,\n        factory: AnalyticsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AnalyticsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}