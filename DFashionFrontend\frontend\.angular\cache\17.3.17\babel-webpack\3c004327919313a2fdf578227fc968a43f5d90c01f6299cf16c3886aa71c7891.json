{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { startFocusVisible } from './focus-visible-dd40d69f.js';\nimport { r as raf, d as renderHiddenInput, g as getElementRoot, l as clamp } from './helpers-be245865.js';\nimport { a as printIonError, p as printIonWarning } from './index-9b0d46f4.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { c as chevronBack, o as chevronForward, l as chevronDown, p as caretUpSharp, q as caretDownSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { g as generateDayAriaLabel, a as getDay, i as isBefore, b as isAfter, c as isSameDay, d as getPreviousMonth, e as getNextMonth, v as validateParts, f as getPartsFromCalendarDay, h as getNextYear, j as getPreviousYear, k as getEndOfWeek, l as getStartOfWeek, m as getPreviousDay, n as getNextDay, o as getPreviousWeek, p as getNextWeek, q as parseMinParts, r as parseMaxParts, s as parseDate, w as warnIfValueOutOfBounds, t as convertToArrayOfNumbers, u as convertDataToISO, x as getToday, y as getClosestValidDate, z as generateMonths, A as getNumDaysInMonth, B as getCombinedDateColumnData, C as getMonthColumnData, D as getDayColumnData, E as getYearColumnData, F as isMonthFirstLocale, G as getTimeColumnsData, H as isLocaleDayPeriodRTL, I as getDaysOfWeek, J as getMonthAndYear, K as getDaysOfMonth, L as getHourCycle, M as getLocalizedTime, N as getLocalizedDateTime, O as formatValue, P as clampDate, Q as parseAmPm, R as calculateHourFromAMPM } from './data-bb424ba8.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall } from './overlays-b874c3c3.js';\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport { b as hapticSelectionChanged, h as hapticSelectionEnd, a as hapticSelectionStart } from './haptic-554688a5.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\nimport './capacitor-59395cbd.js';\nconst isYearDisabled = (refYear, minParts, maxParts) => {\n  if (minParts && minParts.year > refYear) {\n    return true;\n  }\n  if (maxParts && maxParts.year < refYear) {\n    return true;\n  }\n  return false;\n};\n/**\n * Returns true if a given day should\n * not be interactive according to its value,\n * or the max/min dates.\n */\nconst isDayDisabled = (refParts, minParts, maxParts, dayValues) => {\n  /**\n   * If this is a filler date (i.e. padding)\n   * then the date is disabled.\n   */\n  if (refParts.day === null) {\n    return true;\n  }\n  /**\n   * If user passed in a list of acceptable day values\n   * check to make sure that the date we are looking\n   * at is in this array.\n   */\n  if (dayValues !== undefined && !dayValues.includes(refParts.day)) {\n    return true;\n  }\n  /**\n   * Given a min date, perform the following\n   * checks. If any of them are true, then the\n   * day should be disabled:\n   * 1. Is the current year < the min allowed year?\n   * 2. Is the current year === min allowed year,\n   * but the current month < the min allowed month?\n   * 3. Is the current year === min allowed year, the\n   * current month === min allow month, but the current\n   * day < the min allowed day?\n   */\n  if (minParts && isBefore(refParts, minParts)) {\n    return true;\n  }\n  /**\n   * Given a max date, perform the following\n   * checks. If any of them are true, then the\n   * day should be disabled:\n   * 1. Is the current year > the max allowed year?\n   * 2. Is the current year === max allowed year,\n   * but the current month > the max allowed month?\n   * 3. Is the current year === max allowed year, the\n   * current month === max allow month, but the current\n   * day > the max allowed day?\n   */\n  if (maxParts && isAfter(refParts, maxParts)) {\n    return true;\n  }\n  /**\n   * If none of these checks\n   * passed then the date should\n   * be interactive.\n   */\n  return false;\n};\n/**\n * Given a locale, a date, the selected date(s), and today's date,\n * generate the state for a given calendar day button.\n */\nconst getCalendarDayState = (locale, refParts, activeParts, todayParts, minParts, maxParts, dayValues) => {\n  /**\n   * activeParts signals what day(s) are currently selected in the datetime.\n   * If multiple=\"true\", this will be an array, but the logic in this util\n   * is the same whether we have one selected day or many because we're only\n   * calculating the state for one button. So, we treat a single activeParts value\n   * the same as an array of length one.\n   */\n  const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n  /**\n   * The day button is active if it is selected, or in other words, if refParts\n   * matches at least one selected date.\n   */\n  const isActive = activePartsArray.find(parts => isSameDay(refParts, parts)) !== undefined;\n  const isToday = isSameDay(refParts, todayParts);\n  const disabled = isDayDisabled(refParts, minParts, maxParts, dayValues);\n  /**\n   * Note that we always return one object regardless of whether activeParts\n   * was an array, since we pare down to one value for isActive.\n   */\n  return {\n    disabled,\n    isActive,\n    isToday,\n    ariaSelected: isActive ? 'true' : null,\n    ariaLabel: generateDayAriaLabel(locale, isToday, refParts),\n    text: refParts.day != null ? getDay(locale, refParts) : null\n  };\n};\n/**\n * Returns `true` if the month is disabled given the\n * current date value and min/max date constraints.\n */\nconst isMonthDisabled = (refParts, {\n  minParts,\n  maxParts\n}) => {\n  // If the year is disabled then the month is disabled.\n  if (isYearDisabled(refParts.year, minParts, maxParts)) {\n    return true;\n  }\n  // If the date value is before the min date, then the month is disabled.\n  // If the date value is after the max date, then the month is disabled.\n  if (minParts && isBefore(refParts, minParts) || maxParts && isAfter(refParts, maxParts)) {\n    return true;\n  }\n  return false;\n};\n/**\n * Given a working date, an optional minimum date range,\n * and an optional maximum date range; determine if the\n * previous navigation button is disabled.\n */\nconst isPrevMonthDisabled = (refParts, minParts, maxParts) => {\n  const prevMonth = Object.assign(Object.assign({}, getPreviousMonth(refParts)), {\n    day: null\n  });\n  return isMonthDisabled(prevMonth, {\n    minParts,\n    maxParts\n  });\n};\n/**\n * Given a working date and a maximum date range,\n * determine if the next navigation button is disabled.\n */\nconst isNextMonthDisabled = (refParts, maxParts) => {\n  const nextMonth = Object.assign(Object.assign({}, getNextMonth(refParts)), {\n    day: null\n  });\n  return isMonthDisabled(nextMonth, {\n    maxParts\n  });\n};\n/**\n * Given the value of the highlightedDates property\n * and an ISO string, return the styles to use for\n * that date, or undefined if none are found.\n */\nconst getHighlightStyles = (highlightedDates, dateIsoString, el) => {\n  if (Array.isArray(highlightedDates)) {\n    const dateStringWithoutTime = dateIsoString.split('T')[0];\n    const matchingHighlight = highlightedDates.find(hd => hd.date === dateStringWithoutTime);\n    if (matchingHighlight) {\n      return {\n        textColor: matchingHighlight.textColor,\n        backgroundColor: matchingHighlight.backgroundColor\n      };\n    }\n  } else {\n    /**\n     * Wrap in a try-catch to prevent exceptions in the user's function\n     * from interrupting the calendar's rendering.\n     */\n    try {\n      return highlightedDates(dateIsoString);\n    } catch (e) {\n      printIonError('Exception thrown from provided `highlightedDates` callback. Please check your function and try again.', el, e);\n    }\n  }\n  return undefined;\n};\n\n/**\n * If a time zone is provided in the format options, the rendered text could\n * differ from what was selected in the Datetime, which could cause\n * confusion.\n */\nconst warnIfTimeZoneProvided = (el, formatOptions) => {\n  var _a, _b, _c, _d;\n  if (((_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _a === void 0 ? void 0 : _a.timeZone) || ((_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _b === void 0 ? void 0 : _b.timeZoneName) || ((_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _c === void 0 ? void 0 : _c.timeZone) || ((_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _d === void 0 ? void 0 : _d.timeZoneName)) {\n    printIonWarning('Datetime: \"timeZone\" and \"timeZoneName\" are not supported in \"formatOptions\".', el);\n  }\n};\nconst checkForPresentationFormatMismatch = (el, presentation, formatOptions) => {\n  // formatOptions is not required\n  if (!formatOptions) return;\n  // If formatOptions is provided, the date and/or time objects are required, depending on the presentation\n  switch (presentation) {\n    case 'date':\n    case 'month-year':\n    case 'month':\n    case 'year':\n      if (formatOptions.date === undefined) {\n        printIonWarning(`Datetime: The '${presentation}' presentation requires a date object in formatOptions.`, el);\n      }\n      break;\n    case 'time':\n      if (formatOptions.time === undefined) {\n        printIonWarning(`Datetime: The 'time' presentation requires a time object in formatOptions.`, el);\n      }\n      break;\n    case 'date-time':\n    case 'time-date':\n      if (formatOptions.date === undefined && formatOptions.time === undefined) {\n        printIonWarning(`Datetime: The '${presentation}' presentation requires either a date or time object (or both) in formatOptions.`, el);\n      }\n      break;\n  }\n};\nconst datetimeIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-light, #ffffff);--background-rgb:var(--ion-color-light-rgb);--title-color:var(--ion-color-step-600, #666666)}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, #cccccc);font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}:host .calendar-action-buttons ion-item{--padding-start:16px;--background-hover:transparent;--background-activated:transparent;font-size:min(1rem, 25.6px);font-weight:600}:host .calendar-action-buttons ion-item ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, #b3b3b3);font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, #cccccc)}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}\";\nconst IonDatetimeIosStyle0 = datetimeIosCss;\nconst datetimeMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-step-100, #ffffff);--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .datetime-calendar .calendar-action-buttons ion-item{--padding-start:20px}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, #595959)}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, gray);font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, #595959)}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}\";\nconst IonDatetimeMdStyle0 = datetimeMdCss;\nconst Datetime = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.ionRender = createEvent(this, \"ionRender\", 7);\n    this.inputId = `ion-dt-${datetimeIds++}`;\n    this.prevPresentation = null;\n    this.warnIfIncorrectValueUsage = () => {\n      const {\n        multiple,\n        value\n      } = this;\n      if (!multiple && Array.isArray(value)) {\n        /**\n         * We do some processing on the `value` array so\n         * that it looks more like an array when logged to\n         * the console.\n         * Example given ['a', 'b']\n         * Default toString() behavior: a,b\n         * Custom behavior: ['a', 'b']\n         */\n        printIonWarning(`ion-datetime was passed an array of values, but multiple=\"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map(v => `'${v}'`).join(', ')}]\n`, this.el);\n      }\n    };\n    this.setValue = value => {\n      this.value = value;\n      this.ionChange.emit({\n        value\n      });\n    };\n    /**\n     * Returns the DatetimePart interface\n     * to use when rendering an initial set of\n     * data. This should be used when rendering an\n     * interface in an environment where the `value`\n     * may not be set. This function works\n     * by returning the first selected date and then\n     * falling back to defaultParts if no active date\n     * is selected.\n     */\n    this.getActivePartsWithFallback = () => {\n      var _a;\n      const {\n        defaultParts\n      } = this;\n      return (_a = this.getActivePart()) !== null && _a !== void 0 ? _a : defaultParts;\n    };\n    this.getActivePart = () => {\n      const {\n        activeParts\n      } = this;\n      return Array.isArray(activeParts) ? activeParts[0] : activeParts;\n    };\n    this.closeParentOverlay = () => {\n      const popoverOrModal = this.el.closest('ion-modal, ion-popover');\n      if (popoverOrModal) {\n        popoverOrModal.dismiss();\n      }\n    };\n    this.setWorkingParts = parts => {\n      this.workingParts = Object.assign({}, parts);\n    };\n    this.setActiveParts = (parts, removeDate = false) => {\n      /** if the datetime component is in readonly mode,\n       * allow browsing of the calendar without changing\n       * the set value\n       */\n      if (this.readonly) {\n        return;\n      }\n      const {\n        multiple,\n        minParts,\n        maxParts,\n        activeParts\n      } = this;\n      /**\n       * When setting the active parts, it is possible\n       * to set invalid data. For example,\n       * when updating January 31 to February,\n       * February 31 does not exist. As a result\n       * we need to validate the active parts and\n       * ensure that we are only setting valid dates.\n       * Additionally, we need to update the working parts\n       * too in the event that the validated parts are different.\n       */\n      const validatedParts = validateParts(parts, minParts, maxParts);\n      this.setWorkingParts(validatedParts);\n      if (multiple) {\n        const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n        if (removeDate) {\n          this.activeParts = activePartsArray.filter(p => !isSameDay(p, validatedParts));\n        } else {\n          this.activeParts = [...activePartsArray, validatedParts];\n        }\n      } else {\n        this.activeParts = Object.assign({}, validatedParts);\n      }\n      const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n      if (hasSlottedButtons || this.showDefaultButtons) {\n        return;\n      }\n      this.confirm();\n    };\n    this.initializeKeyboardListeners = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const root = this.el.shadowRoot;\n      /**\n       * Get a reference to the month\n       * element we are currently viewing.\n       */\n      const currentMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(2)');\n      /**\n       * When focusing the calendar body, we want to pass focus\n       * to the working day, but other days should\n       * only be accessible using the arrow keys. Pressing\n       * Tab should jump between bodies of selectable content.\n       */\n      const checkCalendarBodyFocus = ev => {\n        var _a;\n        const record = ev[0];\n        /**\n         * If calendar body was already focused\n         * when this fired or if the calendar body\n         * if not currently focused, we should not re-focus\n         * the inner day.\n         */\n        if (((_a = record.oldValue) === null || _a === void 0 ? void 0 : _a.includes('ion-focused')) || !calendarBodyRef.classList.contains('ion-focused')) {\n          return;\n        }\n        this.focusWorkingDay(currentMonth);\n      };\n      const mo = new MutationObserver(checkCalendarBodyFocus);\n      mo.observe(calendarBodyRef, {\n        attributeFilter: ['class'],\n        attributeOldValue: true\n      });\n      this.destroyKeyboardMO = () => {\n        mo === null || mo === void 0 ? void 0 : mo.disconnect();\n      };\n      /**\n       * We must use keydown not keyup as we want\n       * to prevent scrolling when using the arrow keys.\n       */\n      calendarBodyRef.addEventListener('keydown', ev => {\n        const activeElement = root.activeElement;\n        if (!activeElement || !activeElement.classList.contains('calendar-day')) {\n          return;\n        }\n        const parts = getPartsFromCalendarDay(activeElement);\n        let partsToFocus;\n        switch (ev.key) {\n          case 'ArrowDown':\n            ev.preventDefault();\n            partsToFocus = getNextWeek(parts);\n            break;\n          case 'ArrowUp':\n            ev.preventDefault();\n            partsToFocus = getPreviousWeek(parts);\n            break;\n          case 'ArrowRight':\n            ev.preventDefault();\n            partsToFocus = getNextDay(parts);\n            break;\n          case 'ArrowLeft':\n            ev.preventDefault();\n            partsToFocus = getPreviousDay(parts);\n            break;\n          case 'Home':\n            ev.preventDefault();\n            partsToFocus = getStartOfWeek(parts);\n            break;\n          case 'End':\n            ev.preventDefault();\n            partsToFocus = getEndOfWeek(parts);\n            break;\n          case 'PageUp':\n            ev.preventDefault();\n            partsToFocus = ev.shiftKey ? getPreviousYear(parts) : getPreviousMonth(parts);\n            break;\n          case 'PageDown':\n            ev.preventDefault();\n            partsToFocus = ev.shiftKey ? getNextYear(parts) : getNextMonth(parts);\n            break;\n          /**\n           * Do not preventDefault here\n           * as we do not want to override other\n           * browser defaults such as pressing Enter/Space\n           * to select a day.\n           */\n          default:\n            return;\n        }\n        /**\n         * If the day we want to move focus to is\n         * disabled, do not do anything.\n         */\n        if (isDayDisabled(partsToFocus, this.minParts, this.maxParts)) {\n          return;\n        }\n        this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), partsToFocus));\n        /**\n         * Give view a chance to re-render\n         * then move focus to the new working day\n         */\n        requestAnimationFrame(() => this.focusWorkingDay(currentMonth));\n      });\n    };\n    this.focusWorkingDay = currentMonth => {\n      /**\n       * Get the number of padding days so\n       * we know how much to offset our next selector by\n       * to grab the correct calendar-day element.\n       */\n      const padding = currentMonth.querySelectorAll('.calendar-day-padding');\n      const {\n        day\n      } = this.workingParts;\n      if (day === null) {\n        return;\n      }\n      /**\n       * Get the calendar day element\n       * and focus it.\n       */\n      const dayEl = currentMonth.querySelector(`.calendar-day-wrapper:nth-of-type(${padding.length + day}) .calendar-day`);\n      if (dayEl) {\n        dayEl.focus();\n      }\n    };\n    this.processMinParts = () => {\n      const {\n        min,\n        defaultParts\n      } = this;\n      if (min === undefined) {\n        this.minParts = undefined;\n        return;\n      }\n      this.minParts = parseMinParts(min, defaultParts);\n    };\n    this.processMaxParts = () => {\n      const {\n        max,\n        defaultParts\n      } = this;\n      if (max === undefined) {\n        this.maxParts = undefined;\n        return;\n      }\n      this.maxParts = parseMaxParts(max, defaultParts);\n    };\n    this.initializeCalendarListener = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      /**\n       * For performance reasons, we only render 3\n       * months at a time: The current month, the previous\n       * month, and the next month. We have a scroll listener\n       * on the calendar body to append/prepend new months.\n       *\n       * We can do this because Stencil is smart enough to not\n       * re-create the .calendar-month containers, but rather\n       * update the content within those containers.\n       *\n       * As an added bonus, WebKit has some troubles with\n       * scroll-snap-stop: always, so not rendering all of\n       * the months in a row allows us to mostly sidestep\n       * that issue.\n       */\n      const months = calendarBodyRef.querySelectorAll('.calendar-month');\n      const startMonth = months[0];\n      const workingMonth = months[1];\n      const endMonth = months[2];\n      const mode = getIonMode(this);\n      const needsiOSRubberBandFix = mode === 'ios' && typeof navigator !== 'undefined' && navigator.maxTouchPoints > 1;\n      /**\n       * Before setting up the scroll listener,\n       * scroll the middle month into view.\n       * scrollIntoView() will scroll entire page\n       * if element is not in viewport. Use scrollLeft instead.\n       */\n      writeTask(() => {\n        calendarBodyRef.scrollLeft = startMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n        const getChangedMonth = parts => {\n          const box = calendarBodyRef.getBoundingClientRect();\n          /**\n           * If the current scroll position is all the way to the left\n           * then we have scrolled to the previous month.\n           * Otherwise, assume that we have scrolled to the next\n           * month. We have a tolerance of 2px to account for\n           * sub pixel rendering.\n           *\n           * Check below the next line ensures that we did not\n           * swipe and abort (i.e. we swiped but we are still on the current month).\n           */\n          const month = calendarBodyRef.scrollLeft <= 2 ? startMonth : endMonth;\n          /**\n           * The edge of the month must be lined up with\n           * the edge of the calendar body in order for\n           * the component to update. Otherwise, it\n           * may be the case that the user has paused their\n           * swipe or the browser has not finished snapping yet.\n           * Rather than check if the x values are equal,\n           * we give it a tolerance of 2px to account for\n           * sub pixel rendering.\n           */\n          const monthBox = month.getBoundingClientRect();\n          if (Math.abs(monthBox.x - box.x) > 2) return;\n          /**\n           * If we're force-rendering a month, assume we've\n           * scrolled to that and return it.\n           *\n           * If forceRenderDate is ever used in a context where the\n           * forced month is not immediately auto-scrolled to, this\n           * should be updated to also check whether `month` has the\n           * same month and year as the forced date.\n           */\n          const {\n            forceRenderDate\n          } = this;\n          if (forceRenderDate !== undefined) {\n            return {\n              month: forceRenderDate.month,\n              year: forceRenderDate.year,\n              day: forceRenderDate.day\n            };\n          }\n          /**\n           * From here, we can determine if the start\n           * month or the end month was scrolled into view.\n           * If no month was changed, then we can return from\n           * the scroll callback early.\n           */\n          if (month === startMonth) {\n            return getPreviousMonth(parts);\n          } else if (month === endMonth) {\n            return getNextMonth(parts);\n          } else {\n            return;\n          }\n        };\n        const updateActiveMonth = () => {\n          if (needsiOSRubberBandFix) {\n            calendarBodyRef.style.removeProperty('pointer-events');\n            appliediOSRubberBandFix = false;\n          }\n          /**\n           * If the month did not change\n           * then we can return early.\n           */\n          const newDate = getChangedMonth(this.workingParts);\n          if (!newDate) return;\n          const {\n            month,\n            day,\n            year\n          } = newDate;\n          if (isMonthDisabled({\n            month,\n            year,\n            day: null\n          }, {\n            minParts: Object.assign(Object.assign({}, this.minParts), {\n              day: null\n            }),\n            maxParts: Object.assign(Object.assign({}, this.maxParts), {\n              day: null\n            })\n          })) {\n            return;\n          }\n          /**\n           * Prevent scrolling for other browsers\n           * to give the DOM time to update and the container\n           * time to properly snap.\n           */\n          calendarBodyRef.style.setProperty('overflow', 'hidden');\n          /**\n           * Use a writeTask here to ensure\n           * that the state is updated and the\n           * correct month is scrolled into view\n           * in the same frame. This is not\n           * typically a problem on newer devices\n           * but older/slower device may have a flicker\n           * if we did not do this.\n           */\n          writeTask(() => {\n            this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), {\n              month,\n              day: day,\n              year\n            }));\n            calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n            calendarBodyRef.style.removeProperty('overflow');\n            if (this.resolveForceDateScrolling) {\n              this.resolveForceDateScrolling();\n            }\n          });\n        };\n        /**\n         * When the container finishes scrolling we\n         * need to update the DOM with the selected month.\n         */\n        let scrollTimeout;\n        /**\n         * We do not want to attempt to set pointer-events\n         * multiple times within a single swipe gesture as\n         * that adds unnecessary work to the main thread.\n         */\n        let appliediOSRubberBandFix = false;\n        const scrollCallback = () => {\n          if (scrollTimeout) {\n            clearTimeout(scrollTimeout);\n          }\n          /**\n           * On iOS it is possible to quickly rubber band\n           * the scroll area before the scroll timeout has fired.\n           * This results in users reaching the end of the scrollable\n           * container before the DOM has updated.\n           * By setting `pointer-events: none` we can ensure that\n           * subsequent swipes do not happen while the container\n           * is snapping.\n           */\n          if (!appliediOSRubberBandFix && needsiOSRubberBandFix) {\n            calendarBodyRef.style.setProperty('pointer-events', 'none');\n            appliediOSRubberBandFix = true;\n          }\n          // Wait ~3 frames\n          scrollTimeout = setTimeout(updateActiveMonth, 50);\n        };\n        calendarBodyRef.addEventListener('scroll', scrollCallback);\n        this.destroyCalendarListener = () => {\n          calendarBodyRef.removeEventListener('scroll', scrollCallback);\n        };\n      });\n    };\n    /**\n     * Clean up all listeners except for the overlay\n     * listener. This is so that we can re-create the listeners\n     * if the datetime has been hidden/presented by a modal or popover.\n     */\n    this.destroyInteractionListeners = () => {\n      const {\n        destroyCalendarListener,\n        destroyKeyboardMO\n      } = this;\n      if (destroyCalendarListener !== undefined) {\n        destroyCalendarListener();\n      }\n      if (destroyKeyboardMO !== undefined) {\n        destroyKeyboardMO();\n      }\n    };\n    this.processValue = value => {\n      const hasValue = value !== null && value !== undefined && (!Array.isArray(value) || value.length > 0);\n      const valueToProcess = hasValue ? parseDate(value) : this.defaultParts;\n      const {\n        minParts,\n        maxParts,\n        workingParts,\n        el\n      } = this;\n      this.warnIfIncorrectValueUsage();\n      /**\n       * Return early if the value wasn't parsed correctly, such as\n       * if an improperly formatted date string was provided.\n       */\n      if (!valueToProcess) {\n        return;\n      }\n      /**\n       * Datetime should only warn of out of bounds values\n       * if set by the user. If the `value` is undefined,\n       * we will default to today's date which may be out\n       * of bounds. In this case, the warning makes it look\n       * like the developer did something wrong which is\n       * not true.\n       */\n      if (hasValue) {\n        warnIfValueOutOfBounds(valueToProcess, minParts, maxParts);\n      }\n      /**\n       * If there are multiple values, pick an arbitrary one to clamp to. This way,\n       * if the values are across months, we always show at least one of them. Note\n       * that the values don't necessarily have to be in order.\n       */\n      const singleValue = Array.isArray(valueToProcess) ? valueToProcess[0] : valueToProcess;\n      const targetValue = clampDate(singleValue, minParts, maxParts);\n      const {\n        month,\n        day,\n        year,\n        hour,\n        minute\n      } = targetValue;\n      const ampm = parseAmPm(hour);\n      /**\n       * Since `activeParts` indicates a value that\n       * been explicitly selected either by the\n       * user or the app, only update `activeParts`\n       * if the `value` property is set.\n       */\n      if (hasValue) {\n        if (Array.isArray(valueToProcess)) {\n          this.activeParts = [...valueToProcess];\n        } else {\n          this.activeParts = {\n            month,\n            day,\n            year,\n            hour,\n            minute,\n            ampm\n          };\n        }\n      } else {\n        /**\n         * Reset the active parts if the value is not set.\n         * This will clear the selected calendar day when\n         * performing a clear action or using the reset() method.\n         */\n        this.activeParts = [];\n      }\n      /**\n       * Only animate if:\n       * 1. We're using grid style (wheel style pickers should just jump to new value)\n       * 2. The month and/or year actually changed, and both are defined (otherwise there's nothing to animate to)\n       * 3. The calendar body is visible (prevents animation when in collapsed datetime-button, for example)\n       * 4. The month/year picker is not open (since you wouldn't see the animation anyway)\n       */\n      const didChangeMonth = month !== undefined && month !== workingParts.month || year !== undefined && year !== workingParts.year;\n      const bodyIsVisible = el.classList.contains('datetime-ready');\n      const {\n        isGridStyle,\n        showMonthAndYear\n      } = this;\n      let areAllSelectedDatesInSameMonth = true;\n      if (Array.isArray(valueToProcess)) {\n        const firstMonth = valueToProcess[0].month;\n        for (const date of valueToProcess) {\n          if (date.month !== firstMonth) {\n            areAllSelectedDatesInSameMonth = false;\n            break;\n          }\n        }\n      }\n      /**\n       * If there is more than one date selected\n       * and the dates aren't all in the same month,\n       * then we should neither animate to the date\n       * nor update the working parts because we do\n       * not know which date the user wants to view.\n       */\n      if (areAllSelectedDatesInSameMonth) {\n        if (isGridStyle && didChangeMonth && bodyIsVisible && !showMonthAndYear) {\n          this.animateToDate(targetValue);\n        } else {\n          /**\n           * We only need to do this if we didn't just animate to a new month,\n           * since that calls prevMonth/nextMonth which calls setWorkingParts for us.\n           */\n          this.setWorkingParts({\n            month,\n            day,\n            year,\n            hour,\n            minute,\n            ampm\n          });\n        }\n      }\n    };\n    this.animateToDate = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (targetValue) {\n        const {\n          workingParts\n        } = _this;\n        /**\n         * Tell other render functions that we need to force the\n         * target month to appear in place of the actual next/prev month.\n         * Because this is a State variable, a rerender will be triggered\n         * automatically, updating the rendered months.\n         */\n        _this.forceRenderDate = targetValue;\n        /**\n         * Flag that we've started scrolling to the forced date.\n         * The resolve function will be called by the datetime's\n         * scroll listener when it's done updating everything.\n         * This is a replacement for making prev/nextMonth async,\n         * since the logic we're waiting on is in a listener.\n         */\n        const forceDateScrollingPromise = new Promise(resolve => {\n          _this.resolveForceDateScrolling = resolve;\n        });\n        /**\n         * Animate smoothly to the forced month. This will also update\n         * workingParts and correct the surrounding months for us.\n         */\n        const targetMonthIsBefore = isBefore(targetValue, workingParts);\n        targetMonthIsBefore ? _this.prevMonth() : _this.nextMonth();\n        yield forceDateScrollingPromise;\n        _this.resolveForceDateScrolling = undefined;\n        _this.forceRenderDate = undefined;\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.hasValue = () => {\n      return this.value != null;\n    };\n    this.nextMonth = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const nextMonth = calendarBodyRef.querySelector('.calendar-month:last-of-type');\n      if (!nextMonth) {\n        return;\n      }\n      const left = nextMonth.offsetWidth * 2;\n      calendarBodyRef.scrollTo({\n        top: 0,\n        left: left * (isRTL(this.el) ? -1 : 1),\n        behavior: 'smooth'\n      });\n    };\n    this.prevMonth = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const prevMonth = calendarBodyRef.querySelector('.calendar-month:first-of-type');\n      if (!prevMonth) {\n        return;\n      }\n      calendarBodyRef.scrollTo({\n        top: 0,\n        left: 0,\n        behavior: 'smooth'\n      });\n    };\n    this.toggleMonthAndYearView = () => {\n      this.showMonthAndYear = !this.showMonthAndYear;\n    };\n    this.showMonthAndYear = false;\n    this.activeParts = [];\n    this.workingParts = {\n      month: 5,\n      day: 28,\n      year: 2021,\n      hour: 13,\n      minute: 52,\n      ampm: 'pm'\n    };\n    this.isTimePopoverOpen = false;\n    this.forceRenderDate = undefined;\n    this.color = 'primary';\n    this.name = this.inputId;\n    this.disabled = false;\n    this.formatOptions = undefined;\n    this.readonly = false;\n    this.isDateEnabled = undefined;\n    this.min = undefined;\n    this.max = undefined;\n    this.presentation = 'date-time';\n    this.cancelText = 'Cancel';\n    this.doneText = 'Done';\n    this.clearText = 'Clear';\n    this.yearValues = undefined;\n    this.monthValues = undefined;\n    this.dayValues = undefined;\n    this.hourValues = undefined;\n    this.minuteValues = undefined;\n    this.locale = 'default';\n    this.firstDayOfWeek = 0;\n    this.titleSelectedDatesFormatter = undefined;\n    this.multiple = false;\n    this.highlightedDates = undefined;\n    this.value = undefined;\n    this.showDefaultTitle = false;\n    this.showDefaultButtons = false;\n    this.showClearButton = false;\n    this.showDefaultTimeLabel = true;\n    this.hourCycle = undefined;\n    this.size = 'fixed';\n    this.preferWheel = false;\n  }\n  formatOptionsChanged() {\n    const {\n      el,\n      formatOptions,\n      presentation\n    } = this;\n    checkForPresentationFormatMismatch(el, presentation, formatOptions);\n    warnIfTimeZoneProvided(el, formatOptions);\n  }\n  disabledChanged() {\n    this.emitStyle();\n  }\n  minChanged() {\n    this.processMinParts();\n  }\n  maxChanged() {\n    this.processMaxParts();\n  }\n  presentationChanged() {\n    const {\n      el,\n      formatOptions,\n      presentation\n    } = this;\n    checkForPresentationFormatMismatch(el, presentation, formatOptions);\n  }\n  get isGridStyle() {\n    const {\n      presentation,\n      preferWheel\n    } = this;\n    const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    return hasDatePresentation && !preferWheel;\n  }\n  yearValuesChanged() {\n    this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n  }\n  monthValuesChanged() {\n    this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n  }\n  dayValuesChanged() {\n    this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n  }\n  hourValuesChanged() {\n    this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n  }\n  minuteValuesChanged() {\n    this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n  }\n  /**\n   * Update the datetime value when the value changes\n   */\n  valueChanged() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        value\n      } = _this2;\n      if (_this2.hasValue()) {\n        _this2.processValue(value);\n      }\n      _this2.emitStyle();\n      _this2.ionValueChange.emit({\n        value\n      });\n    })();\n  }\n  /**\n   * Confirms the selected datetime value, updates the\n   * `value` property, and optionally closes the popover\n   * or modal that the datetime was presented in.\n   */\n  confirm() {\n    var _this3 = this;\n    return _asyncToGenerator(function* (closeOverlay = false) {\n      const {\n        isCalendarPicker,\n        activeParts,\n        preferWheel,\n        workingParts\n      } = _this3;\n      /**\n       * We only update the value if the presentation is not a calendar picker.\n       */\n      if (activeParts !== undefined || !isCalendarPicker) {\n        const activePartsIsArray = Array.isArray(activeParts);\n        if (activePartsIsArray && activeParts.length === 0) {\n          if (preferWheel) {\n            /**\n             * If the datetime is using a wheel picker, but the\n             * active parts are empty, then the user has confirmed the\n             * initial value (working parts) presented to them.\n             */\n            _this3.setValue(convertDataToISO(workingParts));\n          } else {\n            _this3.setValue(undefined);\n          }\n        } else {\n          _this3.setValue(convertDataToISO(activeParts));\n        }\n      }\n      if (closeOverlay) {\n        _this3.closeParentOverlay();\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Resets the internal state of the datetime but does not update the value.\n   * Passing a valid ISO-8601 string will reset the state of the component to the provided date.\n   * If no value is provided, the internal state will be reset to the clamped value of the min, max and today.\n   */\n  reset(startDate) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.processValue(startDate);\n    })();\n  }\n  /**\n   * Emits the ionCancel event and\n   * optionally closes the popover\n   * or modal that the datetime was\n   * presented in.\n   */\n  cancel() {\n    var _this5 = this;\n    return _asyncToGenerator(function* (closeOverlay = false) {\n      _this5.ionCancel.emit();\n      if (closeOverlay) {\n        _this5.closeParentOverlay();\n      }\n    }).apply(this, arguments);\n  }\n  get isCalendarPicker() {\n    const {\n      presentation\n    } = this;\n    return presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n  }\n  connectedCallback() {\n    this.clearFocusVisible = startFocusVisible(this.el).destroy;\n  }\n  disconnectedCallback() {\n    if (this.clearFocusVisible) {\n      this.clearFocusVisible();\n      this.clearFocusVisible = undefined;\n    }\n  }\n  initializeListeners() {\n    this.initializeCalendarListener();\n    this.initializeKeyboardListeners();\n  }\n  componentDidLoad() {\n    const {\n      el,\n      intersectionTrackerRef\n    } = this;\n    /**\n     * If a scrollable element is hidden using `display: none`,\n     * it will not have a scroll height meaning we cannot scroll elements\n     * into view. As a result, we will need to wait for the datetime to become\n     * visible if used inside of a modal or a popover otherwise the scrollable\n     * areas will not have the correct values snapped into place.\n     */\n    const visibleCallback = entries => {\n      const ev = entries[0];\n      if (!ev.isIntersecting) {\n        return;\n      }\n      this.initializeListeners();\n      /**\n       * TODO FW-2793: Datetime needs a frame to ensure that it\n       * can properly scroll contents into view. As a result\n       * we hide the scrollable content until after that frame\n       * so users do not see the content quickly shifting. The downside\n       * is that the content will pop into view a frame after. Maybe there\n       * is a better way to handle this?\n       */\n      writeTask(() => {\n        this.el.classList.add('datetime-ready');\n      });\n    };\n    const visibleIO = new IntersectionObserver(visibleCallback, {\n      threshold: 0.01,\n      root: el\n    });\n    /**\n     * Use raf to avoid a race condition between the component loading and\n     * its display animation starting (such as when shown in a modal). This\n     * could cause the datetime to start at a visibility of 0, erroneously\n     * triggering the `hiddenIO` observer below.\n     */\n    raf(() => visibleIO === null || visibleIO === void 0 ? void 0 : visibleIO.observe(intersectionTrackerRef));\n    /**\n     * We need to clean up listeners when the datetime is hidden\n     * in a popover/modal so that we can properly scroll containers\n     * back into view if they are re-presented. When the datetime is hidden\n     * the scroll areas have scroll widths/heights of 0px, so any snapping\n     * we did originally has been lost.\n     */\n    const hiddenCallback = entries => {\n      const ev = entries[0];\n      if (ev.isIntersecting) {\n        return;\n      }\n      this.destroyInteractionListeners();\n      /**\n       * When datetime is hidden, we need to make sure that\n       * the month/year picker is closed. Otherwise,\n       * it will be open when the datetime re-appears\n       * and the scroll area of the calendar grid will be 0.\n       * As a result, the wrong month will be shown.\n       */\n      this.showMonthAndYear = false;\n      writeTask(() => {\n        this.el.classList.remove('datetime-ready');\n      });\n    };\n    const hiddenIO = new IntersectionObserver(hiddenCallback, {\n      threshold: 0,\n      root: el\n    });\n    raf(() => hiddenIO === null || hiddenIO === void 0 ? void 0 : hiddenIO.observe(intersectionTrackerRef));\n    /**\n     * Datetime uses Ionic components that emit\n     * ionFocus and ionBlur. These events are\n     * composed meaning they will cross\n     * the shadow dom boundary. We need to\n     * stop propagation on these events otherwise\n     * developers will see 2 ionFocus or 2 ionBlur\n     * events at a time.\n     */\n    const root = getElementRoot(this.el);\n    root.addEventListener('ionFocus', ev => ev.stopPropagation());\n    root.addEventListener('ionBlur', ev => ev.stopPropagation());\n  }\n  /**\n   * When the presentation is changed, all calendar content is recreated,\n   * so we need to re-init behavior with the new elements.\n   */\n  componentDidRender() {\n    const {\n      presentation,\n      prevPresentation,\n      calendarBodyRef,\n      minParts,\n      preferWheel,\n      forceRenderDate\n    } = this;\n    /**\n     * TODO(FW-2165)\n     * Remove this when https://bugs.webkit.org/show_bug.cgi?id=235960 is fixed.\n     * When using `min`, we add `scroll-snap-align: none`\n     * to the disabled month so that users cannot scroll to it.\n     * This triggers a bug in WebKit where the scroll position is reset.\n     * Since the month change logic is handled by a scroll listener,\n     * this causes the month to change leading to `scroll-snap-align`\n     * changing again, thus changing the scroll position again and causing\n     * an infinite loop.\n     * This issue only applies to the calendar grid, so we can disable\n     * it if the calendar grid is not being used.\n     */\n    const hasCalendarGrid = !preferWheel && ['date-time', 'time-date', 'date'].includes(presentation);\n    if (minParts !== undefined && hasCalendarGrid && calendarBodyRef) {\n      const workingMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(1)');\n      /**\n       * We need to make sure the datetime is not in the process\n       * of scrolling to a new datetime value if the value\n       * is updated programmatically.\n       * Otherwise, the datetime will appear to not scroll at all because\n       * we are resetting the scroll position to the center of the view.\n       * Prior to the datetime's value being updated programmatically,\n       * the calendarBodyRef is scrolled such that the middle month is centered\n       * in the view. The below code updates the scroll position so the middle\n       * month is also centered in the view. Since the scroll position did not change,\n       * the scroll callback in this file does not fire,\n       * and the resolveForceDateScrolling promise never resolves.\n       */\n      if (workingMonth && forceRenderDate === undefined) {\n        calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n      }\n    }\n    if (prevPresentation === null) {\n      this.prevPresentation = presentation;\n      return;\n    }\n    if (presentation === prevPresentation) {\n      return;\n    }\n    this.prevPresentation = presentation;\n    this.destroyInteractionListeners();\n    this.initializeListeners();\n    /**\n     * The month/year picker from the date interface\n     * should be closed as it is not available in non-date\n     * interfaces.\n     */\n    this.showMonthAndYear = false;\n    raf(() => {\n      this.ionRender.emit();\n    });\n  }\n  componentWillLoad() {\n    const {\n      el,\n      formatOptions,\n      highlightedDates,\n      multiple,\n      presentation,\n      preferWheel\n    } = this;\n    if (multiple) {\n      if (presentation !== 'date') {\n        printIonWarning('Multiple date selection is only supported for presentation=\"date\".', el);\n      }\n      if (preferWheel) {\n        printIonWarning('Multiple date selection is not supported with preferWheel=\"true\".', el);\n      }\n    }\n    if (highlightedDates !== undefined) {\n      if (presentation !== 'date' && presentation !== 'date-time' && presentation !== 'time-date') {\n        printIonWarning('The highlightedDates property is only supported with the date, date-time, and time-date presentations.', el);\n      }\n      if (preferWheel) {\n        printIonWarning('The highlightedDates property is not supported with preferWheel=\"true\".', el);\n      }\n    }\n    if (formatOptions) {\n      checkForPresentationFormatMismatch(el, presentation, formatOptions);\n      warnIfTimeZoneProvided(el, formatOptions);\n    }\n    const hourValues = this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n    const minuteValues = this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n    const monthValues = this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n    const yearValues = this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n    const dayValues = this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n    const todayParts = this.todayParts = parseDate(getToday());\n    this.processMinParts();\n    this.processMaxParts();\n    this.defaultParts = getClosestValidDate({\n      refParts: todayParts,\n      monthValues,\n      dayValues,\n      yearValues,\n      hourValues,\n      minuteValues,\n      minParts: this.minParts,\n      maxParts: this.maxParts\n    });\n    this.processValue(this.value);\n    this.emitStyle();\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      interactive: true,\n      datetime: true,\n      'interactive-disabled': this.disabled\n    });\n  }\n  /**\n   * Universal render methods\n   * These are pieces of datetime that\n   * are rendered independently of presentation.\n   */\n  renderFooter() {\n    const {\n      disabled,\n      readonly,\n      showDefaultButtons,\n      showClearButton\n    } = this;\n    /**\n     * The cancel, clear, and confirm buttons\n     * should not be interactive if the datetime\n     * is disabled or readonly.\n     */\n    const isButtonDisabled = disabled || readonly;\n    const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n    if (!hasSlottedButtons && !showDefaultButtons && !showClearButton) {\n      return;\n    }\n    const clearButtonClick = () => {\n      this.reset();\n      this.setValue(undefined);\n    };\n    /**\n     * By default we render two buttons:\n     * Cancel - Dismisses the datetime and\n     * does not update the `value` prop.\n     * OK - Dismisses the datetime and\n     * updates the `value` prop.\n     */\n    return h(\"div\", {\n      class: \"datetime-footer\"\n    }, h(\"div\", {\n      class: \"datetime-buttons\"\n    }, h(\"div\", {\n      class: {\n        ['datetime-action-buttons']: true,\n        ['has-clear-button']: this.showClearButton\n      }\n    }, h(\"slot\", {\n      name: \"buttons\"\n    }, h(\"ion-buttons\", null, showDefaultButtons && h(\"ion-button\", {\n      id: \"cancel-button\",\n      color: this.color,\n      onClick: () => this.cancel(true),\n      disabled: isButtonDisabled\n    }, this.cancelText), h(\"div\", {\n      class: \"datetime-action-buttons-container\"\n    }, showClearButton && h(\"ion-button\", {\n      id: \"clear-button\",\n      color: this.color,\n      onClick: () => clearButtonClick(),\n      disabled: isButtonDisabled\n    }, this.clearText), showDefaultButtons && h(\"ion-button\", {\n      id: \"confirm-button\",\n      color: this.color,\n      onClick: () => this.confirm(true),\n      disabled: isButtonDisabled\n    }, this.doneText)))))));\n  }\n  /**\n   * Wheel picker render methods\n   */\n  renderWheelPicker(forcePresentation = this.presentation) {\n    /**\n     * If presentation=\"time-date\" we switch the\n     * order of the render array here instead of\n     * manually reordering each date/time picker\n     * column with CSS. This allows for additional\n     * flexibility if we need to render subsets\n     * of the date/time data or do additional ordering\n     * within the child render functions.\n     */\n    const renderArray = forcePresentation === 'time-date' ? [this.renderTimePickerColumns(forcePresentation), this.renderDatePickerColumns(forcePresentation)] : [this.renderDatePickerColumns(forcePresentation), this.renderTimePickerColumns(forcePresentation)];\n    return h(\"ion-picker-internal\", null, renderArray);\n  }\n  renderDatePickerColumns(forcePresentation) {\n    return forcePresentation === 'date-time' || forcePresentation === 'time-date' ? this.renderCombinedDatePickerColumn() : this.renderIndividualDatePickerColumns(forcePresentation);\n  }\n  renderCombinedDatePickerColumn() {\n    const {\n      defaultParts,\n      disabled,\n      workingParts,\n      locale,\n      minParts,\n      maxParts,\n      todayParts,\n      isDateEnabled\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    /**\n     * By default, generate a range of 3 months:\n     * Previous month, current month, and next month\n     */\n    const monthsToRender = generateMonths(workingParts);\n    const lastMonth = monthsToRender[monthsToRender.length - 1];\n    /**\n     * Ensure that users can select the entire window of dates.\n     */\n    monthsToRender[0].day = 1;\n    lastMonth.day = getNumDaysInMonth(lastMonth.month, lastMonth.year);\n    /**\n     * Narrow the dates rendered based on min/max dates (if any).\n     * The `min` date is used if the min is after the generated min month.\n     * The `max` date is used if the max is before the generated max month.\n     * This ensures that the sliding window always stays at 3 months\n     * but still allows future dates to be lazily rendered based on any min/max\n     * constraints.\n     */\n    const min = minParts !== undefined && isAfter(minParts, monthsToRender[0]) ? minParts : monthsToRender[0];\n    const max = maxParts !== undefined && isBefore(maxParts, lastMonth) ? maxParts : lastMonth;\n    const result = getCombinedDateColumnData(locale, todayParts, min, max, this.parsedDayValues, this.parsedMonthValues);\n    let items = result.items;\n    const parts = result.parts;\n    if (isDateEnabled) {\n      items = items.map((itemObject, index) => {\n        const referenceParts = parts[index];\n        let disabled;\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          disabled = !isDateEnabled(convertDataToISO(referenceParts));\n        } catch (e) {\n          printIonError('Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n        }\n        return Object.assign(Object.assign({}, itemObject), {\n          disabled\n        });\n      });\n    }\n    /**\n     * If we have selected a day already, then default the column\n     * to that value. Otherwise, set it to the default date.\n     */\n    const todayString = workingParts.day !== null ? `${workingParts.year}-${workingParts.month}-${workingParts.day}` : `${defaultParts.year}-${defaultParts.month}-${defaultParts.day}`;\n    return h(\"ion-picker-column-internal\", {\n      class: \"date-column\",\n      color: this.color,\n      disabled: disabled,\n      items: items,\n      value: todayString,\n      onIonChange: ev => {\n        // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n        // Due to a Safari 14 issue we need to destroy\n        // the scroll listener before we update state\n        // and trigger a re-render.\n        if (this.destroyCalendarListener) {\n          this.destroyCalendarListener();\n        }\n        const {\n          value\n        } = ev.detail;\n        const findPart = parts.find(({\n          month,\n          day,\n          year\n        }) => value === `${year}-${month}-${day}`);\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), findPart));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), findPart));\n        // We can re-attach the scroll listener after\n        // the working parts have been updated.\n        this.initializeCalendarListener();\n        ev.stopPropagation();\n      }\n    });\n  }\n  renderIndividualDatePickerColumns(forcePresentation) {\n    const {\n      workingParts,\n      isDateEnabled\n    } = this;\n    const shouldRenderMonths = forcePresentation !== 'year' && forcePresentation !== 'time';\n    const months = shouldRenderMonths ? getMonthColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedMonthValues) : [];\n    const shouldRenderDays = forcePresentation === 'date';\n    let days = shouldRenderDays ? getDayColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedDayValues) : [];\n    if (isDateEnabled) {\n      days = days.map(dayObject => {\n        const {\n          value\n        } = dayObject;\n        const valueNum = typeof value === 'string' ? parseInt(value) : value;\n        const referenceParts = {\n          month: workingParts.month,\n          day: valueNum,\n          year: workingParts.year\n        };\n        let disabled;\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          disabled = !isDateEnabled(convertDataToISO(referenceParts));\n        } catch (e) {\n          printIonError('Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n        }\n        return Object.assign(Object.assign({}, dayObject), {\n          disabled\n        });\n      });\n    }\n    const shouldRenderYears = forcePresentation !== 'month' && forcePresentation !== 'time';\n    const years = shouldRenderYears ? getYearColumnData(this.locale, this.defaultParts, this.minParts, this.maxParts, this.parsedYearValues) : [];\n    /**\n     * Certain locales show the day before the month.\n     */\n    const showMonthFirst = isMonthFirstLocale(this.locale, {\n      month: 'numeric',\n      day: 'numeric'\n    });\n    let renderArray = [];\n    if (showMonthFirst) {\n      renderArray = [this.renderMonthPickerColumn(months), this.renderDayPickerColumn(days), this.renderYearPickerColumn(years)];\n    } else {\n      renderArray = [this.renderDayPickerColumn(days), this.renderMonthPickerColumn(months), this.renderYearPickerColumn(years)];\n    }\n    return renderArray;\n  }\n  renderDayPickerColumn(days) {\n    var _a;\n    if (days.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column-internal\", {\n      class: \"day-column\",\n      color: this.color,\n      disabled: disabled,\n      items: days,\n      value: (_a = workingParts.day !== null ? workingParts.day : this.defaultParts.day) !== null && _a !== void 0 ? _a : undefined,\n      onIonChange: ev => {\n        // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n        // Due to a Safari 14 issue we need to destroy\n        // the scroll listener before we update state\n        // and trigger a re-render.\n        if (this.destroyCalendarListener) {\n          this.destroyCalendarListener();\n        }\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          day: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          day: ev.detail.value\n        }));\n        // We can re-attach the scroll listener after\n        // the working parts have been updated.\n        this.initializeCalendarListener();\n        ev.stopPropagation();\n      }\n    });\n  }\n  renderMonthPickerColumn(months) {\n    if (months.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column-internal\", {\n      class: \"month-column\",\n      color: this.color,\n      disabled: disabled,\n      items: months,\n      value: workingParts.month,\n      onIonChange: ev => {\n        // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n        // Due to a Safari 14 issue we need to destroy\n        // the scroll listener before we update state\n        // and trigger a re-render.\n        if (this.destroyCalendarListener) {\n          this.destroyCalendarListener();\n        }\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          month: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          month: ev.detail.value\n        }));\n        // We can re-attach the scroll listener after\n        // the working parts have been updated.\n        this.initializeCalendarListener();\n        ev.stopPropagation();\n      }\n    });\n  }\n  renderYearPickerColumn(years) {\n    if (years.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column-internal\", {\n      class: \"year-column\",\n      color: this.color,\n      disabled: disabled,\n      items: years,\n      value: workingParts.year,\n      onIonChange: ev => {\n        // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n        // Due to a Safari 14 issue we need to destroy\n        // the scroll listener before we update state\n        // and trigger a re-render.\n        if (this.destroyCalendarListener) {\n          this.destroyCalendarListener();\n        }\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          year: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          year: ev.detail.value\n        }));\n        // We can re-attach the scroll listener after\n        // the working parts have been updated.\n        this.initializeCalendarListener();\n        ev.stopPropagation();\n      }\n    });\n  }\n  renderTimePickerColumns(forcePresentation) {\n    if (['date', 'month', 'month-year', 'year'].includes(forcePresentation)) {\n      return [];\n    }\n    /**\n     * If a user has not selected a date,\n     * then we should show all times. If the\n     * user has selected a date (even if it has\n     * not been confirmed yet), we should apply\n     * the max and min restrictions so that the\n     * time picker shows values that are\n     * appropriate for the selected date.\n     */\n    const activePart = this.getActivePart();\n    const userHasSelectedDate = activePart !== undefined;\n    const {\n      hoursData,\n      minutesData,\n      dayPeriodData\n    } = getTimeColumnsData(this.locale, this.workingParts, this.hourCycle, userHasSelectedDate ? this.minParts : undefined, userHasSelectedDate ? this.maxParts : undefined, this.parsedHourValues, this.parsedMinuteValues);\n    return [this.renderHourPickerColumn(hoursData), this.renderMinutePickerColumn(minutesData), this.renderDayPeriodPickerColumn(dayPeriodData)];\n  }\n  renderHourPickerColumn(hoursData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (hoursData.length === 0) return [];\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column-internal\", {\n      color: this.color,\n      disabled: disabled,\n      value: activePart.hour,\n      items: hoursData,\n      numericInput: true,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          hour: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          hour: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    });\n  }\n  renderMinutePickerColumn(minutesData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (minutesData.length === 0) return [];\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column-internal\", {\n      color: this.color,\n      disabled: disabled,\n      value: activePart.minute,\n      items: minutesData,\n      numericInput: true,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          minute: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          minute: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    });\n  }\n  renderDayPeriodPickerColumn(dayPeriodData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (dayPeriodData.length === 0) {\n      return [];\n    }\n    const activePart = this.getActivePartsWithFallback();\n    const isDayPeriodRTL = isLocaleDayPeriodRTL(this.locale);\n    return h(\"ion-picker-column-internal\", {\n      style: isDayPeriodRTL ? {\n        order: '-1'\n      } : {},\n      color: this.color,\n      disabled: disabled,\n      value: activePart.ampm,\n      items: dayPeriodData,\n      onIonChange: ev => {\n        const hour = calculateHourFromAMPM(workingParts, ev.detail.value);\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          ampm: ev.detail.value,\n          hour\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          ampm: ev.detail.value,\n          hour\n        }));\n        ev.stopPropagation();\n      }\n    });\n  }\n  renderWheelView(forcePresentation) {\n    const {\n      locale\n    } = this;\n    const showMonthFirst = isMonthFirstLocale(locale);\n    const columnOrder = showMonthFirst ? 'month-first' : 'year-first';\n    return h(\"div\", {\n      class: {\n        [`wheel-order-${columnOrder}`]: true\n      }\n    }, this.renderWheelPicker(forcePresentation));\n  }\n  /**\n   * Grid Render Methods\n   */\n  renderCalendarHeader(mode) {\n    const {\n      disabled\n    } = this;\n    const expandedIcon = mode === 'ios' ? chevronDown : caretUpSharp;\n    const collapsedIcon = mode === 'ios' ? chevronForward : caretDownSharp;\n    const prevMonthDisabled = disabled || isPrevMonthDisabled(this.workingParts, this.minParts, this.maxParts);\n    const nextMonthDisabled = disabled || isNextMonthDisabled(this.workingParts, this.maxParts);\n    // don't use the inheritAttributes util because it removes dir from the host, and we still need that\n    const hostDir = this.el.getAttribute('dir') || undefined;\n    return h(\"div\", {\n      class: \"calendar-header\"\n    }, h(\"div\", {\n      class: \"calendar-action-buttons\"\n    }, h(\"div\", {\n      class: \"calendar-month-year\"\n    }, h(\"ion-item\", {\n      part: \"month-year-button\",\n      ref: el => this.monthYearToggleItemRef = el,\n      button: true,\n      \"aria-label\": \"Show year picker\",\n      detail: false,\n      lines: \"none\",\n      disabled: disabled,\n      onClick: () => {\n        var _a;\n        this.toggleMonthAndYearView();\n        /**\n         * TODO: FW-3547\n         *\n         * Currently there is not a way to set the aria-label on the inner button\n         * on the `ion-item` and have it be reactive to changes. This is a workaround\n         * until we either refactor `ion-item` to a button or Stencil adds a way to\n         * have reactive props for built-in properties, such as `aria-label`.\n         */\n        const {\n          monthYearToggleItemRef\n        } = this;\n        if (monthYearToggleItemRef) {\n          const btn = (_a = monthYearToggleItemRef.shadowRoot) === null || _a === void 0 ? void 0 : _a.querySelector('.item-native');\n          if (btn) {\n            const monthYearAriaLabel = this.showMonthAndYear ? 'Hide year picker' : 'Show year picker';\n            btn.setAttribute('aria-label', monthYearAriaLabel);\n          }\n        }\n      }\n    }, h(\"ion-label\", null, getMonthAndYear(this.locale, this.workingParts), h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: this.showMonthAndYear ? expandedIcon : collapsedIcon,\n      lazy: false,\n      flipRtl: true\n    })))), h(\"div\", {\n      class: \"calendar-next-prev\"\n    }, h(\"ion-buttons\", null, h(\"ion-button\", {\n      \"aria-label\": \"Previous month\",\n      disabled: prevMonthDisabled,\n      onClick: () => this.prevMonth()\n    }, h(\"ion-icon\", {\n      dir: hostDir,\n      \"aria-hidden\": \"true\",\n      slot: \"icon-only\",\n      icon: chevronBack,\n      lazy: false,\n      flipRtl: true\n    })), h(\"ion-button\", {\n      \"aria-label\": \"Next month\",\n      disabled: nextMonthDisabled,\n      onClick: () => this.nextMonth()\n    }, h(\"ion-icon\", {\n      dir: hostDir,\n      \"aria-hidden\": \"true\",\n      slot: \"icon-only\",\n      icon: chevronForward,\n      lazy: false,\n      flipRtl: true\n    }))))), h(\"div\", {\n      class: \"calendar-days-of-week\",\n      \"aria-hidden\": \"true\"\n    }, getDaysOfWeek(this.locale, mode, this.firstDayOfWeek % 7).map(d => {\n      return h(\"div\", {\n        class: \"day-of-week\"\n      }, d);\n    })));\n  }\n  renderMonth(month, year) {\n    const {\n      disabled,\n      readonly\n    } = this;\n    const yearAllowed = this.parsedYearValues === undefined || this.parsedYearValues.includes(year);\n    const monthAllowed = this.parsedMonthValues === undefined || this.parsedMonthValues.includes(month);\n    const isCalMonthDisabled = !yearAllowed || !monthAllowed;\n    const isDatetimeDisabled = disabled || readonly;\n    const swipeDisabled = disabled || isMonthDisabled({\n      month,\n      year,\n      day: null\n    }, {\n      // The day is not used when checking if a month is disabled.\n      // Users should be able to access the min or max month, even if the\n      // min/max date is out of bounds (e.g. min is set to Feb 15, Feb should not be disabled).\n      minParts: Object.assign(Object.assign({}, this.minParts), {\n        day: null\n      }),\n      maxParts: Object.assign(Object.assign({}, this.maxParts), {\n        day: null\n      })\n    });\n    // The working month should never have swipe disabled.\n    // Otherwise the CSS scroll snap will not work and the user\n    // can free-scroll the calendar.\n    const isWorkingMonth = this.workingParts.month === month && this.workingParts.year === year;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"div\", {\n      \"aria-hidden\": !isWorkingMonth ? 'true' : null,\n      class: {\n        'calendar-month': true,\n        // Prevents scroll snap swipe gestures for months outside of the min/max bounds\n        'calendar-month-disabled': !isWorkingMonth && swipeDisabled\n      }\n    }, h(\"div\", {\n      class: \"calendar-month-grid\"\n    }, getDaysOfMonth(month, year, this.firstDayOfWeek % 7).map((dateObject, index) => {\n      const {\n        day,\n        dayOfWeek\n      } = dateObject;\n      const {\n        el,\n        highlightedDates,\n        isDateEnabled,\n        multiple\n      } = this;\n      const referenceParts = {\n        month,\n        day,\n        year\n      };\n      const isCalendarPadding = day === null;\n      const {\n        isActive,\n        isToday,\n        ariaLabel,\n        ariaSelected,\n        disabled: isDayDisabled,\n        text\n      } = getCalendarDayState(this.locale, referenceParts, this.activeParts, this.todayParts, this.minParts, this.maxParts, this.parsedDayValues);\n      const dateIsoString = convertDataToISO(referenceParts);\n      let isCalDayDisabled = isCalMonthDisabled || isDayDisabled;\n      if (!isCalDayDisabled && isDateEnabled !== undefined) {\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          isCalDayDisabled = !isDateEnabled(dateIsoString);\n        } catch (e) {\n          printIonError('Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', el, e);\n        }\n      }\n      /**\n       * Some days are constrained through max & min or allowed dates\n       * and also disabled because the component is readonly or disabled.\n       * These need to be displayed differently.\n       */\n      const isCalDayConstrained = isCalDayDisabled && isDatetimeDisabled;\n      const isButtonDisabled = isCalDayDisabled || isDatetimeDisabled;\n      let dateStyle = undefined;\n      /**\n       * Custom highlight styles should not override the style for selected dates,\n       * nor apply to \"filler days\" at the start of the grid.\n       */\n      if (highlightedDates !== undefined && !isActive && day !== null) {\n        dateStyle = getHighlightStyles(highlightedDates, dateIsoString, el);\n      }\n      let dateParts = undefined;\n      // \"Filler days\" at the beginning of the grid should not get the calendar day\n      // CSS parts added to them\n      if (!isCalendarPadding) {\n        dateParts = `calendar-day${isActive ? ' active' : ''}${isToday ? ' today' : ''}${isCalDayDisabled ? ' disabled' : ''}`;\n      }\n      return h(\"div\", {\n        class: \"calendar-day-wrapper\"\n      }, h(\"button\", {\n        // We need to use !important for the inline styles here because\n        // otherwise the CSS shadow parts will override these styles.\n        // See https://github.com/WICG/webcomponents/issues/847\n        // Both the CSS shadow parts and highlightedDates styles are\n        // provided by the developer, but highlightedDates styles should\n        // always take priority.\n        ref: el => {\n          if (el) {\n            el.style.setProperty('color', `${dateStyle ? dateStyle.textColor : ''}`, 'important');\n            el.style.setProperty('background-color', `${dateStyle ? dateStyle.backgroundColor : ''}`, 'important');\n          }\n        },\n        tabindex: \"-1\",\n        \"data-day\": day,\n        \"data-month\": month,\n        \"data-year\": year,\n        \"data-index\": index,\n        \"data-day-of-week\": dayOfWeek,\n        disabled: isButtonDisabled,\n        class: {\n          'calendar-day-padding': isCalendarPadding,\n          'calendar-day': true,\n          'calendar-day-active': isActive,\n          'calendar-day-constrained': isCalDayConstrained,\n          'calendar-day-today': isToday\n        },\n        part: dateParts,\n        \"aria-hidden\": isCalendarPadding ? 'true' : null,\n        \"aria-selected\": ariaSelected,\n        \"aria-label\": ariaLabel,\n        onClick: () => {\n          if (isCalendarPadding) {\n            return;\n          }\n          this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), {\n            month,\n            day,\n            year\n          }));\n          // multiple only needs date info, so we can wipe out other fields like time\n          if (multiple) {\n            this.setActiveParts({\n              month,\n              day,\n              year\n            }, isActive);\n          } else {\n            this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n              month,\n              day,\n              year\n            }));\n          }\n        }\n      }, text));\n    })));\n  }\n  renderCalendarBody() {\n    return h(\"div\", {\n      class: \"calendar-body ion-focusable\",\n      ref: el => this.calendarBodyRef = el,\n      tabindex: \"0\"\n    }, generateMonths(this.workingParts, this.forceRenderDate).map(({\n      month,\n      year\n    }) => {\n      return this.renderMonth(month, year);\n    }));\n  }\n  renderCalendar(mode) {\n    return h(\"div\", {\n      class: \"datetime-calendar\",\n      key: \"datetime-calendar\"\n    }, this.renderCalendarHeader(mode), this.renderCalendarBody());\n  }\n  renderTimeLabel() {\n    const hasSlottedTimeLabel = this.el.querySelector('[slot=\"time-label\"]') !== null;\n    if (!hasSlottedTimeLabel && !this.showDefaultTimeLabel) {\n      return;\n    }\n    return h(\"slot\", {\n      name: \"time-label\"\n    }, \"Time\");\n  }\n  renderTimeOverlay() {\n    var _this6 = this;\n    const {\n      disabled,\n      hourCycle,\n      isTimePopoverOpen,\n      locale,\n      formatOptions\n    } = this;\n    const computedHourCycle = getHourCycle(locale, hourCycle);\n    const activePart = this.getActivePartsWithFallback();\n    return [h(\"div\", {\n      class: \"time-header\"\n    }, this.renderTimeLabel()), h(\"button\", {\n      class: {\n        'time-body': true,\n        'time-body-active': isTimePopoverOpen\n      },\n      part: `time-button${isTimePopoverOpen ? ' active' : ''}`,\n      \"aria-expanded\": \"false\",\n      \"aria-haspopup\": \"true\",\n      disabled: disabled,\n      onClick: function () {\n        var _ref2 = _asyncToGenerator(function* (ev) {\n          const {\n            popoverRef\n          } = _this6;\n          if (popoverRef) {\n            _this6.isTimePopoverOpen = true;\n            popoverRef.present(new CustomEvent('ionShadowTarget', {\n              detail: {\n                ionShadowTarget: ev.target\n              }\n            }));\n            yield popoverRef.onWillDismiss();\n            _this6.isTimePopoverOpen = false;\n          }\n        });\n        return function onClick(_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()\n    }, getLocalizedTime(locale, activePart, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time)), h(\"ion-popover\", {\n      alignment: \"center\",\n      translucent: true,\n      overlayIndex: 1,\n      arrow: false,\n      onWillPresent: ev => {\n        /**\n         * Intersection Observers do not consistently fire between Blink and Webkit\n         * when toggling the visibility of the popover and trying to scroll the picker\n         * column to the correct time value.\n         *\n         * This will correctly scroll the element position to the correct time value,\n         * before the popover is fully presented.\n         */\n        const cols = ev.target.querySelectorAll('ion-picker-column-internal');\n        // TODO (FW-615): Potentially remove this when intersection observers are fixed in picker column\n        cols.forEach(col => col.scrollActiveItemIntoView());\n      },\n      style: {\n        '--offset-y': '-10px',\n        '--min-width': 'fit-content'\n      },\n      // Allow native browser keyboard events to support up/down/home/<USER>\n      // navigation within the time picker.\n      keyboardEvents: true,\n      ref: el => this.popoverRef = el\n    }, this.renderWheelPicker('time'))];\n  }\n  getHeaderSelectedDateText() {\n    var _a;\n    const {\n      activeParts,\n      formatOptions,\n      multiple,\n      titleSelectedDatesFormatter\n    } = this;\n    const isArray = Array.isArray(activeParts);\n    let headerText;\n    if (multiple && isArray && activeParts.length !== 1) {\n      headerText = `${activeParts.length} days`; // default/fallback for multiple selection\n      if (titleSelectedDatesFormatter !== undefined) {\n        try {\n          headerText = titleSelectedDatesFormatter(convertDataToISO(activeParts));\n        } catch (e) {\n          printIonError('Exception in provided `titleSelectedDatesFormatter`: ', e);\n        }\n      }\n    } else {\n      // for exactly 1 day selected (multiple set or not), show a formatted version of that\n      headerText = getLocalizedDateTime(this.locale, this.getActivePartsWithFallback(), (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : {\n        weekday: 'short',\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n    return headerText;\n  }\n  renderHeader(showExpandedHeader = true) {\n    const hasSlottedTitle = this.el.querySelector('[slot=\"title\"]') !== null;\n    if (!hasSlottedTitle && !this.showDefaultTitle) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"datetime-header\"\n    }, h(\"div\", {\n      class: \"datetime-title\"\n    }, h(\"slot\", {\n      name: \"title\"\n    }, \"Select Date\")), showExpandedHeader && h(\"div\", {\n      class: \"datetime-selected-date\"\n    }, this.getHeaderSelectedDateText()));\n  }\n  /**\n   * Render time picker inside of datetime.\n   * Do not pass color prop to segment on\n   * iOS mode. MD segment has been customized and\n   * should take on the color prop, but iOS\n   * should just be the default segment.\n   */\n  renderTime() {\n    const {\n      presentation\n    } = this;\n    const timeOnlyPresentation = presentation === 'time';\n    return h(\"div\", {\n      class: \"datetime-time\"\n    }, timeOnlyPresentation ? this.renderWheelPicker() : this.renderTimeOverlay());\n  }\n  /**\n   * Renders the month/year picker that is\n   * displayed on the calendar grid.\n   * The .datetime-year class has additional\n   * styles that let us show/hide the\n   * picker when the user clicks on the\n   * toggle in the calendar header.\n   */\n  renderCalendarViewMonthYearPicker() {\n    return h(\"div\", {\n      class: \"datetime-year\"\n    }, this.renderWheelView('month-year'));\n  }\n  /**\n   * Render entry point\n   * All presentation types are rendered from here.\n   */\n  renderDatetime(mode) {\n    const {\n      presentation,\n      preferWheel\n    } = this;\n    /**\n     * Certain presentation types have separate grid and wheel displays.\n     * If preferWheel is true then we should show a wheel picker instead.\n     */\n    const hasWheelVariant = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    if (preferWheel && hasWheelVariant) {\n      return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n    }\n    switch (presentation) {\n      case 'date-time':\n        return [this.renderHeader(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderTime(), this.renderFooter()];\n      case 'time-date':\n        return [this.renderHeader(), this.renderTime(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderFooter()];\n      case 'time':\n        return [this.renderHeader(false), this.renderTime(), this.renderFooter()];\n      case 'month':\n      case 'month-year':\n      case 'year':\n        return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n      default:\n        return [this.renderHeader(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderFooter()];\n    }\n  }\n  render() {\n    const {\n      name,\n      value,\n      disabled,\n      el,\n      color,\n      readonly,\n      showMonthAndYear,\n      preferWheel,\n      presentation,\n      size,\n      isGridStyle\n    } = this;\n    const mode = getIonMode(this);\n    const isMonthAndYearPresentation = presentation === 'year' || presentation === 'month' || presentation === 'month-year';\n    const shouldShowMonthAndYear = showMonthAndYear || isMonthAndYearPresentation;\n    const monthYearPickerOpen = showMonthAndYear && !isMonthAndYearPresentation;\n    const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    const hasWheelVariant = hasDatePresentation && preferWheel;\n    renderHiddenInput(true, el, name, formatValue(value), disabled);\n    return h(Host, {\n      key: '8490192beb6c5c6064ed8f2a7be2d51846f84f36',\n      \"aria-disabled\": disabled ? 'true' : null,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      class: Object.assign({}, createColorClasses(color, {\n        [mode]: true,\n        ['datetime-readonly']: readonly,\n        ['datetime-disabled']: disabled,\n        'show-month-and-year': shouldShowMonthAndYear,\n        'month-year-picker-open': monthYearPickerOpen,\n        [`datetime-presentation-${presentation}`]: true,\n        [`datetime-size-${size}`]: true,\n        [`datetime-prefer-wheel`]: hasWheelVariant,\n        [`datetime-grid`]: isGridStyle\n      }))\n    }, h(\"div\", {\n      key: 'a2959c07ed871f9004a2f11ab1385a5a7b5737fd',\n      class: \"intersection-tracker\",\n      ref: el => this.intersectionTrackerRef = el\n    }), this.renderDatetime(mode));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"formatOptions\": [\"formatOptionsChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"min\": [\"minChanged\"],\n      \"max\": [\"maxChanged\"],\n      \"presentation\": [\"presentationChanged\"],\n      \"yearValues\": [\"yearValuesChanged\"],\n      \"monthValues\": [\"monthValuesChanged\"],\n      \"dayValues\": [\"dayValuesChanged\"],\n      \"hourValues\": [\"hourValuesChanged\"],\n      \"minuteValues\": [\"minuteValuesChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet datetimeIds = 0;\nDatetime.style = {\n  ios: IonDatetimeIosStyle0,\n  md: IonDatetimeMdStyle0\n};\n\n/**\n * iOS Picker Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.picker-wrapper')).fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Picker Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0.01);\n  wrapperAnimation.addElement(baseEl.querySelector('.picker-wrapper')).fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst pickerIosCss = \".sc-ion-picker-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-ios-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-ios-h{left:0}[dir=rtl].sc-ion-picker-ios-h,[dir=rtl] .sc-ion-picker-ios-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-ios-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-ios-h{display:none}.picker-wrapper.sc-ion-picker-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-ios:active,.picker-button.sc-ion-picker-ios:focus{outline:none}.picker-columns.sc-ion-picker-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-ios,.picker-below-highlight.sc-ion-picker-ios{display:none;pointer-events:none}.sc-ion-picker-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-ios:last-child .picker-button.sc-ion-picker-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-ios,.picker-button.ion-activated.sc-ion-picker-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:16px}.picker-columns.sc-ion-picker-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}\";\nconst IonPickerIosStyle0 = pickerIosCss;\nconst pickerMdCss = \".sc-ion-picker-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-md-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-md-h{left:0}[dir=rtl].sc-ion-picker-md-h,[dir=rtl] .sc-ion-picker-md-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-md-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-md-h{display:none}.picker-wrapper.sc-ion-picker-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-md:active,.picker-button.sc-ion-picker-md:focus{outline:none}.picker-columns.sc-ion-picker-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-md,.picker-below-highlight.sc-ion-picker-md{display:none;pointer-events:none}.sc-ion-picker-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-md,.picker-button.ion-activated.sc-ion-picker-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}\";\nconst IonPickerMdStyle0 = pickerMdCss;\nconst Picker = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionPickerDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionPickerWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionPickerWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionPickerDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.dispatchCancelHandler = ev => {\n      const role = ev.detail.role;\n      if (isCancel(role)) {\n        const cancelButton = this.buttons.find(b => b.role === 'cancel');\n        this.callButtonHandler(cancelButton);\n      }\n    };\n    this.presented = false;\n    this.overlayIndex = undefined;\n    this.delegate = undefined;\n    this.hasController = false;\n    this.keyboardClose = true;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.buttons = [];\n    this.columns = [];\n    this.cssClass = undefined;\n    this.duration = 0;\n    this.showBackdrop = true;\n    this.backdropDismiss = true;\n    this.animated = true;\n    this.htmlAttributes = undefined;\n    this.isOpen = false;\n    this.trigger = undefined;\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    setOverlayId(this.el);\n  }\n  componentDidLoad() {\n    /**\n     * If picker was rendered with isOpen=\"true\"\n     * then we should open picker immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  /**\n   * Present the picker overlay after it has been created.\n   */\n  present() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this7.lockController.lock();\n      yield _this7.delegateController.attachViewToDom();\n      yield present(_this7, 'pickerEnter', iosEnterAnimation, iosEnterAnimation, undefined);\n      if (_this7.duration > 0) {\n        _this7.durationTimeout = setTimeout(() => _this7.dismiss(), _this7.duration);\n      }\n      unlock();\n    })();\n  }\n  /**\n   * Dismiss the picker overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the picker.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the picker.\n   * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n   *\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   */\n  dismiss(data, role) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this8.lockController.lock();\n      if (_this8.durationTimeout) {\n        clearTimeout(_this8.durationTimeout);\n      }\n      const dismissed = yield dismiss(_this8, data, role, 'pickerLeave', iosLeaveAnimation, iosLeaveAnimation);\n      if (dismissed) {\n        _this8.delegateController.removeViewFromDom();\n      }\n      unlock();\n      return dismissed;\n    })();\n  }\n  /**\n   * Returns a promise that resolves when the picker did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionPickerDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the picker will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionPickerWillDismiss');\n  }\n  /**\n   * Get the column that matches the specified name.\n   *\n   * @param name The name of the column.\n   */\n  getColumn(name) {\n    return Promise.resolve(this.columns.find(column => column.name === name));\n  }\n  buttonClick(button) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      const role = button.role;\n      if (isCancel(role)) {\n        return _this9.dismiss(undefined, role);\n      }\n      const shouldDismiss = yield _this9.callButtonHandler(button);\n      if (shouldDismiss) {\n        return _this9.dismiss(_this9.getSelected(), button.role);\n      }\n      return Promise.resolve();\n    })();\n  }\n  callButtonHandler(button) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      if (button) {\n        // a handler has been provided, execute it\n        // pass the handler the values from the inputs\n        const rtn = yield safeCall(button.handler, _this0.getSelected());\n        if (rtn === false) {\n          // if the return value of the handler is false then do not dismiss\n          return false;\n        }\n      }\n      return true;\n    })();\n  }\n  getSelected() {\n    const selected = {};\n    this.columns.forEach((col, index) => {\n      const selectedColumn = col.selectedIndex !== undefined ? col.options[col.selectedIndex] : undefined;\n      selected[col.name] = {\n        text: selectedColumn ? selectedColumn.text : undefined,\n        value: selectedColumn ? selectedColumn.value : undefined,\n        columnIndex: index\n      };\n    });\n    return selected;\n  }\n  render() {\n    const {\n      htmlAttributes\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, Object.assign({\n      key: 'eb5f91ea74fb11daa6942f779ef461742cad9ecb',\n      \"aria-modal\": \"true\",\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${20000 + this.overlayIndex}`\n      },\n      class: Object.assign({\n        [mode]: true,\n        // Used internally for styling\n        [`picker-${mode}`]: true,\n        'overlay-hidden': true\n      }, getClassMap(this.cssClass)),\n      onIonBackdropTap: this.onBackdropTap,\n      onIonPickerWillDismiss: this.dispatchCancelHandler\n    }), h(\"ion-backdrop\", {\n      key: '7ea872d939e62f14129fff15334b2822ad2360c9',\n      visible: this.showBackdrop,\n      tappable: this.backdropDismiss\n    }), h(\"div\", {\n      key: '2d77c225091eacab0207e28c96b966122afafef0',\n      tabindex: \"0\"\n    }), h(\"div\", {\n      key: '630d21e0c60ad97b71462cdc540858bb6ced0b8f',\n      class: \"picker-wrapper ion-overlay-wrapper\",\n      role: \"dialog\"\n    }, h(\"div\", {\n      key: 'fa8553ec8d2ce8bf93e16e02334b6475cb51b5d4',\n      class: \"picker-toolbar\"\n    }, this.buttons.map(b => h(\"div\", {\n      class: buttonWrapperClass(b)\n    }, h(\"button\", {\n      type: \"button\",\n      onClick: () => this.buttonClick(b),\n      class: buttonClass(b)\n    }, b.text)))), h(\"div\", {\n      key: '177d1bcbd0ce38f16d9c936295a917fb981d02d7',\n      class: \"picker-columns\"\n    }, h(\"div\", {\n      key: 'be99b6e0279c210ef91a88ccc81acc7d37917a53',\n      class: \"picker-above-highlight\"\n    }), this.presented && this.columns.map(c => h(\"ion-picker-column\", {\n      col: c\n    })), h(\"div\", {\n      key: 'b36b21e8133b59e873e1d3447a1279f1b971c854',\n      class: \"picker-below-highlight\"\n    }))), h(\"div\", {\n      key: '17cea6dd24dbb0a08073ca4a84bfe027eb24833d',\n      tabindex: \"0\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nconst buttonWrapperClass = button => {\n  return {\n    [`picker-toolbar-${button.role}`]: button.role !== undefined,\n    'picker-toolbar-button': true\n  };\n};\nconst buttonClass = button => {\n  return Object.assign({\n    'picker-button': true,\n    'ion-activatable': true\n  }, getClassMap(button.cssClass));\n};\nPicker.style = {\n  ios: IonPickerIosStyle0,\n  md: IonPickerMdStyle0\n};\nconst pickerColumnIosCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}\";\nconst IonPickerColumnIosStyle0 = pickerColumnIosCss;\nconst pickerColumnMdCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #3880ff)}\";\nconst IonPickerColumnMdStyle0 = pickerColumnMdCss;\nconst PickerColumnCmp = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionPickerColChange = createEvent(this, \"ionPickerColChange\", 7);\n    this.optHeight = 0;\n    this.rotateFactor = 0;\n    this.scaleFactor = 1;\n    this.velocity = 0;\n    this.y = 0;\n    this.noAnimate = true;\n    // `colDidChange` is a flag that gets set when the column is changed\n    // dynamically. When this flag is set, the column will refresh\n    // after the component re-renders to incorporate the new column data.\n    // This is necessary because `this.refresh` queries for the option elements,\n    // so it needs to wait for the latest elements to be available in the DOM.\n    // Ex: column is created with 3 options. User updates the column data\n    // to have 5 options. The column will still think it only has 3 options.\n    this.colDidChange = false;\n    this.col = undefined;\n  }\n  colChanged() {\n    this.colDidChange = true;\n  }\n  connectedCallback() {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      let pickerRotateFactor = 0;\n      let pickerScaleFactor = 0.81;\n      const mode = getIonMode(_this1);\n      if (mode === 'ios') {\n        pickerRotateFactor = -0.46;\n        pickerScaleFactor = 1;\n      }\n      _this1.rotateFactor = pickerRotateFactor;\n      _this1.scaleFactor = pickerScaleFactor;\n      _this1.gesture = (yield import('./index-2cf77112.js')).createGesture({\n        el: _this1.el,\n        gestureName: 'picker-swipe',\n        gesturePriority: 100,\n        threshold: 0,\n        passive: false,\n        onStart: ev => _this1.onStart(ev),\n        onMove: ev => _this1.onMove(ev),\n        onEnd: ev => _this1.onEnd(ev)\n      });\n      _this1.gesture.enable();\n      // Options have not been initialized yet\n      // Animation must be disabled through the `noAnimate` flag\n      // Otherwise, the options will render\n      // at the top of the column and transition down\n      _this1.tmrId = setTimeout(() => {\n        _this1.noAnimate = false;\n        // After initialization, `refresh()` will be called\n        // At this point, animation will be enabled. The options will\n        // animate as they are being selected.\n        _this1.refresh(true);\n      }, 250);\n    })();\n  }\n  componentDidLoad() {\n    this.onDomChange();\n  }\n  componentDidUpdate() {\n    // Options may have changed since last update.\n    if (this.colDidChange) {\n      // Animation must be disabled through the `onDomChange` parameter.\n      // Otherwise, the recently added options will render\n      // at the top of the column and transition down\n      this.onDomChange(true, false);\n      this.colDidChange = false;\n    }\n  }\n  disconnectedCallback() {\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    if (this.tmrId) clearTimeout(this.tmrId);\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  emitColChange() {\n    this.ionPickerColChange.emit(this.col);\n  }\n  setSelected(selectedIndex, duration) {\n    // if there is a selected index, then figure out it's y position\n    // if there isn't a selected index, then just use the top y position\n    const y = selectedIndex > -1 ? -(selectedIndex * this.optHeight) : 0;\n    this.velocity = 0;\n    // set what y position we're at\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    this.update(y, duration, true);\n    this.emitColChange();\n  }\n  update(y, duration, saveY) {\n    if (!this.optsEl) {\n      return;\n    }\n    // ensure we've got a good round number :)\n    let translateY = 0;\n    let translateZ = 0;\n    const {\n      col,\n      rotateFactor\n    } = this;\n    const prevSelected = col.selectedIndex;\n    const selectedIndex = col.selectedIndex = this.indexForY(-y);\n    const durationStr = duration === 0 ? '' : duration + 'ms';\n    const scaleStr = `scale(${this.scaleFactor})`;\n    const children = this.optsEl.children;\n    for (let i = 0; i < children.length; i++) {\n      const button = children[i];\n      const opt = col.options[i];\n      const optOffset = i * this.optHeight + y;\n      let transform = '';\n      if (rotateFactor !== 0) {\n        const rotateX = optOffset * rotateFactor;\n        if (Math.abs(rotateX) <= 90) {\n          translateY = 0;\n          translateZ = 90;\n          transform = `rotateX(${rotateX}deg) `;\n        } else {\n          translateY = -9999;\n        }\n      } else {\n        translateZ = 0;\n        translateY = optOffset;\n      }\n      const selected = selectedIndex === i;\n      transform += `translate3d(0px,${translateY}px,${translateZ}px) `;\n      if (this.scaleFactor !== 1 && !selected) {\n        transform += scaleStr;\n      }\n      // Update transition duration\n      if (this.noAnimate) {\n        opt.duration = 0;\n        button.style.transitionDuration = '';\n      } else if (duration !== opt.duration) {\n        opt.duration = duration;\n        button.style.transitionDuration = durationStr;\n      }\n      // Update transform\n      if (transform !== opt.transform) {\n        opt.transform = transform;\n      }\n      button.style.transform = transform;\n      /**\n       * Ensure that the select column\n       * item has the selected class\n       */\n      opt.selected = selected;\n      if (selected) {\n        button.classList.add(PICKER_OPT_SELECTED);\n      } else {\n        button.classList.remove(PICKER_OPT_SELECTED);\n      }\n    }\n    this.col.prevSelected = prevSelected;\n    if (saveY) {\n      this.y = y;\n    }\n    if (this.lastIndex !== selectedIndex) {\n      // have not set a last index yet\n      hapticSelectionChanged();\n      this.lastIndex = selectedIndex;\n    }\n  }\n  decelerate() {\n    if (this.velocity !== 0) {\n      // still decelerating\n      this.velocity *= DECELERATION_FRICTION;\n      // do not let it go slower than a velocity of 1\n      this.velocity = this.velocity > 0 ? Math.max(this.velocity, 1) : Math.min(this.velocity, -1);\n      let y = this.y + this.velocity;\n      if (y > this.minY) {\n        // whoops, it's trying to scroll up farther than the options we have!\n        y = this.minY;\n        this.velocity = 0;\n      } else if (y < this.maxY) {\n        // gahh, it's trying to scroll down farther than we can!\n        y = this.maxY;\n        this.velocity = 0;\n      }\n      this.update(y, 0, true);\n      const notLockedIn = Math.round(y) % this.optHeight !== 0 || Math.abs(this.velocity) > 1;\n      if (notLockedIn) {\n        // isn't locked in yet, keep decelerating until it is\n        this.rafId = requestAnimationFrame(() => this.decelerate());\n      } else {\n        this.velocity = 0;\n        this.emitColChange();\n        hapticSelectionEnd();\n      }\n    } else if (this.y % this.optHeight !== 0) {\n      // needs to still get locked into a position so options line up\n      const currentPos = Math.abs(this.y % this.optHeight);\n      // create a velocity in the direction it needs to scroll\n      this.velocity = currentPos > this.optHeight / 2 ? 1 : -1;\n      this.decelerate();\n    }\n  }\n  indexForY(y) {\n    return Math.min(Math.max(Math.abs(Math.round(y / this.optHeight)), 0), this.col.options.length - 1);\n  }\n  onStart(detail) {\n    // We have to prevent default in order to block scrolling under the picker\n    // but we DO NOT have to stop propagation, since we still want\n    // some \"click\" events to capture\n    if (detail.event.cancelable) {\n      detail.event.preventDefault();\n    }\n    detail.event.stopPropagation();\n    hapticSelectionStart();\n    // reset everything\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    const options = this.col.options;\n    let minY = options.length - 1;\n    let maxY = 0;\n    for (let i = 0; i < options.length; i++) {\n      if (!options[i].disabled) {\n        minY = Math.min(minY, i);\n        maxY = Math.max(maxY, i);\n      }\n    }\n    this.minY = -(minY * this.optHeight);\n    this.maxY = -(maxY * this.optHeight);\n  }\n  onMove(detail) {\n    if (detail.event.cancelable) {\n      detail.event.preventDefault();\n    }\n    detail.event.stopPropagation();\n    // update the scroll position relative to pointer start position\n    let y = this.y + detail.deltaY;\n    if (y > this.minY) {\n      // scrolling up higher than scroll area\n      y = Math.pow(y, 0.8);\n      this.bounceFrom = y;\n    } else if (y < this.maxY) {\n      // scrolling down below scroll area\n      y += Math.pow(this.maxY - y, 0.9);\n      this.bounceFrom = y;\n    } else {\n      this.bounceFrom = 0;\n    }\n    this.update(y, 0, false);\n  }\n  onEnd(detail) {\n    if (this.bounceFrom > 0) {\n      // bounce back up\n      this.update(this.minY, 100, true);\n      this.emitColChange();\n      return;\n    } else if (this.bounceFrom < 0) {\n      // bounce back down\n      this.update(this.maxY, 100, true);\n      this.emitColChange();\n      return;\n    }\n    this.velocity = clamp(-MAX_PICKER_SPEED, detail.velocityY * 23, MAX_PICKER_SPEED);\n    if (this.velocity === 0 && detail.deltaY === 0) {\n      const opt = detail.event.target.closest('.picker-opt');\n      if (opt === null || opt === void 0 ? void 0 : opt.hasAttribute('opt-index')) {\n        this.setSelected(parseInt(opt.getAttribute('opt-index'), 10), TRANSITION_DURATION);\n      }\n    } else {\n      this.y += detail.deltaY;\n      if (Math.abs(detail.velocityY) < 0.05) {\n        const isScrollingUp = detail.deltaY > 0;\n        const optHeightFraction = Math.abs(this.y) % this.optHeight / this.optHeight;\n        if (isScrollingUp && optHeightFraction > 0.5) {\n          this.velocity = Math.abs(this.velocity) * -1;\n        } else if (!isScrollingUp && optHeightFraction <= 0.5) {\n          this.velocity = Math.abs(this.velocity);\n        }\n      }\n      this.decelerate();\n    }\n  }\n  refresh(forceRefresh, animated) {\n    var _a;\n    let min = this.col.options.length - 1;\n    let max = 0;\n    const options = this.col.options;\n    for (let i = 0; i < options.length; i++) {\n      if (!options[i].disabled) {\n        min = Math.min(min, i);\n        max = Math.max(max, i);\n      }\n    }\n    /**\n     * Only update selected value if column has a\n     * velocity of 0. If it does not, then the\n     * column is animating might land on\n     * a value different than the value at\n     * selectedIndex\n     */\n    if (this.velocity !== 0) {\n      return;\n    }\n    const selectedIndex = clamp(min, (_a = this.col.selectedIndex) !== null && _a !== void 0 ? _a : 0, max);\n    if (this.col.prevSelected !== selectedIndex || forceRefresh) {\n      const y = selectedIndex * this.optHeight * -1;\n      const duration = animated ? TRANSITION_DURATION : 0;\n      this.velocity = 0;\n      this.update(y, duration, true);\n    }\n  }\n  onDomChange(forceRefresh, animated) {\n    const colEl = this.optsEl;\n    if (colEl) {\n      // DOM READ\n      // We perfom a DOM read over a rendered item, this needs to happen after the first render or after the the column has changed\n      this.optHeight = colEl.firstElementChild ? colEl.firstElementChild.clientHeight : 0;\n    }\n    this.refresh(forceRefresh, animated);\n  }\n  render() {\n    const col = this.col;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '49bb4c67a67c7318d4c305df78ceabae36355112',\n      class: Object.assign({\n        [mode]: true,\n        'picker-col': true,\n        'picker-opts-left': this.col.align === 'left',\n        'picker-opts-right': this.col.align === 'right'\n      }, getClassMap(col.cssClass)),\n      style: {\n        'max-width': this.col.columnWidth\n      }\n    }, col.prefix && h(\"div\", {\n      key: '7e65761d24473e4ba0ce2d4fc707a5c5e8127903',\n      class: \"picker-prefix\",\n      style: {\n        width: col.prefixWidth\n      }\n    }, col.prefix), h(\"div\", {\n      key: '65c3aea609401e8ae4ea6d363a1b9436796c0a86',\n      class: \"picker-opts\",\n      style: {\n        maxWidth: col.optionsWidth\n      },\n      ref: el => this.optsEl = el\n    }, col.options.map((o, index) => h(\"button\", {\n      \"aria-label\": o.ariaLabel,\n      class: {\n        'picker-opt': true,\n        'picker-opt-disabled': !!o.disabled\n      },\n      \"opt-index\": index\n    }, o.text))), col.suffix && h(\"div\", {\n      key: 'c2e5a324ba95dd8832d3eb81b139e1f674d74a35',\n      class: \"picker-suffix\",\n      style: {\n        width: col.suffixWidth\n      }\n    }, col.suffix));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"col\": [\"colChanged\"]\n    };\n  }\n};\nconst PICKER_OPT_SELECTED = 'picker-opt-selected';\nconst DECELERATION_FRICTION = 0.97;\nconst MAX_PICKER_SPEED = 90;\nconst TRANSITION_DURATION = 150;\nPickerColumnCmp.style = {\n  ios: IonPickerColumnIosStyle0,\n  md: IonPickerColumnMdStyle0\n};\nexport { Datetime as ion_datetime, Picker as ion_picker, PickerColumnCmp as ion_picker_column };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "h", "H", "Host", "f", "getElement", "startFocusVisible", "raf", "renderHiddenInput", "g", "getElementRoot", "l", "clamp", "a", "printIonError", "p", "printIonWarning", "i", "isRTL", "c", "createColorClasses", "getClassMap", "chevronBack", "o", "chevronForward", "chevronDown", "caretUpSharp", "q", "caretDownSharp", "b", "getIonMode", "generateDayAriaLabel", "getDay", "isBefore", "isAfter", "isSameDay", "getPrevious<PERSON><PERSON>h", "e", "getNextMonth", "v", "validateParts", "getPartsFromCalendarDay", "getNextYear", "j", "getPreviousYear", "k", "getEndOfWeek", "getStartOfWeek", "m", "getPreviousDay", "n", "getNextDay", "getPreviousWeek", "getNextWeek", "parseMinParts", "parseMaxParts", "s", "parseDate", "warnIfValueOutOfBounds", "t", "convertToArrayOfNumbers", "u", "convertDataToISO", "x", "get<PERSON><PERSON>y", "y", "getClosestValidDate", "z", "generateMonths", "A", "getNumDaysInMonth", "B", "getCombinedDateColumnData", "C", "getMonthColumnData", "D", "getDayColumnData", "E", "getYearColumnData", "F", "isMonthFirstLocale", "G", "getTimeColumnsData", "isLocaleDayPeriodRTL", "I", "getDaysOfWeek", "J", "getMonthAndYear", "K", "getDaysOfMonth", "L", "getHourCycle", "M", "getLocalizedTime", "N", "getLocalizedDateTime", "O", "formatValue", "P", "clampDate", "Q", "parseAmPm", "R", "calculateHourFromAMPM", "createLockController", "createDelegateController", "createTriggerController", "BACKDROP", "isCancel", "prepareOverlay", "setOverlayId", "present", "dismiss", "eventMethod", "safeCall", "createAnimation", "hapticSelectionChanged", "hapticSelectionEnd", "hapticSelectionStart", "isYearDisabled", "refYear", "minParts", "maxParts", "year", "isDayDisabled", "refParts", "dayV<PERSON><PERSON>", "day", "undefined", "includes", "getCalendarDayState", "locale", "activeParts", "todayParts", "activePartsArray", "Array", "isArray", "isActive", "find", "parts", "isToday", "disabled", "ariaSelected", "aria<PERSON><PERSON><PERSON>", "text", "isMonthDisabled", "isPrevMonthDisabled", "prevMonth", "Object", "assign", "isNextMonthDisabled", "nextMonth", "getHighlightStyles", "highlightedDates", "dateIsoString", "el", "dateStringWithoutTime", "split", "matching<PERSON>ighlight", "hd", "date", "textColor", "backgroundColor", "warnIfTimeZoneProvided", "formatOptions", "_a", "_b", "_c", "_d", "timeZone", "timeZoneName", "time", "checkForPresentationFormatMismatch", "presentation", "datetimeIosCss", "IonDatetimeIosStyle0", "datetimeMdCss", "IonDatetimeMdStyle0", "Datetime", "constructor", "hostRef", "_this", "ionCancel", "ionChange", "ionValueChange", "ionFocus", "ionBlur", "ionStyle", "ionRender", "inputId", "datetimeIds", "prevPresentation", "warnIfIncorrectValueUsage", "multiple", "value", "map", "join", "setValue", "emit", "getActivePartsWithFallback", "defaultParts", "getActivePart", "closeParentOverlay", "popoverOrModal", "closest", "setWorkingParts", "workingParts", "setActiveParts", "removeDate", "readonly", "validatedParts", "filter", "hasSlottedButtons", "querySelector", "showDefaultButtons", "confirm", "initializeKeyboardListeners", "calendarBodyRef", "root", "shadowRoot", "currentMonth", "checkCalendarBodyFocus", "ev", "record", "oldValue", "classList", "contains", "focusWorkingDay", "mo", "MutationObserver", "observe", "attributeFilter", "attributeOldValue", "destroyKeyboardMO", "disconnect", "addEventListener", "activeElement", "partsToFocus", "key", "preventDefault", "shift<PERSON>ey", "requestAnimationFrame", "padding", "querySelectorAll", "dayEl", "length", "focus", "processMinParts", "min", "processMaxParts", "max", "initializeCalendarListener", "months", "startMonth", "workingMonth", "endMonth", "mode", "needsiOSRubberBandFix", "navigator", "maxTouchPoints", "scrollLeft", "clientWidth", "getChangedMonth", "box", "getBoundingClientRect", "month", "monthBox", "Math", "abs", "forceRenderDate", "updateActiveMonth", "style", "removeProperty", "appliediOSRubberBandFix", "newDate", "setProperty", "resolveForceDateScrolling", "scrollTimeout", "scrollCallback", "clearTimeout", "setTimeout", "destroyCalendarListener", "removeEventListener", "destroyInteractionListeners", "processValue", "hasValue", "valueToProcess", "singleValue", "targetValue", "hour", "minute", "ampm", "didChange<PERSON>onth", "bodyIsVisible", "isGridStyle", "showMonthAndYear", "areAllSelectedDatesInSameMonth", "firstMonth", "animateToDate", "_ref", "_asyncToGenerator", "forceDateScrollingPromise", "Promise", "resolve", "targetMonthIsBefore", "_x", "apply", "arguments", "onFocus", "onBlur", "left", "offsetWidth", "scrollTo", "top", "behavior", "toggleMonthAndYearView", "isTimePopoverOpen", "color", "name", "isDateEnabled", "cancelText", "doneText", "clearText", "yearValues", "month<PERSON><PERSON><PERSON>", "hourValues", "minute<PERSON><PERSON><PERSON>", "firstDayOfWeek", "titleSelectedDatesFormatter", "showDefaultTitle", "showClearButton", "showDefaultTimeLabel", "hourCycle", "size", "preferWheel", "formatOptionsChanged", "disabled<PERSON><PERSON>ed", "emitStyle", "minC<PERSON>ed", "max<PERSON><PERSON>ed", "presentationChanged", "hasDatePresentation", "yearV<PERSON><PERSON><PERSON><PERSON>ed", "parsedYear<PERSON><PERSON>ues", "month<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "parsed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed<PERSON><PERSON><PERSON><PERSON><PERSON>", "hourValuesChanged", "parsedHourValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsed<PERSON><PERSON>ute<PERSON><PERSON><PERSON>", "valueChanged", "_this2", "_this3", "closeOverlay", "isCalendarPicker", "activePartsIsArray", "reset", "startDate", "_this4", "cancel", "_this5", "connectedCallback", "clearFocusVisible", "destroy", "disconnectedCallback", "initializeListeners", "componentDidLoad", "intersectionTrackerRef", "visibleCallback", "entries", "isIntersecting", "add", "visibleIO", "IntersectionObserver", "threshold", "hiddenCallback", "remove", "hiddenIO", "stopPropagation", "componentDidRender", "hasCalendarGrid", "componentWillLoad", "interactive", "datetime", "renderFooter", "isButtonDisabled", "clearButtonClick", "class", "id", "onClick", "renderWheelPicker", "forcePresentation", "renderArray", "renderTimePickerColumns", "renderDatePickerColumns", "renderCombinedDatePickerColumn", "renderIndividualDatePickerColumns", "activePart", "<PERSON>T<PERSON><PERSON><PERSON>", "lastM<PERSON>h", "result", "items", "itemObject", "index", "referenceParts", "todayString", "onIonChange", "detail", "find<PERSON><PERSON>", "shouldRenderMonths", "shouldRenderDays", "days", "dayObject", "valueNum", "parseInt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "years", "showMonthFirst", "renderMonthPickerColumn", "renderDayPickerColumn", "renderYearPickerColumn", "userHasSelectedDate", "hoursData", "minutesData", "dayPeriodData", "renderHourPickerColumn", "renderMinutePickerColumn", "renderDayPeriodPickerColumn", "numericInput", "isDayPeriodRTL", "order", "renderWheelView", "columnOrder", "renderCalendarHeader", "expandedIcon", "collapsedIcon", "prevMonthDisabled", "nextMonthDisabled", "hostDir", "getAttribute", "part", "ref", "monthYearToggleItemRef", "button", "lines", "btn", "monthYearAriaLabel", "setAttribute", "icon", "lazy", "flipRtl", "dir", "slot", "renderMonth", "yearAllowed", "monthAllowed", "isCalMonthDisabled", "isDatetimeDisabled", "swipeDisabled", "isWorkingMonth", "dateObject", "dayOfWeek", "isCalendarPadding", "isCalDayDisabled", "isCalDayConstrained", "dateStyle", "dateParts", "tabindex", "renderCalendarBody", "renderCalendar", "renderTimeLabel", "hasSlottedTimeLabel", "renderTimeOverlay", "_this6", "computedHourCycle", "_ref2", "popoverRef", "CustomEvent", "ionShadowTarget", "target", "on<PERSON>ill<PERSON><PERSON>iss", "_x2", "alignment", "translucent", "overlayIndex", "arrow", "onWillPresent", "cols", "for<PERSON>ach", "col", "scrollActiveItemIntoView", "keyboardEvents", "getHeaderSelectedDateText", "headerText", "weekday", "renderHeader", "showExpandedHeader", "hasSlottedTitle", "renderTime", "timeOnlyPresentation", "renderCalendarViewMonthYearPicker", "renderDatetime", "hasWheelVariant", "render", "isMonthAndYearPresentation", "shouldShowMonthAndYear", "monthYearPickerOpen", "watchers", "ios", "md", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "fromTo", "beforeStyles", "afterClearStyles", "easing", "duration", "addAnimation", "iosLeaveAnimation", "pickerIosCss", "IonPickerIosStyle0", "pickerMdCss", "IonPickerMdStyle0", "Picker", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "onBackdropTap", "dispatchCancelHandler", "role", "cancelButton", "buttons", "callButtonHandler", "presented", "delegate", "hasController", "keyboardClose", "enterAnimation", "leaveAnimation", "columns", "cssClass", "showBackdrop", "<PERSON><PERSON><PERSON><PERSON>", "animated", "htmlAttributes", "isOpen", "trigger", "onIsOpenChange", "newValue", "triggerChanged", "addClickListener", "removeClickListener", "_this7", "unlock", "lock", "attachViewToDom", "durationTimeout", "data", "_this8", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "getColumn", "column", "buttonClick", "_this9", "<PERSON><PERSON><PERSON><PERSON>", "getSelected", "_this0", "rtn", "handler", "selected", "selectedColumn", "selectedIndex", "options", "columnIndex", "zIndex", "onIonBackdropTap", "onIonPickerWillDismiss", "visible", "tappable", "buttonWrapperClass", "type", "buttonClass", "pickerColumnIosCss", "IonPickerColumnIosStyle0", "pickerColumnMdCss", "IonPickerColumnMdStyle0", "PickerColumnCmp", "ionPickerColChange", "optHeight", "rotateFactor", "scaleFactor", "velocity", "noAnimate", "colDidChange", "col<PERSON><PERSON><PERSON>", "_this1", "pickerRotateFactor", "pickerScaleFactor", "gesture", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "passive", "onStart", "onMove", "onEnd", "enable", "tmrId", "refresh", "onDomChange", "componentDidUpdate", "rafId", "cancelAnimationFrame", "emitColChange", "setSelected", "update", "saveY", "optsEl", "translateY", "translateZ", "prevSelected", "indexForY", "durationStr", "scaleStr", "children", "opt", "optOffset", "transform", "rotateX", "transitionDuration", "PICKER_OPT_SELECTED", "lastIndex", "decelerate", "DECELERATION_FRICTION", "minY", "maxY", "notLockedIn", "round", "currentPos", "event", "cancelable", "deltaY", "pow", "bounceFrom", "MAX_PICKER_SPEED", "velocityY", "hasAttribute", "TRANSITION_DURATION", "isScrollingUp", "optHeightFraction", "forceRefresh", "colEl", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "clientHeight", "align", "columnWidth", "prefix", "width", "prefixWidth", "max<PERSON><PERSON><PERSON>", "optionsWidth", "suffix", "suffixWidth", "ion_datetime", "ion_picker", "ion_picker_column"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-datetime_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { startFocusVisible } from './focus-visible-dd40d69f.js';\nimport { r as raf, d as renderHiddenInput, g as getElementRoot, l as clamp } from './helpers-be245865.js';\nimport { a as printIonError, p as printIonWarning } from './index-9b0d46f4.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { c as chevronBack, o as chevronForward, l as chevronDown, p as caretUpSharp, q as caretDownSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { g as generateDay<PERSON>ria<PERSON>abel, a as getDay, i as isBefore, b as isAfter, c as isSameDay, d as getPreviousMonth, e as getNextMonth, v as validateParts, f as getPartsFromCalendarDay, h as getNextYear, j as getPreviousYear, k as getEndOfWeek, l as getStartOfWeek, m as getPreviousDay, n as getNextDay, o as getPreviousWeek, p as getNextWeek, q as parseMinParts, r as parseMaxParts, s as parseDate, w as warnIfValueOutOfBounds, t as convertToArrayOfNumbers, u as convertDataToISO, x as getToday, y as getClosestValidDate, z as generateMonths, A as getNumDaysInMonth, B as getCombinedDateColumnData, C as getMonthColumnData, D as getDayColumnData, E as getYearColumnData, F as isMonthFirstLocale, G as getTimeColumnsData, H as isLocaleDayPeriodRTL, I as getDaysOfWeek, J as getMonthAndYear, K as getDaysOfMonth, L as getHourCycle, M as getLocalizedTime, N as getLocalizedDateTime, O as formatValue, P as clampDate, Q as parseAmPm, R as calculateHourFromAMPM } from './data-bb424ba8.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall } from './overlays-b874c3c3.js';\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport { b as hapticSelectionChanged, h as hapticSelectionEnd, a as hapticSelectionStart } from './haptic-554688a5.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\nimport './capacitor-59395cbd.js';\n\nconst isYearDisabled = (refYear, minParts, maxParts) => {\n    if (minParts && minParts.year > refYear) {\n        return true;\n    }\n    if (maxParts && maxParts.year < refYear) {\n        return true;\n    }\n    return false;\n};\n/**\n * Returns true if a given day should\n * not be interactive according to its value,\n * or the max/min dates.\n */\nconst isDayDisabled = (refParts, minParts, maxParts, dayValues) => {\n    /**\n     * If this is a filler date (i.e. padding)\n     * then the date is disabled.\n     */\n    if (refParts.day === null) {\n        return true;\n    }\n    /**\n     * If user passed in a list of acceptable day values\n     * check to make sure that the date we are looking\n     * at is in this array.\n     */\n    if (dayValues !== undefined && !dayValues.includes(refParts.day)) {\n        return true;\n    }\n    /**\n     * Given a min date, perform the following\n     * checks. If any of them are true, then the\n     * day should be disabled:\n     * 1. Is the current year < the min allowed year?\n     * 2. Is the current year === min allowed year,\n     * but the current month < the min allowed month?\n     * 3. Is the current year === min allowed year, the\n     * current month === min allow month, but the current\n     * day < the min allowed day?\n     */\n    if (minParts && isBefore(refParts, minParts)) {\n        return true;\n    }\n    /**\n     * Given a max date, perform the following\n     * checks. If any of them are true, then the\n     * day should be disabled:\n     * 1. Is the current year > the max allowed year?\n     * 2. Is the current year === max allowed year,\n     * but the current month > the max allowed month?\n     * 3. Is the current year === max allowed year, the\n     * current month === max allow month, but the current\n     * day > the max allowed day?\n     */\n    if (maxParts && isAfter(refParts, maxParts)) {\n        return true;\n    }\n    /**\n     * If none of these checks\n     * passed then the date should\n     * be interactive.\n     */\n    return false;\n};\n/**\n * Given a locale, a date, the selected date(s), and today's date,\n * generate the state for a given calendar day button.\n */\nconst getCalendarDayState = (locale, refParts, activeParts, todayParts, minParts, maxParts, dayValues) => {\n    /**\n     * activeParts signals what day(s) are currently selected in the datetime.\n     * If multiple=\"true\", this will be an array, but the logic in this util\n     * is the same whether we have one selected day or many because we're only\n     * calculating the state for one button. So, we treat a single activeParts value\n     * the same as an array of length one.\n     */\n    const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n    /**\n     * The day button is active if it is selected, or in other words, if refParts\n     * matches at least one selected date.\n     */\n    const isActive = activePartsArray.find((parts) => isSameDay(refParts, parts)) !== undefined;\n    const isToday = isSameDay(refParts, todayParts);\n    const disabled = isDayDisabled(refParts, minParts, maxParts, dayValues);\n    /**\n     * Note that we always return one object regardless of whether activeParts\n     * was an array, since we pare down to one value for isActive.\n     */\n    return {\n        disabled,\n        isActive,\n        isToday,\n        ariaSelected: isActive ? 'true' : null,\n        ariaLabel: generateDayAriaLabel(locale, isToday, refParts),\n        text: refParts.day != null ? getDay(locale, refParts) : null,\n    };\n};\n/**\n * Returns `true` if the month is disabled given the\n * current date value and min/max date constraints.\n */\nconst isMonthDisabled = (refParts, { minParts, maxParts, }) => {\n    // If the year is disabled then the month is disabled.\n    if (isYearDisabled(refParts.year, minParts, maxParts)) {\n        return true;\n    }\n    // If the date value is before the min date, then the month is disabled.\n    // If the date value is after the max date, then the month is disabled.\n    if ((minParts && isBefore(refParts, minParts)) || (maxParts && isAfter(refParts, maxParts))) {\n        return true;\n    }\n    return false;\n};\n/**\n * Given a working date, an optional minimum date range,\n * and an optional maximum date range; determine if the\n * previous navigation button is disabled.\n */\nconst isPrevMonthDisabled = (refParts, minParts, maxParts) => {\n    const prevMonth = Object.assign(Object.assign({}, getPreviousMonth(refParts)), { day: null });\n    return isMonthDisabled(prevMonth, {\n        minParts,\n        maxParts,\n    });\n};\n/**\n * Given a working date and a maximum date range,\n * determine if the next navigation button is disabled.\n */\nconst isNextMonthDisabled = (refParts, maxParts) => {\n    const nextMonth = Object.assign(Object.assign({}, getNextMonth(refParts)), { day: null });\n    return isMonthDisabled(nextMonth, {\n        maxParts,\n    });\n};\n/**\n * Given the value of the highlightedDates property\n * and an ISO string, return the styles to use for\n * that date, or undefined if none are found.\n */\nconst getHighlightStyles = (highlightedDates, dateIsoString, el) => {\n    if (Array.isArray(highlightedDates)) {\n        const dateStringWithoutTime = dateIsoString.split('T')[0];\n        const matchingHighlight = highlightedDates.find((hd) => hd.date === dateStringWithoutTime);\n        if (matchingHighlight) {\n            return {\n                textColor: matchingHighlight.textColor,\n                backgroundColor: matchingHighlight.backgroundColor,\n            };\n        }\n    }\n    else {\n        /**\n         * Wrap in a try-catch to prevent exceptions in the user's function\n         * from interrupting the calendar's rendering.\n         */\n        try {\n            return highlightedDates(dateIsoString);\n        }\n        catch (e) {\n            printIonError('Exception thrown from provided `highlightedDates` callback. Please check your function and try again.', el, e);\n        }\n    }\n    return undefined;\n};\n\n/**\n * If a time zone is provided in the format options, the rendered text could\n * differ from what was selected in the Datetime, which could cause\n * confusion.\n */\nconst warnIfTimeZoneProvided = (el, formatOptions) => {\n    var _a, _b, _c, _d;\n    if (((_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _a === void 0 ? void 0 : _a.timeZone) ||\n        ((_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _b === void 0 ? void 0 : _b.timeZoneName) ||\n        ((_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _c === void 0 ? void 0 : _c.timeZone) ||\n        ((_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _d === void 0 ? void 0 : _d.timeZoneName)) {\n        printIonWarning('Datetime: \"timeZone\" and \"timeZoneName\" are not supported in \"formatOptions\".', el);\n    }\n};\nconst checkForPresentationFormatMismatch = (el, presentation, formatOptions) => {\n    // formatOptions is not required\n    if (!formatOptions)\n        return;\n    // If formatOptions is provided, the date and/or time objects are required, depending on the presentation\n    switch (presentation) {\n        case 'date':\n        case 'month-year':\n        case 'month':\n        case 'year':\n            if (formatOptions.date === undefined) {\n                printIonWarning(`Datetime: The '${presentation}' presentation requires a date object in formatOptions.`, el);\n            }\n            break;\n        case 'time':\n            if (formatOptions.time === undefined) {\n                printIonWarning(`Datetime: The 'time' presentation requires a time object in formatOptions.`, el);\n            }\n            break;\n        case 'date-time':\n        case 'time-date':\n            if (formatOptions.date === undefined && formatOptions.time === undefined) {\n                printIonWarning(`Datetime: The '${presentation}' presentation requires either a date or time object (or both) in formatOptions.`, el);\n            }\n            break;\n    }\n};\n\nconst datetimeIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-light, #ffffff);--background-rgb:var(--ion-color-light-rgb);--title-color:var(--ion-color-step-600, #666666)}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, #cccccc);font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}:host .calendar-action-buttons ion-item{--padding-start:16px;--background-hover:transparent;--background-activated:transparent;font-size:min(1rem, 25.6px);font-weight:600}:host .calendar-action-buttons ion-item ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, #b3b3b3);font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, #cccccc)}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}\";\nconst IonDatetimeIosStyle0 = datetimeIosCss;\n\nconst datetimeMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-step-100, #ffffff);--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .datetime-calendar .calendar-action-buttons ion-item{--padding-start:20px}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, #595959)}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, gray);font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, #595959)}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}\";\nconst IonDatetimeMdStyle0 = datetimeMdCss;\n\nconst Datetime = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.ionRender = createEvent(this, \"ionRender\", 7);\n        this.inputId = `ion-dt-${datetimeIds++}`;\n        this.prevPresentation = null;\n        this.warnIfIncorrectValueUsage = () => {\n            const { multiple, value } = this;\n            if (!multiple && Array.isArray(value)) {\n                /**\n                 * We do some processing on the `value` array so\n                 * that it looks more like an array when logged to\n                 * the console.\n                 * Example given ['a', 'b']\n                 * Default toString() behavior: a,b\n                 * Custom behavior: ['a', 'b']\n                 */\n                printIonWarning(`ion-datetime was passed an array of values, but multiple=\"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map((v) => `'${v}'`).join(', ')}]\n`, this.el);\n            }\n        };\n        this.setValue = (value) => {\n            this.value = value;\n            this.ionChange.emit({ value });\n        };\n        /**\n         * Returns the DatetimePart interface\n         * to use when rendering an initial set of\n         * data. This should be used when rendering an\n         * interface in an environment where the `value`\n         * may not be set. This function works\n         * by returning the first selected date and then\n         * falling back to defaultParts if no active date\n         * is selected.\n         */\n        this.getActivePartsWithFallback = () => {\n            var _a;\n            const { defaultParts } = this;\n            return (_a = this.getActivePart()) !== null && _a !== void 0 ? _a : defaultParts;\n        };\n        this.getActivePart = () => {\n            const { activeParts } = this;\n            return Array.isArray(activeParts) ? activeParts[0] : activeParts;\n        };\n        this.closeParentOverlay = () => {\n            const popoverOrModal = this.el.closest('ion-modal, ion-popover');\n            if (popoverOrModal) {\n                popoverOrModal.dismiss();\n            }\n        };\n        this.setWorkingParts = (parts) => {\n            this.workingParts = Object.assign({}, parts);\n        };\n        this.setActiveParts = (parts, removeDate = false) => {\n            /** if the datetime component is in readonly mode,\n             * allow browsing of the calendar without changing\n             * the set value\n             */\n            if (this.readonly) {\n                return;\n            }\n            const { multiple, minParts, maxParts, activeParts } = this;\n            /**\n             * When setting the active parts, it is possible\n             * to set invalid data. For example,\n             * when updating January 31 to February,\n             * February 31 does not exist. As a result\n             * we need to validate the active parts and\n             * ensure that we are only setting valid dates.\n             * Additionally, we need to update the working parts\n             * too in the event that the validated parts are different.\n             */\n            const validatedParts = validateParts(parts, minParts, maxParts);\n            this.setWorkingParts(validatedParts);\n            if (multiple) {\n                const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n                if (removeDate) {\n                    this.activeParts = activePartsArray.filter((p) => !isSameDay(p, validatedParts));\n                }\n                else {\n                    this.activeParts = [...activePartsArray, validatedParts];\n                }\n            }\n            else {\n                this.activeParts = Object.assign({}, validatedParts);\n            }\n            const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n            if (hasSlottedButtons || this.showDefaultButtons) {\n                return;\n            }\n            this.confirm();\n        };\n        this.initializeKeyboardListeners = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            const root = this.el.shadowRoot;\n            /**\n             * Get a reference to the month\n             * element we are currently viewing.\n             */\n            const currentMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(2)');\n            /**\n             * When focusing the calendar body, we want to pass focus\n             * to the working day, but other days should\n             * only be accessible using the arrow keys. Pressing\n             * Tab should jump between bodies of selectable content.\n             */\n            const checkCalendarBodyFocus = (ev) => {\n                var _a;\n                const record = ev[0];\n                /**\n                 * If calendar body was already focused\n                 * when this fired or if the calendar body\n                 * if not currently focused, we should not re-focus\n                 * the inner day.\n                 */\n                if (((_a = record.oldValue) === null || _a === void 0 ? void 0 : _a.includes('ion-focused')) || !calendarBodyRef.classList.contains('ion-focused')) {\n                    return;\n                }\n                this.focusWorkingDay(currentMonth);\n            };\n            const mo = new MutationObserver(checkCalendarBodyFocus);\n            mo.observe(calendarBodyRef, { attributeFilter: ['class'], attributeOldValue: true });\n            this.destroyKeyboardMO = () => {\n                mo === null || mo === void 0 ? void 0 : mo.disconnect();\n            };\n            /**\n             * We must use keydown not keyup as we want\n             * to prevent scrolling when using the arrow keys.\n             */\n            calendarBodyRef.addEventListener('keydown', (ev) => {\n                const activeElement = root.activeElement;\n                if (!activeElement || !activeElement.classList.contains('calendar-day')) {\n                    return;\n                }\n                const parts = getPartsFromCalendarDay(activeElement);\n                let partsToFocus;\n                switch (ev.key) {\n                    case 'ArrowDown':\n                        ev.preventDefault();\n                        partsToFocus = getNextWeek(parts);\n                        break;\n                    case 'ArrowUp':\n                        ev.preventDefault();\n                        partsToFocus = getPreviousWeek(parts);\n                        break;\n                    case 'ArrowRight':\n                        ev.preventDefault();\n                        partsToFocus = getNextDay(parts);\n                        break;\n                    case 'ArrowLeft':\n                        ev.preventDefault();\n                        partsToFocus = getPreviousDay(parts);\n                        break;\n                    case 'Home':\n                        ev.preventDefault();\n                        partsToFocus = getStartOfWeek(parts);\n                        break;\n                    case 'End':\n                        ev.preventDefault();\n                        partsToFocus = getEndOfWeek(parts);\n                        break;\n                    case 'PageUp':\n                        ev.preventDefault();\n                        partsToFocus = ev.shiftKey ? getPreviousYear(parts) : getPreviousMonth(parts);\n                        break;\n                    case 'PageDown':\n                        ev.preventDefault();\n                        partsToFocus = ev.shiftKey ? getNextYear(parts) : getNextMonth(parts);\n                        break;\n                    /**\n                     * Do not preventDefault here\n                     * as we do not want to override other\n                     * browser defaults such as pressing Enter/Space\n                     * to select a day.\n                     */\n                    default:\n                        return;\n                }\n                /**\n                 * If the day we want to move focus to is\n                 * disabled, do not do anything.\n                 */\n                if (isDayDisabled(partsToFocus, this.minParts, this.maxParts)) {\n                    return;\n                }\n                this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), partsToFocus));\n                /**\n                 * Give view a chance to re-render\n                 * then move focus to the new working day\n                 */\n                requestAnimationFrame(() => this.focusWorkingDay(currentMonth));\n            });\n        };\n        this.focusWorkingDay = (currentMonth) => {\n            /**\n             * Get the number of padding days so\n             * we know how much to offset our next selector by\n             * to grab the correct calendar-day element.\n             */\n            const padding = currentMonth.querySelectorAll('.calendar-day-padding');\n            const { day } = this.workingParts;\n            if (day === null) {\n                return;\n            }\n            /**\n             * Get the calendar day element\n             * and focus it.\n             */\n            const dayEl = currentMonth.querySelector(`.calendar-day-wrapper:nth-of-type(${padding.length + day}) .calendar-day`);\n            if (dayEl) {\n                dayEl.focus();\n            }\n        };\n        this.processMinParts = () => {\n            const { min, defaultParts } = this;\n            if (min === undefined) {\n                this.minParts = undefined;\n                return;\n            }\n            this.minParts = parseMinParts(min, defaultParts);\n        };\n        this.processMaxParts = () => {\n            const { max, defaultParts } = this;\n            if (max === undefined) {\n                this.maxParts = undefined;\n                return;\n            }\n            this.maxParts = parseMaxParts(max, defaultParts);\n        };\n        this.initializeCalendarListener = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            /**\n             * For performance reasons, we only render 3\n             * months at a time: The current month, the previous\n             * month, and the next month. We have a scroll listener\n             * on the calendar body to append/prepend new months.\n             *\n             * We can do this because Stencil is smart enough to not\n             * re-create the .calendar-month containers, but rather\n             * update the content within those containers.\n             *\n             * As an added bonus, WebKit has some troubles with\n             * scroll-snap-stop: always, so not rendering all of\n             * the months in a row allows us to mostly sidestep\n             * that issue.\n             */\n            const months = calendarBodyRef.querySelectorAll('.calendar-month');\n            const startMonth = months[0];\n            const workingMonth = months[1];\n            const endMonth = months[2];\n            const mode = getIonMode(this);\n            const needsiOSRubberBandFix = mode === 'ios' && typeof navigator !== 'undefined' && navigator.maxTouchPoints > 1;\n            /**\n             * Before setting up the scroll listener,\n             * scroll the middle month into view.\n             * scrollIntoView() will scroll entire page\n             * if element is not in viewport. Use scrollLeft instead.\n             */\n            writeTask(() => {\n                calendarBodyRef.scrollLeft = startMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n                const getChangedMonth = (parts) => {\n                    const box = calendarBodyRef.getBoundingClientRect();\n                    /**\n                     * If the current scroll position is all the way to the left\n                     * then we have scrolled to the previous month.\n                     * Otherwise, assume that we have scrolled to the next\n                     * month. We have a tolerance of 2px to account for\n                     * sub pixel rendering.\n                     *\n                     * Check below the next line ensures that we did not\n                     * swipe and abort (i.e. we swiped but we are still on the current month).\n                     */\n                    const month = calendarBodyRef.scrollLeft <= 2 ? startMonth : endMonth;\n                    /**\n                     * The edge of the month must be lined up with\n                     * the edge of the calendar body in order for\n                     * the component to update. Otherwise, it\n                     * may be the case that the user has paused their\n                     * swipe or the browser has not finished snapping yet.\n                     * Rather than check if the x values are equal,\n                     * we give it a tolerance of 2px to account for\n                     * sub pixel rendering.\n                     */\n                    const monthBox = month.getBoundingClientRect();\n                    if (Math.abs(monthBox.x - box.x) > 2)\n                        return;\n                    /**\n                     * If we're force-rendering a month, assume we've\n                     * scrolled to that and return it.\n                     *\n                     * If forceRenderDate is ever used in a context where the\n                     * forced month is not immediately auto-scrolled to, this\n                     * should be updated to also check whether `month` has the\n                     * same month and year as the forced date.\n                     */\n                    const { forceRenderDate } = this;\n                    if (forceRenderDate !== undefined) {\n                        return { month: forceRenderDate.month, year: forceRenderDate.year, day: forceRenderDate.day };\n                    }\n                    /**\n                     * From here, we can determine if the start\n                     * month or the end month was scrolled into view.\n                     * If no month was changed, then we can return from\n                     * the scroll callback early.\n                     */\n                    if (month === startMonth) {\n                        return getPreviousMonth(parts);\n                    }\n                    else if (month === endMonth) {\n                        return getNextMonth(parts);\n                    }\n                    else {\n                        return;\n                    }\n                };\n                const updateActiveMonth = () => {\n                    if (needsiOSRubberBandFix) {\n                        calendarBodyRef.style.removeProperty('pointer-events');\n                        appliediOSRubberBandFix = false;\n                    }\n                    /**\n                     * If the month did not change\n                     * then we can return early.\n                     */\n                    const newDate = getChangedMonth(this.workingParts);\n                    if (!newDate)\n                        return;\n                    const { month, day, year } = newDate;\n                    if (isMonthDisabled({ month, year, day: null }, {\n                        minParts: Object.assign(Object.assign({}, this.minParts), { day: null }),\n                        maxParts: Object.assign(Object.assign({}, this.maxParts), { day: null }),\n                    })) {\n                        return;\n                    }\n                    /**\n                     * Prevent scrolling for other browsers\n                     * to give the DOM time to update and the container\n                     * time to properly snap.\n                     */\n                    calendarBodyRef.style.setProperty('overflow', 'hidden');\n                    /**\n                     * Use a writeTask here to ensure\n                     * that the state is updated and the\n                     * correct month is scrolled into view\n                     * in the same frame. This is not\n                     * typically a problem on newer devices\n                     * but older/slower device may have a flicker\n                     * if we did not do this.\n                     */\n                    writeTask(() => {\n                        this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), { month, day: day, year }));\n                        calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n                        calendarBodyRef.style.removeProperty('overflow');\n                        if (this.resolveForceDateScrolling) {\n                            this.resolveForceDateScrolling();\n                        }\n                    });\n                };\n                /**\n                 * When the container finishes scrolling we\n                 * need to update the DOM with the selected month.\n                 */\n                let scrollTimeout;\n                /**\n                 * We do not want to attempt to set pointer-events\n                 * multiple times within a single swipe gesture as\n                 * that adds unnecessary work to the main thread.\n                 */\n                let appliediOSRubberBandFix = false;\n                const scrollCallback = () => {\n                    if (scrollTimeout) {\n                        clearTimeout(scrollTimeout);\n                    }\n                    /**\n                     * On iOS it is possible to quickly rubber band\n                     * the scroll area before the scroll timeout has fired.\n                     * This results in users reaching the end of the scrollable\n                     * container before the DOM has updated.\n                     * By setting `pointer-events: none` we can ensure that\n                     * subsequent swipes do not happen while the container\n                     * is snapping.\n                     */\n                    if (!appliediOSRubberBandFix && needsiOSRubberBandFix) {\n                        calendarBodyRef.style.setProperty('pointer-events', 'none');\n                        appliediOSRubberBandFix = true;\n                    }\n                    // Wait ~3 frames\n                    scrollTimeout = setTimeout(updateActiveMonth, 50);\n                };\n                calendarBodyRef.addEventListener('scroll', scrollCallback);\n                this.destroyCalendarListener = () => {\n                    calendarBodyRef.removeEventListener('scroll', scrollCallback);\n                };\n            });\n        };\n        /**\n         * Clean up all listeners except for the overlay\n         * listener. This is so that we can re-create the listeners\n         * if the datetime has been hidden/presented by a modal or popover.\n         */\n        this.destroyInteractionListeners = () => {\n            const { destroyCalendarListener, destroyKeyboardMO } = this;\n            if (destroyCalendarListener !== undefined) {\n                destroyCalendarListener();\n            }\n            if (destroyKeyboardMO !== undefined) {\n                destroyKeyboardMO();\n            }\n        };\n        this.processValue = (value) => {\n            const hasValue = value !== null && value !== undefined && (!Array.isArray(value) || value.length > 0);\n            const valueToProcess = hasValue ? parseDate(value) : this.defaultParts;\n            const { minParts, maxParts, workingParts, el } = this;\n            this.warnIfIncorrectValueUsage();\n            /**\n             * Return early if the value wasn't parsed correctly, such as\n             * if an improperly formatted date string was provided.\n             */\n            if (!valueToProcess) {\n                return;\n            }\n            /**\n             * Datetime should only warn of out of bounds values\n             * if set by the user. If the `value` is undefined,\n             * we will default to today's date which may be out\n             * of bounds. In this case, the warning makes it look\n             * like the developer did something wrong which is\n             * not true.\n             */\n            if (hasValue) {\n                warnIfValueOutOfBounds(valueToProcess, minParts, maxParts);\n            }\n            /**\n             * If there are multiple values, pick an arbitrary one to clamp to. This way,\n             * if the values are across months, we always show at least one of them. Note\n             * that the values don't necessarily have to be in order.\n             */\n            const singleValue = Array.isArray(valueToProcess) ? valueToProcess[0] : valueToProcess;\n            const targetValue = clampDate(singleValue, minParts, maxParts);\n            const { month, day, year, hour, minute } = targetValue;\n            const ampm = parseAmPm(hour);\n            /**\n             * Since `activeParts` indicates a value that\n             * been explicitly selected either by the\n             * user or the app, only update `activeParts`\n             * if the `value` property is set.\n             */\n            if (hasValue) {\n                if (Array.isArray(valueToProcess)) {\n                    this.activeParts = [...valueToProcess];\n                }\n                else {\n                    this.activeParts = {\n                        month,\n                        day,\n                        year,\n                        hour,\n                        minute,\n                        ampm,\n                    };\n                }\n            }\n            else {\n                /**\n                 * Reset the active parts if the value is not set.\n                 * This will clear the selected calendar day when\n                 * performing a clear action or using the reset() method.\n                 */\n                this.activeParts = [];\n            }\n            /**\n             * Only animate if:\n             * 1. We're using grid style (wheel style pickers should just jump to new value)\n             * 2. The month and/or year actually changed, and both are defined (otherwise there's nothing to animate to)\n             * 3. The calendar body is visible (prevents animation when in collapsed datetime-button, for example)\n             * 4. The month/year picker is not open (since you wouldn't see the animation anyway)\n             */\n            const didChangeMonth = (month !== undefined && month !== workingParts.month) || (year !== undefined && year !== workingParts.year);\n            const bodyIsVisible = el.classList.contains('datetime-ready');\n            const { isGridStyle, showMonthAndYear } = this;\n            let areAllSelectedDatesInSameMonth = true;\n            if (Array.isArray(valueToProcess)) {\n                const firstMonth = valueToProcess[0].month;\n                for (const date of valueToProcess) {\n                    if (date.month !== firstMonth) {\n                        areAllSelectedDatesInSameMonth = false;\n                        break;\n                    }\n                }\n            }\n            /**\n             * If there is more than one date selected\n             * and the dates aren't all in the same month,\n             * then we should neither animate to the date\n             * nor update the working parts because we do\n             * not know which date the user wants to view.\n             */\n            if (areAllSelectedDatesInSameMonth) {\n                if (isGridStyle && didChangeMonth && bodyIsVisible && !showMonthAndYear) {\n                    this.animateToDate(targetValue);\n                }\n                else {\n                    /**\n                     * We only need to do this if we didn't just animate to a new month,\n                     * since that calls prevMonth/nextMonth which calls setWorkingParts for us.\n                     */\n                    this.setWorkingParts({\n                        month,\n                        day,\n                        year,\n                        hour,\n                        minute,\n                        ampm,\n                    });\n                }\n            }\n        };\n        this.animateToDate = async (targetValue) => {\n            const { workingParts } = this;\n            /**\n             * Tell other render functions that we need to force the\n             * target month to appear in place of the actual next/prev month.\n             * Because this is a State variable, a rerender will be triggered\n             * automatically, updating the rendered months.\n             */\n            this.forceRenderDate = targetValue;\n            /**\n             * Flag that we've started scrolling to the forced date.\n             * The resolve function will be called by the datetime's\n             * scroll listener when it's done updating everything.\n             * This is a replacement for making prev/nextMonth async,\n             * since the logic we're waiting on is in a listener.\n             */\n            const forceDateScrollingPromise = new Promise((resolve) => {\n                this.resolveForceDateScrolling = resolve;\n            });\n            /**\n             * Animate smoothly to the forced month. This will also update\n             * workingParts and correct the surrounding months for us.\n             */\n            const targetMonthIsBefore = isBefore(targetValue, workingParts);\n            targetMonthIsBefore ? this.prevMonth() : this.nextMonth();\n            await forceDateScrollingPromise;\n            this.resolveForceDateScrolling = undefined;\n            this.forceRenderDate = undefined;\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.hasValue = () => {\n            return this.value != null;\n        };\n        this.nextMonth = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            const nextMonth = calendarBodyRef.querySelector('.calendar-month:last-of-type');\n            if (!nextMonth) {\n                return;\n            }\n            const left = nextMonth.offsetWidth * 2;\n            calendarBodyRef.scrollTo({\n                top: 0,\n                left: left * (isRTL(this.el) ? -1 : 1),\n                behavior: 'smooth',\n            });\n        };\n        this.prevMonth = () => {\n            const calendarBodyRef = this.calendarBodyRef;\n            if (!calendarBodyRef) {\n                return;\n            }\n            const prevMonth = calendarBodyRef.querySelector('.calendar-month:first-of-type');\n            if (!prevMonth) {\n                return;\n            }\n            calendarBodyRef.scrollTo({\n                top: 0,\n                left: 0,\n                behavior: 'smooth',\n            });\n        };\n        this.toggleMonthAndYearView = () => {\n            this.showMonthAndYear = !this.showMonthAndYear;\n        };\n        this.showMonthAndYear = false;\n        this.activeParts = [];\n        this.workingParts = {\n            month: 5,\n            day: 28,\n            year: 2021,\n            hour: 13,\n            minute: 52,\n            ampm: 'pm',\n        };\n        this.isTimePopoverOpen = false;\n        this.forceRenderDate = undefined;\n        this.color = 'primary';\n        this.name = this.inputId;\n        this.disabled = false;\n        this.formatOptions = undefined;\n        this.readonly = false;\n        this.isDateEnabled = undefined;\n        this.min = undefined;\n        this.max = undefined;\n        this.presentation = 'date-time';\n        this.cancelText = 'Cancel';\n        this.doneText = 'Done';\n        this.clearText = 'Clear';\n        this.yearValues = undefined;\n        this.monthValues = undefined;\n        this.dayValues = undefined;\n        this.hourValues = undefined;\n        this.minuteValues = undefined;\n        this.locale = 'default';\n        this.firstDayOfWeek = 0;\n        this.titleSelectedDatesFormatter = undefined;\n        this.multiple = false;\n        this.highlightedDates = undefined;\n        this.value = undefined;\n        this.showDefaultTitle = false;\n        this.showDefaultButtons = false;\n        this.showClearButton = false;\n        this.showDefaultTimeLabel = true;\n        this.hourCycle = undefined;\n        this.size = 'fixed';\n        this.preferWheel = false;\n    }\n    formatOptionsChanged() {\n        const { el, formatOptions, presentation } = this;\n        checkForPresentationFormatMismatch(el, presentation, formatOptions);\n        warnIfTimeZoneProvided(el, formatOptions);\n    }\n    disabledChanged() {\n        this.emitStyle();\n    }\n    minChanged() {\n        this.processMinParts();\n    }\n    maxChanged() {\n        this.processMaxParts();\n    }\n    presentationChanged() {\n        const { el, formatOptions, presentation } = this;\n        checkForPresentationFormatMismatch(el, presentation, formatOptions);\n    }\n    get isGridStyle() {\n        const { presentation, preferWheel } = this;\n        const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n        return hasDatePresentation && !preferWheel;\n    }\n    yearValuesChanged() {\n        this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n    }\n    monthValuesChanged() {\n        this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n    }\n    dayValuesChanged() {\n        this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n    }\n    hourValuesChanged() {\n        this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n    }\n    minuteValuesChanged() {\n        this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n    }\n    /**\n     * Update the datetime value when the value changes\n     */\n    async valueChanged() {\n        const { value } = this;\n        if (this.hasValue()) {\n            this.processValue(value);\n        }\n        this.emitStyle();\n        this.ionValueChange.emit({ value });\n    }\n    /**\n     * Confirms the selected datetime value, updates the\n     * `value` property, and optionally closes the popover\n     * or modal that the datetime was presented in.\n     */\n    async confirm(closeOverlay = false) {\n        const { isCalendarPicker, activeParts, preferWheel, workingParts } = this;\n        /**\n         * We only update the value if the presentation is not a calendar picker.\n         */\n        if (activeParts !== undefined || !isCalendarPicker) {\n            const activePartsIsArray = Array.isArray(activeParts);\n            if (activePartsIsArray && activeParts.length === 0) {\n                if (preferWheel) {\n                    /**\n                     * If the datetime is using a wheel picker, but the\n                     * active parts are empty, then the user has confirmed the\n                     * initial value (working parts) presented to them.\n                     */\n                    this.setValue(convertDataToISO(workingParts));\n                }\n                else {\n                    this.setValue(undefined);\n                }\n            }\n            else {\n                this.setValue(convertDataToISO(activeParts));\n            }\n        }\n        if (closeOverlay) {\n            this.closeParentOverlay();\n        }\n    }\n    /**\n     * Resets the internal state of the datetime but does not update the value.\n     * Passing a valid ISO-8601 string will reset the state of the component to the provided date.\n     * If no value is provided, the internal state will be reset to the clamped value of the min, max and today.\n     */\n    async reset(startDate) {\n        this.processValue(startDate);\n    }\n    /**\n     * Emits the ionCancel event and\n     * optionally closes the popover\n     * or modal that the datetime was\n     * presented in.\n     */\n    async cancel(closeOverlay = false) {\n        this.ionCancel.emit();\n        if (closeOverlay) {\n            this.closeParentOverlay();\n        }\n    }\n    get isCalendarPicker() {\n        const { presentation } = this;\n        return presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    }\n    connectedCallback() {\n        this.clearFocusVisible = startFocusVisible(this.el).destroy;\n    }\n    disconnectedCallback() {\n        if (this.clearFocusVisible) {\n            this.clearFocusVisible();\n            this.clearFocusVisible = undefined;\n        }\n    }\n    initializeListeners() {\n        this.initializeCalendarListener();\n        this.initializeKeyboardListeners();\n    }\n    componentDidLoad() {\n        const { el, intersectionTrackerRef } = this;\n        /**\n         * If a scrollable element is hidden using `display: none`,\n         * it will not have a scroll height meaning we cannot scroll elements\n         * into view. As a result, we will need to wait for the datetime to become\n         * visible if used inside of a modal or a popover otherwise the scrollable\n         * areas will not have the correct values snapped into place.\n         */\n        const visibleCallback = (entries) => {\n            const ev = entries[0];\n            if (!ev.isIntersecting) {\n                return;\n            }\n            this.initializeListeners();\n            /**\n             * TODO FW-2793: Datetime needs a frame to ensure that it\n             * can properly scroll contents into view. As a result\n             * we hide the scrollable content until after that frame\n             * so users do not see the content quickly shifting. The downside\n             * is that the content will pop into view a frame after. Maybe there\n             * is a better way to handle this?\n             */\n            writeTask(() => {\n                this.el.classList.add('datetime-ready');\n            });\n        };\n        const visibleIO = new IntersectionObserver(visibleCallback, { threshold: 0.01, root: el });\n        /**\n         * Use raf to avoid a race condition between the component loading and\n         * its display animation starting (such as when shown in a modal). This\n         * could cause the datetime to start at a visibility of 0, erroneously\n         * triggering the `hiddenIO` observer below.\n         */\n        raf(() => visibleIO === null || visibleIO === void 0 ? void 0 : visibleIO.observe(intersectionTrackerRef));\n        /**\n         * We need to clean up listeners when the datetime is hidden\n         * in a popover/modal so that we can properly scroll containers\n         * back into view if they are re-presented. When the datetime is hidden\n         * the scroll areas have scroll widths/heights of 0px, so any snapping\n         * we did originally has been lost.\n         */\n        const hiddenCallback = (entries) => {\n            const ev = entries[0];\n            if (ev.isIntersecting) {\n                return;\n            }\n            this.destroyInteractionListeners();\n            /**\n             * When datetime is hidden, we need to make sure that\n             * the month/year picker is closed. Otherwise,\n             * it will be open when the datetime re-appears\n             * and the scroll area of the calendar grid will be 0.\n             * As a result, the wrong month will be shown.\n             */\n            this.showMonthAndYear = false;\n            writeTask(() => {\n                this.el.classList.remove('datetime-ready');\n            });\n        };\n        const hiddenIO = new IntersectionObserver(hiddenCallback, { threshold: 0, root: el });\n        raf(() => hiddenIO === null || hiddenIO === void 0 ? void 0 : hiddenIO.observe(intersectionTrackerRef));\n        /**\n         * Datetime uses Ionic components that emit\n         * ionFocus and ionBlur. These events are\n         * composed meaning they will cross\n         * the shadow dom boundary. We need to\n         * stop propagation on these events otherwise\n         * developers will see 2 ionFocus or 2 ionBlur\n         * events at a time.\n         */\n        const root = getElementRoot(this.el);\n        root.addEventListener('ionFocus', (ev) => ev.stopPropagation());\n        root.addEventListener('ionBlur', (ev) => ev.stopPropagation());\n    }\n    /**\n     * When the presentation is changed, all calendar content is recreated,\n     * so we need to re-init behavior with the new elements.\n     */\n    componentDidRender() {\n        const { presentation, prevPresentation, calendarBodyRef, minParts, preferWheel, forceRenderDate } = this;\n        /**\n         * TODO(FW-2165)\n         * Remove this when https://bugs.webkit.org/show_bug.cgi?id=235960 is fixed.\n         * When using `min`, we add `scroll-snap-align: none`\n         * to the disabled month so that users cannot scroll to it.\n         * This triggers a bug in WebKit where the scroll position is reset.\n         * Since the month change logic is handled by a scroll listener,\n         * this causes the month to change leading to `scroll-snap-align`\n         * changing again, thus changing the scroll position again and causing\n         * an infinite loop.\n         * This issue only applies to the calendar grid, so we can disable\n         * it if the calendar grid is not being used.\n         */\n        const hasCalendarGrid = !preferWheel && ['date-time', 'time-date', 'date'].includes(presentation);\n        if (minParts !== undefined && hasCalendarGrid && calendarBodyRef) {\n            const workingMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(1)');\n            /**\n             * We need to make sure the datetime is not in the process\n             * of scrolling to a new datetime value if the value\n             * is updated programmatically.\n             * Otherwise, the datetime will appear to not scroll at all because\n             * we are resetting the scroll position to the center of the view.\n             * Prior to the datetime's value being updated programmatically,\n             * the calendarBodyRef is scrolled such that the middle month is centered\n             * in the view. The below code updates the scroll position so the middle\n             * month is also centered in the view. Since the scroll position did not change,\n             * the scroll callback in this file does not fire,\n             * and the resolveForceDateScrolling promise never resolves.\n             */\n            if (workingMonth && forceRenderDate === undefined) {\n                calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n            }\n        }\n        if (prevPresentation === null) {\n            this.prevPresentation = presentation;\n            return;\n        }\n        if (presentation === prevPresentation) {\n            return;\n        }\n        this.prevPresentation = presentation;\n        this.destroyInteractionListeners();\n        this.initializeListeners();\n        /**\n         * The month/year picker from the date interface\n         * should be closed as it is not available in non-date\n         * interfaces.\n         */\n        this.showMonthAndYear = false;\n        raf(() => {\n            this.ionRender.emit();\n        });\n    }\n    componentWillLoad() {\n        const { el, formatOptions, highlightedDates, multiple, presentation, preferWheel } = this;\n        if (multiple) {\n            if (presentation !== 'date') {\n                printIonWarning('Multiple date selection is only supported for presentation=\"date\".', el);\n            }\n            if (preferWheel) {\n                printIonWarning('Multiple date selection is not supported with preferWheel=\"true\".', el);\n            }\n        }\n        if (highlightedDates !== undefined) {\n            if (presentation !== 'date' && presentation !== 'date-time' && presentation !== 'time-date') {\n                printIonWarning('The highlightedDates property is only supported with the date, date-time, and time-date presentations.', el);\n            }\n            if (preferWheel) {\n                printIonWarning('The highlightedDates property is not supported with preferWheel=\"true\".', el);\n            }\n        }\n        if (formatOptions) {\n            checkForPresentationFormatMismatch(el, presentation, formatOptions);\n            warnIfTimeZoneProvided(el, formatOptions);\n        }\n        const hourValues = (this.parsedHourValues = convertToArrayOfNumbers(this.hourValues));\n        const minuteValues = (this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues));\n        const monthValues = (this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues));\n        const yearValues = (this.parsedYearValues = convertToArrayOfNumbers(this.yearValues));\n        const dayValues = (this.parsedDayValues = convertToArrayOfNumbers(this.dayValues));\n        const todayParts = (this.todayParts = parseDate(getToday()));\n        this.processMinParts();\n        this.processMaxParts();\n        this.defaultParts = getClosestValidDate({\n            refParts: todayParts,\n            monthValues,\n            dayValues,\n            yearValues,\n            hourValues,\n            minuteValues,\n            minParts: this.minParts,\n            maxParts: this.maxParts,\n        });\n        this.processValue(this.value);\n        this.emitStyle();\n    }\n    emitStyle() {\n        this.ionStyle.emit({\n            interactive: true,\n            datetime: true,\n            'interactive-disabled': this.disabled,\n        });\n    }\n    /**\n     * Universal render methods\n     * These are pieces of datetime that\n     * are rendered independently of presentation.\n     */\n    renderFooter() {\n        const { disabled, readonly, showDefaultButtons, showClearButton } = this;\n        /**\n         * The cancel, clear, and confirm buttons\n         * should not be interactive if the datetime\n         * is disabled or readonly.\n         */\n        const isButtonDisabled = disabled || readonly;\n        const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n        if (!hasSlottedButtons && !showDefaultButtons && !showClearButton) {\n            return;\n        }\n        const clearButtonClick = () => {\n            this.reset();\n            this.setValue(undefined);\n        };\n        /**\n         * By default we render two buttons:\n         * Cancel - Dismisses the datetime and\n         * does not update the `value` prop.\n         * OK - Dismisses the datetime and\n         * updates the `value` prop.\n         */\n        return (h(\"div\", { class: \"datetime-footer\" }, h(\"div\", { class: \"datetime-buttons\" }, h(\"div\", { class: {\n                ['datetime-action-buttons']: true,\n                ['has-clear-button']: this.showClearButton,\n            } }, h(\"slot\", { name: \"buttons\" }, h(\"ion-buttons\", null, showDefaultButtons && (h(\"ion-button\", { id: \"cancel-button\", color: this.color, onClick: () => this.cancel(true), disabled: isButtonDisabled }, this.cancelText)), h(\"div\", { class: \"datetime-action-buttons-container\" }, showClearButton && (h(\"ion-button\", { id: \"clear-button\", color: this.color, onClick: () => clearButtonClick(), disabled: isButtonDisabled }, this.clearText)), showDefaultButtons && (h(\"ion-button\", { id: \"confirm-button\", color: this.color, onClick: () => this.confirm(true), disabled: isButtonDisabled }, this.doneText)))))))));\n    }\n    /**\n     * Wheel picker render methods\n     */\n    renderWheelPicker(forcePresentation = this.presentation) {\n        /**\n         * If presentation=\"time-date\" we switch the\n         * order of the render array here instead of\n         * manually reordering each date/time picker\n         * column with CSS. This allows for additional\n         * flexibility if we need to render subsets\n         * of the date/time data or do additional ordering\n         * within the child render functions.\n         */\n        const renderArray = forcePresentation === 'time-date'\n            ? [this.renderTimePickerColumns(forcePresentation), this.renderDatePickerColumns(forcePresentation)]\n            : [this.renderDatePickerColumns(forcePresentation), this.renderTimePickerColumns(forcePresentation)];\n        return h(\"ion-picker-internal\", null, renderArray);\n    }\n    renderDatePickerColumns(forcePresentation) {\n        return forcePresentation === 'date-time' || forcePresentation === 'time-date'\n            ? this.renderCombinedDatePickerColumn()\n            : this.renderIndividualDatePickerColumns(forcePresentation);\n    }\n    renderCombinedDatePickerColumn() {\n        const { defaultParts, disabled, workingParts, locale, minParts, maxParts, todayParts, isDateEnabled } = this;\n        const activePart = this.getActivePartsWithFallback();\n        /**\n         * By default, generate a range of 3 months:\n         * Previous month, current month, and next month\n         */\n        const monthsToRender = generateMonths(workingParts);\n        const lastMonth = monthsToRender[monthsToRender.length - 1];\n        /**\n         * Ensure that users can select the entire window of dates.\n         */\n        monthsToRender[0].day = 1;\n        lastMonth.day = getNumDaysInMonth(lastMonth.month, lastMonth.year);\n        /**\n         * Narrow the dates rendered based on min/max dates (if any).\n         * The `min` date is used if the min is after the generated min month.\n         * The `max` date is used if the max is before the generated max month.\n         * This ensures that the sliding window always stays at 3 months\n         * but still allows future dates to be lazily rendered based on any min/max\n         * constraints.\n         */\n        const min = minParts !== undefined && isAfter(minParts, monthsToRender[0]) ? minParts : monthsToRender[0];\n        const max = maxParts !== undefined && isBefore(maxParts, lastMonth) ? maxParts : lastMonth;\n        const result = getCombinedDateColumnData(locale, todayParts, min, max, this.parsedDayValues, this.parsedMonthValues);\n        let items = result.items;\n        const parts = result.parts;\n        if (isDateEnabled) {\n            items = items.map((itemObject, index) => {\n                const referenceParts = parts[index];\n                let disabled;\n                try {\n                    /**\n                     * The `isDateEnabled` implementation is try-catch wrapped\n                     * to prevent exceptions in the user's function from\n                     * interrupting the calendar rendering.\n                     */\n                    disabled = !isDateEnabled(convertDataToISO(referenceParts));\n                }\n                catch (e) {\n                    printIonError('Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n                }\n                return Object.assign(Object.assign({}, itemObject), { disabled });\n            });\n        }\n        /**\n         * If we have selected a day already, then default the column\n         * to that value. Otherwise, set it to the default date.\n         */\n        const todayString = workingParts.day !== null\n            ? `${workingParts.year}-${workingParts.month}-${workingParts.day}`\n            : `${defaultParts.year}-${defaultParts.month}-${defaultParts.day}`;\n        return (h(\"ion-picker-column-internal\", { class: \"date-column\", color: this.color, disabled: disabled, items: items, value: todayString, onIonChange: (ev) => {\n                // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n                // Due to a Safari 14 issue we need to destroy\n                // the scroll listener before we update state\n                // and trigger a re-render.\n                if (this.destroyCalendarListener) {\n                    this.destroyCalendarListener();\n                }\n                const { value } = ev.detail;\n                const findPart = parts.find(({ month, day, year }) => value === `${year}-${month}-${day}`);\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), findPart));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), findPart));\n                // We can re-attach the scroll listener after\n                // the working parts have been updated.\n                this.initializeCalendarListener();\n                ev.stopPropagation();\n            } }));\n    }\n    renderIndividualDatePickerColumns(forcePresentation) {\n        const { workingParts, isDateEnabled } = this;\n        const shouldRenderMonths = forcePresentation !== 'year' && forcePresentation !== 'time';\n        const months = shouldRenderMonths\n            ? getMonthColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedMonthValues)\n            : [];\n        const shouldRenderDays = forcePresentation === 'date';\n        let days = shouldRenderDays\n            ? getDayColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedDayValues)\n            : [];\n        if (isDateEnabled) {\n            days = days.map((dayObject) => {\n                const { value } = dayObject;\n                const valueNum = typeof value === 'string' ? parseInt(value) : value;\n                const referenceParts = {\n                    month: workingParts.month,\n                    day: valueNum,\n                    year: workingParts.year,\n                };\n                let disabled;\n                try {\n                    /**\n                     * The `isDateEnabled` implementation is try-catch wrapped\n                     * to prevent exceptions in the user's function from\n                     * interrupting the calendar rendering.\n                     */\n                    disabled = !isDateEnabled(convertDataToISO(referenceParts));\n                }\n                catch (e) {\n                    printIonError('Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n                }\n                return Object.assign(Object.assign({}, dayObject), { disabled });\n            });\n        }\n        const shouldRenderYears = forcePresentation !== 'month' && forcePresentation !== 'time';\n        const years = shouldRenderYears\n            ? getYearColumnData(this.locale, this.defaultParts, this.minParts, this.maxParts, this.parsedYearValues)\n            : [];\n        /**\n         * Certain locales show the day before the month.\n         */\n        const showMonthFirst = isMonthFirstLocale(this.locale, { month: 'numeric', day: 'numeric' });\n        let renderArray = [];\n        if (showMonthFirst) {\n            renderArray = [\n                this.renderMonthPickerColumn(months),\n                this.renderDayPickerColumn(days),\n                this.renderYearPickerColumn(years),\n            ];\n        }\n        else {\n            renderArray = [\n                this.renderDayPickerColumn(days),\n                this.renderMonthPickerColumn(months),\n                this.renderYearPickerColumn(years),\n            ];\n        }\n        return renderArray;\n    }\n    renderDayPickerColumn(days) {\n        var _a;\n        if (days.length === 0) {\n            return [];\n        }\n        const { disabled, workingParts } = this;\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column-internal\", { class: \"day-column\", color: this.color, disabled: disabled, items: days, value: (_a = (workingParts.day !== null ? workingParts.day : this.defaultParts.day)) !== null && _a !== void 0 ? _a : undefined, onIonChange: (ev) => {\n                // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n                // Due to a Safari 14 issue we need to destroy\n                // the scroll listener before we update state\n                // and trigger a re-render.\n                if (this.destroyCalendarListener) {\n                    this.destroyCalendarListener();\n                }\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { day: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { day: ev.detail.value }));\n                // We can re-attach the scroll listener after\n                // the working parts have been updated.\n                this.initializeCalendarListener();\n                ev.stopPropagation();\n            } }));\n    }\n    renderMonthPickerColumn(months) {\n        if (months.length === 0) {\n            return [];\n        }\n        const { disabled, workingParts } = this;\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column-internal\", { class: \"month-column\", color: this.color, disabled: disabled, items: months, value: workingParts.month, onIonChange: (ev) => {\n                // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n                // Due to a Safari 14 issue we need to destroy\n                // the scroll listener before we update state\n                // and trigger a re-render.\n                if (this.destroyCalendarListener) {\n                    this.destroyCalendarListener();\n                }\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { month: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { month: ev.detail.value }));\n                // We can re-attach the scroll listener after\n                // the working parts have been updated.\n                this.initializeCalendarListener();\n                ev.stopPropagation();\n            } }));\n    }\n    renderYearPickerColumn(years) {\n        if (years.length === 0) {\n            return [];\n        }\n        const { disabled, workingParts } = this;\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column-internal\", { class: \"year-column\", color: this.color, disabled: disabled, items: years, value: workingParts.year, onIonChange: (ev) => {\n                // TODO(FW-1823) Remove this when iOS 14 support is dropped.\n                // Due to a Safari 14 issue we need to destroy\n                // the scroll listener before we update state\n                // and trigger a re-render.\n                if (this.destroyCalendarListener) {\n                    this.destroyCalendarListener();\n                }\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { year: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { year: ev.detail.value }));\n                // We can re-attach the scroll listener after\n                // the working parts have been updated.\n                this.initializeCalendarListener();\n                ev.stopPropagation();\n            } }));\n    }\n    renderTimePickerColumns(forcePresentation) {\n        if (['date', 'month', 'month-year', 'year'].includes(forcePresentation)) {\n            return [];\n        }\n        /**\n         * If a user has not selected a date,\n         * then we should show all times. If the\n         * user has selected a date (even if it has\n         * not been confirmed yet), we should apply\n         * the max and min restrictions so that the\n         * time picker shows values that are\n         * appropriate for the selected date.\n         */\n        const activePart = this.getActivePart();\n        const userHasSelectedDate = activePart !== undefined;\n        const { hoursData, minutesData, dayPeriodData } = getTimeColumnsData(this.locale, this.workingParts, this.hourCycle, userHasSelectedDate ? this.minParts : undefined, userHasSelectedDate ? this.maxParts : undefined, this.parsedHourValues, this.parsedMinuteValues);\n        return [\n            this.renderHourPickerColumn(hoursData),\n            this.renderMinutePickerColumn(minutesData),\n            this.renderDayPeriodPickerColumn(dayPeriodData),\n        ];\n    }\n    renderHourPickerColumn(hoursData) {\n        const { disabled, workingParts } = this;\n        if (hoursData.length === 0)\n            return [];\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column-internal\", { color: this.color, disabled: disabled, value: activePart.hour, items: hoursData, numericInput: true, onIonChange: (ev) => {\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { hour: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { hour: ev.detail.value }));\n                ev.stopPropagation();\n            } }));\n    }\n    renderMinutePickerColumn(minutesData) {\n        const { disabled, workingParts } = this;\n        if (minutesData.length === 0)\n            return [];\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"ion-picker-column-internal\", { color: this.color, disabled: disabled, value: activePart.minute, items: minutesData, numericInput: true, onIonChange: (ev) => {\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { minute: ev.detail.value }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { minute: ev.detail.value }));\n                ev.stopPropagation();\n            } }));\n    }\n    renderDayPeriodPickerColumn(dayPeriodData) {\n        const { disabled, workingParts } = this;\n        if (dayPeriodData.length === 0) {\n            return [];\n        }\n        const activePart = this.getActivePartsWithFallback();\n        const isDayPeriodRTL = isLocaleDayPeriodRTL(this.locale);\n        return (h(\"ion-picker-column-internal\", { style: isDayPeriodRTL ? { order: '-1' } : {}, color: this.color, disabled: disabled, value: activePart.ampm, items: dayPeriodData, onIonChange: (ev) => {\n                const hour = calculateHourFromAMPM(workingParts, ev.detail.value);\n                this.setWorkingParts(Object.assign(Object.assign({}, workingParts), { ampm: ev.detail.value, hour }));\n                this.setActiveParts(Object.assign(Object.assign({}, activePart), { ampm: ev.detail.value, hour }));\n                ev.stopPropagation();\n            } }));\n    }\n    renderWheelView(forcePresentation) {\n        const { locale } = this;\n        const showMonthFirst = isMonthFirstLocale(locale);\n        const columnOrder = showMonthFirst ? 'month-first' : 'year-first';\n        return (h(\"div\", { class: {\n                [`wheel-order-${columnOrder}`]: true,\n            } }, this.renderWheelPicker(forcePresentation)));\n    }\n    /**\n     * Grid Render Methods\n     */\n    renderCalendarHeader(mode) {\n        const { disabled } = this;\n        const expandedIcon = mode === 'ios' ? chevronDown : caretUpSharp;\n        const collapsedIcon = mode === 'ios' ? chevronForward : caretDownSharp;\n        const prevMonthDisabled = disabled || isPrevMonthDisabled(this.workingParts, this.minParts, this.maxParts);\n        const nextMonthDisabled = disabled || isNextMonthDisabled(this.workingParts, this.maxParts);\n        // don't use the inheritAttributes util because it removes dir from the host, and we still need that\n        const hostDir = this.el.getAttribute('dir') || undefined;\n        return (h(\"div\", { class: \"calendar-header\" }, h(\"div\", { class: \"calendar-action-buttons\" }, h(\"div\", { class: \"calendar-month-year\" }, h(\"ion-item\", { part: \"month-year-button\", ref: (el) => (this.monthYearToggleItemRef = el), button: true, \"aria-label\": \"Show year picker\", detail: false, lines: \"none\", disabled: disabled, onClick: () => {\n                var _a;\n                this.toggleMonthAndYearView();\n                /**\n                 * TODO: FW-3547\n                 *\n                 * Currently there is not a way to set the aria-label on the inner button\n                 * on the `ion-item` and have it be reactive to changes. This is a workaround\n                 * until we either refactor `ion-item` to a button or Stencil adds a way to\n                 * have reactive props for built-in properties, such as `aria-label`.\n                 */\n                const { monthYearToggleItemRef } = this;\n                if (monthYearToggleItemRef) {\n                    const btn = (_a = monthYearToggleItemRef.shadowRoot) === null || _a === void 0 ? void 0 : _a.querySelector('.item-native');\n                    if (btn) {\n                        const monthYearAriaLabel = this.showMonthAndYear ? 'Hide year picker' : 'Show year picker';\n                        btn.setAttribute('aria-label', monthYearAriaLabel);\n                    }\n                }\n            } }, h(\"ion-label\", null, getMonthAndYear(this.locale, this.workingParts), h(\"ion-icon\", { \"aria-hidden\": \"true\", icon: this.showMonthAndYear ? expandedIcon : collapsedIcon, lazy: false, flipRtl: true })))), h(\"div\", { class: \"calendar-next-prev\" }, h(\"ion-buttons\", null, h(\"ion-button\", { \"aria-label\": \"Previous month\", disabled: prevMonthDisabled, onClick: () => this.prevMonth() }, h(\"ion-icon\", { dir: hostDir, \"aria-hidden\": \"true\", slot: \"icon-only\", icon: chevronBack, lazy: false, flipRtl: true })), h(\"ion-button\", { \"aria-label\": \"Next month\", disabled: nextMonthDisabled, onClick: () => this.nextMonth() }, h(\"ion-icon\", { dir: hostDir, \"aria-hidden\": \"true\", slot: \"icon-only\", icon: chevronForward, lazy: false, flipRtl: true }))))), h(\"div\", { class: \"calendar-days-of-week\", \"aria-hidden\": \"true\" }, getDaysOfWeek(this.locale, mode, this.firstDayOfWeek % 7).map((d) => {\n            return h(\"div\", { class: \"day-of-week\" }, d);\n        }))));\n    }\n    renderMonth(month, year) {\n        const { disabled, readonly } = this;\n        const yearAllowed = this.parsedYearValues === undefined || this.parsedYearValues.includes(year);\n        const monthAllowed = this.parsedMonthValues === undefined || this.parsedMonthValues.includes(month);\n        const isCalMonthDisabled = !yearAllowed || !monthAllowed;\n        const isDatetimeDisabled = disabled || readonly;\n        const swipeDisabled = disabled ||\n            isMonthDisabled({\n                month,\n                year,\n                day: null,\n            }, {\n                // The day is not used when checking if a month is disabled.\n                // Users should be able to access the min or max month, even if the\n                // min/max date is out of bounds (e.g. min is set to Feb 15, Feb should not be disabled).\n                minParts: Object.assign(Object.assign({}, this.minParts), { day: null }),\n                maxParts: Object.assign(Object.assign({}, this.maxParts), { day: null }),\n            });\n        // The working month should never have swipe disabled.\n        // Otherwise the CSS scroll snap will not work and the user\n        // can free-scroll the calendar.\n        const isWorkingMonth = this.workingParts.month === month && this.workingParts.year === year;\n        const activePart = this.getActivePartsWithFallback();\n        return (h(\"div\", { \"aria-hidden\": !isWorkingMonth ? 'true' : null, class: {\n                'calendar-month': true,\n                // Prevents scroll snap swipe gestures for months outside of the min/max bounds\n                'calendar-month-disabled': !isWorkingMonth && swipeDisabled,\n            } }, h(\"div\", { class: \"calendar-month-grid\" }, getDaysOfMonth(month, year, this.firstDayOfWeek % 7).map((dateObject, index) => {\n            const { day, dayOfWeek } = dateObject;\n            const { el, highlightedDates, isDateEnabled, multiple } = this;\n            const referenceParts = { month, day, year };\n            const isCalendarPadding = day === null;\n            const { isActive, isToday, ariaLabel, ariaSelected, disabled: isDayDisabled, text, } = getCalendarDayState(this.locale, referenceParts, this.activeParts, this.todayParts, this.minParts, this.maxParts, this.parsedDayValues);\n            const dateIsoString = convertDataToISO(referenceParts);\n            let isCalDayDisabled = isCalMonthDisabled || isDayDisabled;\n            if (!isCalDayDisabled && isDateEnabled !== undefined) {\n                try {\n                    /**\n                     * The `isDateEnabled` implementation is try-catch wrapped\n                     * to prevent exceptions in the user's function from\n                     * interrupting the calendar rendering.\n                     */\n                    isCalDayDisabled = !isDateEnabled(dateIsoString);\n                }\n                catch (e) {\n                    printIonError('Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', el, e);\n                }\n            }\n            /**\n             * Some days are constrained through max & min or allowed dates\n             * and also disabled because the component is readonly or disabled.\n             * These need to be displayed differently.\n             */\n            const isCalDayConstrained = isCalDayDisabled && isDatetimeDisabled;\n            const isButtonDisabled = isCalDayDisabled || isDatetimeDisabled;\n            let dateStyle = undefined;\n            /**\n             * Custom highlight styles should not override the style for selected dates,\n             * nor apply to \"filler days\" at the start of the grid.\n             */\n            if (highlightedDates !== undefined && !isActive && day !== null) {\n                dateStyle = getHighlightStyles(highlightedDates, dateIsoString, el);\n            }\n            let dateParts = undefined;\n            // \"Filler days\" at the beginning of the grid should not get the calendar day\n            // CSS parts added to them\n            if (!isCalendarPadding) {\n                dateParts = `calendar-day${isActive ? ' active' : ''}${isToday ? ' today' : ''}${isCalDayDisabled ? ' disabled' : ''}`;\n            }\n            return (h(\"div\", { class: \"calendar-day-wrapper\" }, h(\"button\", {\n                // We need to use !important for the inline styles here because\n                // otherwise the CSS shadow parts will override these styles.\n                // See https://github.com/WICG/webcomponents/issues/847\n                // Both the CSS shadow parts and highlightedDates styles are\n                // provided by the developer, but highlightedDates styles should\n                // always take priority.\n                ref: (el) => {\n                    if (el) {\n                        el.style.setProperty('color', `${dateStyle ? dateStyle.textColor : ''}`, 'important');\n                        el.style.setProperty('background-color', `${dateStyle ? dateStyle.backgroundColor : ''}`, 'important');\n                    }\n                }, tabindex: \"-1\", \"data-day\": day, \"data-month\": month, \"data-year\": year, \"data-index\": index, \"data-day-of-week\": dayOfWeek, disabled: isButtonDisabled, class: {\n                    'calendar-day-padding': isCalendarPadding,\n                    'calendar-day': true,\n                    'calendar-day-active': isActive,\n                    'calendar-day-constrained': isCalDayConstrained,\n                    'calendar-day-today': isToday,\n                }, part: dateParts, \"aria-hidden\": isCalendarPadding ? 'true' : null, \"aria-selected\": ariaSelected, \"aria-label\": ariaLabel, onClick: () => {\n                    if (isCalendarPadding) {\n                        return;\n                    }\n                    this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), { month,\n                        day,\n                        year }));\n                    // multiple only needs date info, so we can wipe out other fields like time\n                    if (multiple) {\n                        this.setActiveParts({\n                            month,\n                            day,\n                            year,\n                        }, isActive);\n                    }\n                    else {\n                        this.setActiveParts(Object.assign(Object.assign({}, activePart), { month,\n                            day,\n                            year }));\n                    }\n                }\n            }, text)));\n        }))));\n    }\n    renderCalendarBody() {\n        return (h(\"div\", { class: \"calendar-body ion-focusable\", ref: (el) => (this.calendarBodyRef = el), tabindex: \"0\" }, generateMonths(this.workingParts, this.forceRenderDate).map(({ month, year }) => {\n            return this.renderMonth(month, year);\n        })));\n    }\n    renderCalendar(mode) {\n        return (h(\"div\", { class: \"datetime-calendar\", key: \"datetime-calendar\" }, this.renderCalendarHeader(mode), this.renderCalendarBody()));\n    }\n    renderTimeLabel() {\n        const hasSlottedTimeLabel = this.el.querySelector('[slot=\"time-label\"]') !== null;\n        if (!hasSlottedTimeLabel && !this.showDefaultTimeLabel) {\n            return;\n        }\n        return h(\"slot\", { name: \"time-label\" }, \"Time\");\n    }\n    renderTimeOverlay() {\n        const { disabled, hourCycle, isTimePopoverOpen, locale, formatOptions } = this;\n        const computedHourCycle = getHourCycle(locale, hourCycle);\n        const activePart = this.getActivePartsWithFallback();\n        return [\n            h(\"div\", { class: \"time-header\" }, this.renderTimeLabel()),\n            h(\"button\", { class: {\n                    'time-body': true,\n                    'time-body-active': isTimePopoverOpen,\n                }, part: `time-button${isTimePopoverOpen ? ' active' : ''}`, \"aria-expanded\": \"false\", \"aria-haspopup\": \"true\", disabled: disabled, onClick: async (ev) => {\n                    const { popoverRef } = this;\n                    if (popoverRef) {\n                        this.isTimePopoverOpen = true;\n                        popoverRef.present(new CustomEvent('ionShadowTarget', {\n                            detail: {\n                                ionShadowTarget: ev.target,\n                            },\n                        }));\n                        await popoverRef.onWillDismiss();\n                        this.isTimePopoverOpen = false;\n                    }\n                } }, getLocalizedTime(locale, activePart, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time)),\n            h(\"ion-popover\", { alignment: \"center\", translucent: true, overlayIndex: 1, arrow: false, onWillPresent: (ev) => {\n                    /**\n                     * Intersection Observers do not consistently fire between Blink and Webkit\n                     * when toggling the visibility of the popover and trying to scroll the picker\n                     * column to the correct time value.\n                     *\n                     * This will correctly scroll the element position to the correct time value,\n                     * before the popover is fully presented.\n                     */\n                    const cols = ev.target.querySelectorAll('ion-picker-column-internal');\n                    // TODO (FW-615): Potentially remove this when intersection observers are fixed in picker column\n                    cols.forEach((col) => col.scrollActiveItemIntoView());\n                }, style: {\n                    '--offset-y': '-10px',\n                    '--min-width': 'fit-content',\n                },\n                // Allow native browser keyboard events to support up/down/home/<USER>\n                // navigation within the time picker.\n                keyboardEvents: true, ref: (el) => (this.popoverRef = el) }, this.renderWheelPicker('time')),\n        ];\n    }\n    getHeaderSelectedDateText() {\n        var _a;\n        const { activeParts, formatOptions, multiple, titleSelectedDatesFormatter } = this;\n        const isArray = Array.isArray(activeParts);\n        let headerText;\n        if (multiple && isArray && activeParts.length !== 1) {\n            headerText = `${activeParts.length} days`; // default/fallback for multiple selection\n            if (titleSelectedDatesFormatter !== undefined) {\n                try {\n                    headerText = titleSelectedDatesFormatter(convertDataToISO(activeParts));\n                }\n                catch (e) {\n                    printIonError('Exception in provided `titleSelectedDatesFormatter`: ', e);\n                }\n            }\n        }\n        else {\n            // for exactly 1 day selected (multiple set or not), show a formatted version of that\n            headerText = getLocalizedDateTime(this.locale, this.getActivePartsWithFallback(), (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : { weekday: 'short', month: 'short', day: 'numeric' });\n        }\n        return headerText;\n    }\n    renderHeader(showExpandedHeader = true) {\n        const hasSlottedTitle = this.el.querySelector('[slot=\"title\"]') !== null;\n        if (!hasSlottedTitle && !this.showDefaultTitle) {\n            return;\n        }\n        return (h(\"div\", { class: \"datetime-header\" }, h(\"div\", { class: \"datetime-title\" }, h(\"slot\", { name: \"title\" }, \"Select Date\")), showExpandedHeader && h(\"div\", { class: \"datetime-selected-date\" }, this.getHeaderSelectedDateText())));\n    }\n    /**\n     * Render time picker inside of datetime.\n     * Do not pass color prop to segment on\n     * iOS mode. MD segment has been customized and\n     * should take on the color prop, but iOS\n     * should just be the default segment.\n     */\n    renderTime() {\n        const { presentation } = this;\n        const timeOnlyPresentation = presentation === 'time';\n        return (h(\"div\", { class: \"datetime-time\" }, timeOnlyPresentation ? this.renderWheelPicker() : this.renderTimeOverlay()));\n    }\n    /**\n     * Renders the month/year picker that is\n     * displayed on the calendar grid.\n     * The .datetime-year class has additional\n     * styles that let us show/hide the\n     * picker when the user clicks on the\n     * toggle in the calendar header.\n     */\n    renderCalendarViewMonthYearPicker() {\n        return h(\"div\", { class: \"datetime-year\" }, this.renderWheelView('month-year'));\n    }\n    /**\n     * Render entry point\n     * All presentation types are rendered from here.\n     */\n    renderDatetime(mode) {\n        const { presentation, preferWheel } = this;\n        /**\n         * Certain presentation types have separate grid and wheel displays.\n         * If preferWheel is true then we should show a wheel picker instead.\n         */\n        const hasWheelVariant = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n        if (preferWheel && hasWheelVariant) {\n            return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n        }\n        switch (presentation) {\n            case 'date-time':\n                return [\n                    this.renderHeader(),\n                    this.renderCalendar(mode),\n                    this.renderCalendarViewMonthYearPicker(),\n                    this.renderTime(),\n                    this.renderFooter(),\n                ];\n            case 'time-date':\n                return [\n                    this.renderHeader(),\n                    this.renderTime(),\n                    this.renderCalendar(mode),\n                    this.renderCalendarViewMonthYearPicker(),\n                    this.renderFooter(),\n                ];\n            case 'time':\n                return [this.renderHeader(false), this.renderTime(), this.renderFooter()];\n            case 'month':\n            case 'month-year':\n            case 'year':\n                return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n            default:\n                return [\n                    this.renderHeader(),\n                    this.renderCalendar(mode),\n                    this.renderCalendarViewMonthYearPicker(),\n                    this.renderFooter(),\n                ];\n        }\n    }\n    render() {\n        const { name, value, disabled, el, color, readonly, showMonthAndYear, preferWheel, presentation, size, isGridStyle, } = this;\n        const mode = getIonMode(this);\n        const isMonthAndYearPresentation = presentation === 'year' || presentation === 'month' || presentation === 'month-year';\n        const shouldShowMonthAndYear = showMonthAndYear || isMonthAndYearPresentation;\n        const monthYearPickerOpen = showMonthAndYear && !isMonthAndYearPresentation;\n        const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n        const hasWheelVariant = hasDatePresentation && preferWheel;\n        renderHiddenInput(true, el, name, formatValue(value), disabled);\n        return (h(Host, { key: '8490192beb6c5c6064ed8f2a7be2d51846f84f36', \"aria-disabled\": disabled ? 'true' : null, onFocus: this.onFocus, onBlur: this.onBlur, class: Object.assign({}, createColorClasses(color, {\n                [mode]: true,\n                ['datetime-readonly']: readonly,\n                ['datetime-disabled']: disabled,\n                'show-month-and-year': shouldShowMonthAndYear,\n                'month-year-picker-open': monthYearPickerOpen,\n                [`datetime-presentation-${presentation}`]: true,\n                [`datetime-size-${size}`]: true,\n                [`datetime-prefer-wheel`]: hasWheelVariant,\n                [`datetime-grid`]: isGridStyle,\n            })) }, h(\"div\", { key: 'a2959c07ed871f9004a2f11ab1385a5a7b5737fd', class: \"intersection-tracker\", ref: (el) => (this.intersectionTrackerRef = el) }), this.renderDatetime(mode)));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"formatOptions\": [\"formatOptionsChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"min\": [\"minChanged\"],\n        \"max\": [\"maxChanged\"],\n        \"presentation\": [\"presentationChanged\"],\n        \"yearValues\": [\"yearValuesChanged\"],\n        \"monthValues\": [\"monthValuesChanged\"],\n        \"dayValues\": [\"dayValuesChanged\"],\n        \"hourValues\": [\"hourValuesChanged\"],\n        \"minuteValues\": [\"minuteValuesChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet datetimeIds = 0;\nDatetime.style = {\n    ios: IonDatetimeIosStyle0,\n    md: IonDatetimeMdStyle0\n};\n\n/**\n * iOS Picker Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.picker-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Picker Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 'var(--backdrop-opacity)', 0.01);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.picker-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst pickerIosCss = \".sc-ion-picker-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-ios-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-ios-h{left:0}[dir=rtl].sc-ion-picker-ios-h,[dir=rtl] .sc-ion-picker-ios-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-ios-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-ios-h{display:none}.picker-wrapper.sc-ion-picker-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-ios:active,.picker-button.sc-ion-picker-ios:focus{outline:none}.picker-columns.sc-ion-picker-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-ios,.picker-below-highlight.sc-ion-picker-ios{display:none;pointer-events:none}.sc-ion-picker-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-ios:last-child .picker-button.sc-ion-picker-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-ios,.picker-button.ion-activated.sc-ion-picker-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:16px}.picker-columns.sc-ion-picker-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}\";\nconst IonPickerIosStyle0 = pickerIosCss;\n\nconst pickerMdCss = \".sc-ion-picker-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-md-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-md-h{left:0}[dir=rtl].sc-ion-picker-md-h,[dir=rtl] .sc-ion-picker-md-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-md-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-md-h{display:none}.picker-wrapper.sc-ion-picker-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-md:active,.picker-button.sc-ion-picker-md:focus{outline:none}.picker-columns.sc-ion-picker-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-md,.picker-below-highlight.sc-ion-picker-md{display:none;pointer-events:none}.sc-ion-picker-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-md,.picker-button.ion-activated.sc-ion-picker-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}\";\nconst IonPickerMdStyle0 = pickerMdCss;\n\nconst Picker = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionPickerDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionPickerWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionPickerWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionPickerDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.dispatchCancelHandler = (ev) => {\n            const role = ev.detail.role;\n            if (isCancel(role)) {\n                const cancelButton = this.buttons.find((b) => b.role === 'cancel');\n                this.callButtonHandler(cancelButton);\n            }\n        };\n        this.presented = false;\n        this.overlayIndex = undefined;\n        this.delegate = undefined;\n        this.hasController = false;\n        this.keyboardClose = true;\n        this.enterAnimation = undefined;\n        this.leaveAnimation = undefined;\n        this.buttons = [];\n        this.columns = [];\n        this.cssClass = undefined;\n        this.duration = 0;\n        this.showBackdrop = true;\n        this.backdropDismiss = true;\n        this.animated = true;\n        this.htmlAttributes = undefined;\n        this.isOpen = false;\n        this.trigger = undefined;\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n    }\n    componentWillLoad() {\n        setOverlayId(this.el);\n    }\n    componentDidLoad() {\n        /**\n         * If picker was rendered with isOpen=\"true\"\n         * then we should open picker immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    /**\n     * Present the picker overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'pickerEnter', iosEnterAnimation, iosEnterAnimation, undefined);\n        if (this.duration > 0) {\n            this.durationTimeout = setTimeout(() => this.dismiss(), this.duration);\n        }\n        unlock();\n    }\n    /**\n     * Dismiss the picker overlay after it has been presented.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the picker.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the picker.\n     * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n     *\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        if (this.durationTimeout) {\n            clearTimeout(this.durationTimeout);\n        }\n        const dismissed = await dismiss(this, data, role, 'pickerLeave', iosLeaveAnimation, iosLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the picker did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionPickerDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the picker will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionPickerWillDismiss');\n    }\n    /**\n     * Get the column that matches the specified name.\n     *\n     * @param name The name of the column.\n     */\n    getColumn(name) {\n        return Promise.resolve(this.columns.find((column) => column.name === name));\n    }\n    async buttonClick(button) {\n        const role = button.role;\n        if (isCancel(role)) {\n            return this.dismiss(undefined, role);\n        }\n        const shouldDismiss = await this.callButtonHandler(button);\n        if (shouldDismiss) {\n            return this.dismiss(this.getSelected(), button.role);\n        }\n        return Promise.resolve();\n    }\n    async callButtonHandler(button) {\n        if (button) {\n            // a handler has been provided, execute it\n            // pass the handler the values from the inputs\n            const rtn = await safeCall(button.handler, this.getSelected());\n            if (rtn === false) {\n                // if the return value of the handler is false then do not dismiss\n                return false;\n            }\n        }\n        return true;\n    }\n    getSelected() {\n        const selected = {};\n        this.columns.forEach((col, index) => {\n            const selectedColumn = col.selectedIndex !== undefined ? col.options[col.selectedIndex] : undefined;\n            selected[col.name] = {\n                text: selectedColumn ? selectedColumn.text : undefined,\n                value: selectedColumn ? selectedColumn.value : undefined,\n                columnIndex: index,\n            };\n        });\n        return selected;\n    }\n    render() {\n        const { htmlAttributes } = this;\n        const mode = getIonMode(this);\n        return (h(Host, Object.assign({ key: 'eb5f91ea74fb11daa6942f779ef461742cad9ecb', \"aria-modal\": \"true\", tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign({ [mode]: true,\n                // Used internally for styling\n                [`picker-${mode}`]: true, 'overlay-hidden': true }, getClassMap(this.cssClass)), onIonBackdropTap: this.onBackdropTap, onIonPickerWillDismiss: this.dispatchCancelHandler }), h(\"ion-backdrop\", { key: '7ea872d939e62f14129fff15334b2822ad2360c9', visible: this.showBackdrop, tappable: this.backdropDismiss }), h(\"div\", { key: '2d77c225091eacab0207e28c96b966122afafef0', tabindex: \"0\" }), h(\"div\", { key: '630d21e0c60ad97b71462cdc540858bb6ced0b8f', class: \"picker-wrapper ion-overlay-wrapper\", role: \"dialog\" }, h(\"div\", { key: 'fa8553ec8d2ce8bf93e16e02334b6475cb51b5d4', class: \"picker-toolbar\" }, this.buttons.map((b) => (h(\"div\", { class: buttonWrapperClass(b) }, h(\"button\", { type: \"button\", onClick: () => this.buttonClick(b), class: buttonClass(b) }, b.text))))), h(\"div\", { key: '177d1bcbd0ce38f16d9c936295a917fb981d02d7', class: \"picker-columns\" }, h(\"div\", { key: 'be99b6e0279c210ef91a88ccc81acc7d37917a53', class: \"picker-above-highlight\" }), this.presented && this.columns.map((c) => h(\"ion-picker-column\", { col: c })), h(\"div\", { key: 'b36b21e8133b59e873e1d3447a1279f1b971c854', class: \"picker-below-highlight\" }))), h(\"div\", { key: '17cea6dd24dbb0a08073ca4a84bfe027eb24833d', tabindex: \"0\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst buttonWrapperClass = (button) => {\n    return {\n        [`picker-toolbar-${button.role}`]: button.role !== undefined,\n        'picker-toolbar-button': true,\n    };\n};\nconst buttonClass = (button) => {\n    return Object.assign({ 'picker-button': true, 'ion-activatable': true }, getClassMap(button.cssClass));\n};\nPicker.style = {\n    ios: IonPickerIosStyle0,\n    md: IonPickerMdStyle0\n};\n\nconst pickerColumnIosCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}\";\nconst IonPickerColumnIosStyle0 = pickerColumnIosCss;\n\nconst pickerColumnMdCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #3880ff)}\";\nconst IonPickerColumnMdStyle0 = pickerColumnMdCss;\n\nconst PickerColumnCmp = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionPickerColChange = createEvent(this, \"ionPickerColChange\", 7);\n        this.optHeight = 0;\n        this.rotateFactor = 0;\n        this.scaleFactor = 1;\n        this.velocity = 0;\n        this.y = 0;\n        this.noAnimate = true;\n        // `colDidChange` is a flag that gets set when the column is changed\n        // dynamically. When this flag is set, the column will refresh\n        // after the component re-renders to incorporate the new column data.\n        // This is necessary because `this.refresh` queries for the option elements,\n        // so it needs to wait for the latest elements to be available in the DOM.\n        // Ex: column is created with 3 options. User updates the column data\n        // to have 5 options. The column will still think it only has 3 options.\n        this.colDidChange = false;\n        this.col = undefined;\n    }\n    colChanged() {\n        this.colDidChange = true;\n    }\n    async connectedCallback() {\n        let pickerRotateFactor = 0;\n        let pickerScaleFactor = 0.81;\n        const mode = getIonMode(this);\n        if (mode === 'ios') {\n            pickerRotateFactor = -0.46;\n            pickerScaleFactor = 1;\n        }\n        this.rotateFactor = pickerRotateFactor;\n        this.scaleFactor = pickerScaleFactor;\n        this.gesture = (await import('./index-2cf77112.js')).createGesture({\n            el: this.el,\n            gestureName: 'picker-swipe',\n            gesturePriority: 100,\n            threshold: 0,\n            passive: false,\n            onStart: (ev) => this.onStart(ev),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.gesture.enable();\n        // Options have not been initialized yet\n        // Animation must be disabled through the `noAnimate` flag\n        // Otherwise, the options will render\n        // at the top of the column and transition down\n        this.tmrId = setTimeout(() => {\n            this.noAnimate = false;\n            // After initialization, `refresh()` will be called\n            // At this point, animation will be enabled. The options will\n            // animate as they are being selected.\n            this.refresh(true);\n        }, 250);\n    }\n    componentDidLoad() {\n        this.onDomChange();\n    }\n    componentDidUpdate() {\n        // Options may have changed since last update.\n        if (this.colDidChange) {\n            // Animation must be disabled through the `onDomChange` parameter.\n            // Otherwise, the recently added options will render\n            // at the top of the column and transition down\n            this.onDomChange(true, false);\n            this.colDidChange = false;\n        }\n    }\n    disconnectedCallback() {\n        if (this.rafId !== undefined)\n            cancelAnimationFrame(this.rafId);\n        if (this.tmrId)\n            clearTimeout(this.tmrId);\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    emitColChange() {\n        this.ionPickerColChange.emit(this.col);\n    }\n    setSelected(selectedIndex, duration) {\n        // if there is a selected index, then figure out it's y position\n        // if there isn't a selected index, then just use the top y position\n        const y = selectedIndex > -1 ? -(selectedIndex * this.optHeight) : 0;\n        this.velocity = 0;\n        // set what y position we're at\n        if (this.rafId !== undefined)\n            cancelAnimationFrame(this.rafId);\n        this.update(y, duration, true);\n        this.emitColChange();\n    }\n    update(y, duration, saveY) {\n        if (!this.optsEl) {\n            return;\n        }\n        // ensure we've got a good round number :)\n        let translateY = 0;\n        let translateZ = 0;\n        const { col, rotateFactor } = this;\n        const prevSelected = col.selectedIndex;\n        const selectedIndex = (col.selectedIndex = this.indexForY(-y));\n        const durationStr = duration === 0 ? '' : duration + 'ms';\n        const scaleStr = `scale(${this.scaleFactor})`;\n        const children = this.optsEl.children;\n        for (let i = 0; i < children.length; i++) {\n            const button = children[i];\n            const opt = col.options[i];\n            const optOffset = i * this.optHeight + y;\n            let transform = '';\n            if (rotateFactor !== 0) {\n                const rotateX = optOffset * rotateFactor;\n                if (Math.abs(rotateX) <= 90) {\n                    translateY = 0;\n                    translateZ = 90;\n                    transform = `rotateX(${rotateX}deg) `;\n                }\n                else {\n                    translateY = -9999;\n                }\n            }\n            else {\n                translateZ = 0;\n                translateY = optOffset;\n            }\n            const selected = selectedIndex === i;\n            transform += `translate3d(0px,${translateY}px,${translateZ}px) `;\n            if (this.scaleFactor !== 1 && !selected) {\n                transform += scaleStr;\n            }\n            // Update transition duration\n            if (this.noAnimate) {\n                opt.duration = 0;\n                button.style.transitionDuration = '';\n            }\n            else if (duration !== opt.duration) {\n                opt.duration = duration;\n                button.style.transitionDuration = durationStr;\n            }\n            // Update transform\n            if (transform !== opt.transform) {\n                opt.transform = transform;\n            }\n            button.style.transform = transform;\n            /**\n             * Ensure that the select column\n             * item has the selected class\n             */\n            opt.selected = selected;\n            if (selected) {\n                button.classList.add(PICKER_OPT_SELECTED);\n            }\n            else {\n                button.classList.remove(PICKER_OPT_SELECTED);\n            }\n        }\n        this.col.prevSelected = prevSelected;\n        if (saveY) {\n            this.y = y;\n        }\n        if (this.lastIndex !== selectedIndex) {\n            // have not set a last index yet\n            hapticSelectionChanged();\n            this.lastIndex = selectedIndex;\n        }\n    }\n    decelerate() {\n        if (this.velocity !== 0) {\n            // still decelerating\n            this.velocity *= DECELERATION_FRICTION;\n            // do not let it go slower than a velocity of 1\n            this.velocity = this.velocity > 0 ? Math.max(this.velocity, 1) : Math.min(this.velocity, -1);\n            let y = this.y + this.velocity;\n            if (y > this.minY) {\n                // whoops, it's trying to scroll up farther than the options we have!\n                y = this.minY;\n                this.velocity = 0;\n            }\n            else if (y < this.maxY) {\n                // gahh, it's trying to scroll down farther than we can!\n                y = this.maxY;\n                this.velocity = 0;\n            }\n            this.update(y, 0, true);\n            const notLockedIn = Math.round(y) % this.optHeight !== 0 || Math.abs(this.velocity) > 1;\n            if (notLockedIn) {\n                // isn't locked in yet, keep decelerating until it is\n                this.rafId = requestAnimationFrame(() => this.decelerate());\n            }\n            else {\n                this.velocity = 0;\n                this.emitColChange();\n                hapticSelectionEnd();\n            }\n        }\n        else if (this.y % this.optHeight !== 0) {\n            // needs to still get locked into a position so options line up\n            const currentPos = Math.abs(this.y % this.optHeight);\n            // create a velocity in the direction it needs to scroll\n            this.velocity = currentPos > this.optHeight / 2 ? 1 : -1;\n            this.decelerate();\n        }\n    }\n    indexForY(y) {\n        return Math.min(Math.max(Math.abs(Math.round(y / this.optHeight)), 0), this.col.options.length - 1);\n    }\n    onStart(detail) {\n        // We have to prevent default in order to block scrolling under the picker\n        // but we DO NOT have to stop propagation, since we still want\n        // some \"click\" events to capture\n        if (detail.event.cancelable) {\n            detail.event.preventDefault();\n        }\n        detail.event.stopPropagation();\n        hapticSelectionStart();\n        // reset everything\n        if (this.rafId !== undefined)\n            cancelAnimationFrame(this.rafId);\n        const options = this.col.options;\n        let minY = options.length - 1;\n        let maxY = 0;\n        for (let i = 0; i < options.length; i++) {\n            if (!options[i].disabled) {\n                minY = Math.min(minY, i);\n                maxY = Math.max(maxY, i);\n            }\n        }\n        this.minY = -(minY * this.optHeight);\n        this.maxY = -(maxY * this.optHeight);\n    }\n    onMove(detail) {\n        if (detail.event.cancelable) {\n            detail.event.preventDefault();\n        }\n        detail.event.stopPropagation();\n        // update the scroll position relative to pointer start position\n        let y = this.y + detail.deltaY;\n        if (y > this.minY) {\n            // scrolling up higher than scroll area\n            y = Math.pow(y, 0.8);\n            this.bounceFrom = y;\n        }\n        else if (y < this.maxY) {\n            // scrolling down below scroll area\n            y += Math.pow(this.maxY - y, 0.9);\n            this.bounceFrom = y;\n        }\n        else {\n            this.bounceFrom = 0;\n        }\n        this.update(y, 0, false);\n    }\n    onEnd(detail) {\n        if (this.bounceFrom > 0) {\n            // bounce back up\n            this.update(this.minY, 100, true);\n            this.emitColChange();\n            return;\n        }\n        else if (this.bounceFrom < 0) {\n            // bounce back down\n            this.update(this.maxY, 100, true);\n            this.emitColChange();\n            return;\n        }\n        this.velocity = clamp(-MAX_PICKER_SPEED, detail.velocityY * 23, MAX_PICKER_SPEED);\n        if (this.velocity === 0 && detail.deltaY === 0) {\n            const opt = detail.event.target.closest('.picker-opt');\n            if (opt === null || opt === void 0 ? void 0 : opt.hasAttribute('opt-index')) {\n                this.setSelected(parseInt(opt.getAttribute('opt-index'), 10), TRANSITION_DURATION);\n            }\n        }\n        else {\n            this.y += detail.deltaY;\n            if (Math.abs(detail.velocityY) < 0.05) {\n                const isScrollingUp = detail.deltaY > 0;\n                const optHeightFraction = (Math.abs(this.y) % this.optHeight) / this.optHeight;\n                if (isScrollingUp && optHeightFraction > 0.5) {\n                    this.velocity = Math.abs(this.velocity) * -1;\n                }\n                else if (!isScrollingUp && optHeightFraction <= 0.5) {\n                    this.velocity = Math.abs(this.velocity);\n                }\n            }\n            this.decelerate();\n        }\n    }\n    refresh(forceRefresh, animated) {\n        var _a;\n        let min = this.col.options.length - 1;\n        let max = 0;\n        const options = this.col.options;\n        for (let i = 0; i < options.length; i++) {\n            if (!options[i].disabled) {\n                min = Math.min(min, i);\n                max = Math.max(max, i);\n            }\n        }\n        /**\n         * Only update selected value if column has a\n         * velocity of 0. If it does not, then the\n         * column is animating might land on\n         * a value different than the value at\n         * selectedIndex\n         */\n        if (this.velocity !== 0) {\n            return;\n        }\n        const selectedIndex = clamp(min, (_a = this.col.selectedIndex) !== null && _a !== void 0 ? _a : 0, max);\n        if (this.col.prevSelected !== selectedIndex || forceRefresh) {\n            const y = selectedIndex * this.optHeight * -1;\n            const duration = animated ? TRANSITION_DURATION : 0;\n            this.velocity = 0;\n            this.update(y, duration, true);\n        }\n    }\n    onDomChange(forceRefresh, animated) {\n        const colEl = this.optsEl;\n        if (colEl) {\n            // DOM READ\n            // We perfom a DOM read over a rendered item, this needs to happen after the first render or after the the column has changed\n            this.optHeight = colEl.firstElementChild ? colEl.firstElementChild.clientHeight : 0;\n        }\n        this.refresh(forceRefresh, animated);\n    }\n    render() {\n        const col = this.col;\n        const mode = getIonMode(this);\n        return (h(Host, { key: '49bb4c67a67c7318d4c305df78ceabae36355112', class: Object.assign({ [mode]: true, 'picker-col': true, 'picker-opts-left': this.col.align === 'left', 'picker-opts-right': this.col.align === 'right' }, getClassMap(col.cssClass)), style: {\n                'max-width': this.col.columnWidth,\n            } }, col.prefix && (h(\"div\", { key: '7e65761d24473e4ba0ce2d4fc707a5c5e8127903', class: \"picker-prefix\", style: { width: col.prefixWidth } }, col.prefix)), h(\"div\", { key: '65c3aea609401e8ae4ea6d363a1b9436796c0a86', class: \"picker-opts\", style: { maxWidth: col.optionsWidth }, ref: (el) => (this.optsEl = el) }, col.options.map((o, index) => (h(\"button\", { \"aria-label\": o.ariaLabel, class: { 'picker-opt': true, 'picker-opt-disabled': !!o.disabled }, \"opt-index\": index }, o.text)))), col.suffix && (h(\"div\", { key: 'c2e5a324ba95dd8832d3eb81b139e1f674d74a35', class: \"picker-suffix\", style: { width: col.suffixWidth } }, col.suffix))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"col\": [\"colChanged\"]\n    }; }\n};\nconst PICKER_OPT_SELECTED = 'picker-opt-selected';\nconst DECELERATION_FRICTION = 0.97;\nconst MAX_PICKER_SPEED = 90;\nconst TRANSITION_DURATION = 150;\nPickerColumnCmp.style = {\n    ios: IonPickerColumnIosStyle0,\n    md: IonPickerColumnMdStyle0\n};\n\nexport { Datetime as ion_datetime, Picker as ion_picker, PickerColumnCmp as ion_picker_column };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5H,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASX,CAAC,IAAIY,GAAG,EAAEV,CAAC,IAAIW,iBAAiB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,KAAK,QAAQ,uBAAuB;AACzG,SAASC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC9E,SAASC,CAAC,IAAIC,KAAK,QAAQ,mBAAmB;AAC9C,SAASC,CAAC,IAAIC,kBAAkB,EAAEX,CAAC,IAAIY,WAAW,QAAQ,qBAAqB;AAC/E,SAASF,CAAC,IAAIG,WAAW,EAAEC,CAAC,IAAIC,cAAc,EAAEb,CAAC,IAAIc,WAAW,EAAEV,CAAC,IAAIW,YAAY,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AACrI,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,SAASrB,CAAC,IAAIsB,oBAAoB,EAAElB,CAAC,IAAImB,MAAM,EAAEf,CAAC,IAAIgB,QAAQ,EAAEJ,CAAC,IAAIK,OAAO,EAAEf,CAAC,IAAIgB,SAAS,EAAEtC,CAAC,IAAIuC,gBAAgB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,aAAa,EAAEpC,CAAC,IAAIqC,uBAAuB,EAAExC,CAAC,IAAIyC,WAAW,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,YAAY,EAAEnC,CAAC,IAAIoC,cAAc,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,UAAU,EAAE5B,CAAC,IAAI6B,eAAe,EAAErC,CAAC,IAAIsC,WAAW,EAAE1B,CAAC,IAAI2B,aAAa,EAAE3D,CAAC,IAAI4D,aAAa,EAAEC,CAAC,IAAIC,SAAS,EAAE1D,CAAC,IAAI2D,sBAAsB,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEhF,CAAC,IAAIiF,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,qBAAqB,QAAQ,oBAAoB;AACz9B,SAASpF,CAAC,IAAIqF,oBAAoB,QAAQ,+BAA+B;AACzE,SAAS3G,CAAC,IAAI4G,wBAAwB,EAAEpE,CAAC,IAAIqE,uBAAuB,EAAEnC,CAAC,IAAIoC,QAAQ,EAAE1F,CAAC,IAAI2F,QAAQ,EAAEjE,CAAC,IAAIkE,cAAc,EAAEhE,CAAC,IAAIiE,YAAY,EAAE1G,CAAC,IAAI2G,OAAO,EAAEtG,CAAC,IAAIuG,OAAO,EAAE/G,CAAC,IAAIgH,WAAW,EAAEzD,CAAC,IAAI0D,QAAQ,QAAQ,wBAAwB;AACvO,SAAS/F,CAAC,IAAIgG,eAAe,QAAQ,yBAAyB;AAC9D,SAAStF,CAAC,IAAIuF,sBAAsB,EAAEnH,CAAC,IAAIoH,kBAAkB,EAAExG,CAAC,IAAIyG,oBAAoB,QAAQ,sBAAsB;AACtH,OAAO,qBAAqB;AAC5B,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AACzC,OAAO,yBAAyB;AAEhC,MAAMC,cAAc,GAAGA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,KAAK;EACpD,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,GAAGH,OAAO,EAAE;IACrC,OAAO,IAAI;EACf;EACA,IAAIE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,GAAGH,OAAO,EAAE;IACrC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,GAAGA,CAACC,QAAQ,EAAEJ,QAAQ,EAAEC,QAAQ,EAAEI,SAAS,KAAK;EAC/D;AACJ;AACA;AACA;EACI,IAAID,QAAQ,CAACE,GAAG,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,IAAID,SAAS,KAAKE,SAAS,IAAI,CAACF,SAAS,CAACG,QAAQ,CAACJ,QAAQ,CAACE,GAAG,CAAC,EAAE;IAC9D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIN,QAAQ,IAAIxF,QAAQ,CAAC4F,QAAQ,EAAEJ,QAAQ,CAAC,EAAE;IAC1C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,QAAQ,IAAIxF,OAAO,CAAC2F,QAAQ,EAAEH,QAAQ,CAAC,EAAE;IACzC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMQ,mBAAmB,GAAGA,CAACC,MAAM,EAAEN,QAAQ,EAAEO,WAAW,EAAEC,UAAU,EAAEZ,QAAQ,EAAEC,QAAQ,EAAEI,SAAS,KAAK;EACtG;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMQ,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;EACjF;AACJ;AACA;AACA;EACI,MAAMK,QAAQ,GAAGH,gBAAgB,CAACI,IAAI,CAAEC,KAAK,IAAKxG,SAAS,CAAC0F,QAAQ,EAAEc,KAAK,CAAC,CAAC,KAAKX,SAAS;EAC3F,MAAMY,OAAO,GAAGzG,SAAS,CAAC0F,QAAQ,EAAEQ,UAAU,CAAC;EAC/C,MAAMQ,QAAQ,GAAGjB,aAAa,CAACC,QAAQ,EAAEJ,QAAQ,EAAEC,QAAQ,EAAEI,SAAS,CAAC;EACvE;AACJ;AACA;AACA;EACI,OAAO;IACHe,QAAQ;IACRJ,QAAQ;IACRG,OAAO;IACPE,YAAY,EAAEL,QAAQ,GAAG,MAAM,GAAG,IAAI;IACtCM,SAAS,EAAEhH,oBAAoB,CAACoG,MAAM,EAAES,OAAO,EAAEf,QAAQ,CAAC;IAC1DmB,IAAI,EAAEnB,QAAQ,CAACE,GAAG,IAAI,IAAI,GAAG/F,MAAM,CAACmG,MAAM,EAAEN,QAAQ,CAAC,GAAG;EAC5D,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMoB,eAAe,GAAGA,CAACpB,QAAQ,EAAE;EAAEJ,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAC3D;EACA,IAAIH,cAAc,CAACM,QAAQ,CAACF,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,EAAE;IACnD,OAAO,IAAI;EACf;EACA;EACA;EACA,IAAKD,QAAQ,IAAIxF,QAAQ,CAAC4F,QAAQ,EAAEJ,QAAQ,CAAC,IAAMC,QAAQ,IAAIxF,OAAO,CAAC2F,QAAQ,EAAEH,QAAQ,CAAE,EAAE;IACzF,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMwB,mBAAmB,GAAGA,CAACrB,QAAQ,EAAEJ,QAAQ,EAAEC,QAAQ,KAAK;EAC1D,MAAMyB,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjH,gBAAgB,CAACyF,QAAQ,CAAC,CAAC,EAAE;IAAEE,GAAG,EAAE;EAAK,CAAC,CAAC;EAC7F,OAAOkB,eAAe,CAACE,SAAS,EAAE;IAC9B1B,QAAQ;IACRC;EACJ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM4B,mBAAmB,GAAGA,CAACzB,QAAQ,EAAEH,QAAQ,KAAK;EAChD,MAAM6B,SAAS,GAAGH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/G,YAAY,CAACuF,QAAQ,CAAC,CAAC,EAAE;IAAEE,GAAG,EAAE;EAAK,CAAC,CAAC;EACzF,OAAOkB,eAAe,CAACM,SAAS,EAAE;IAC9B7B;EACJ,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM8B,kBAAkB,GAAGA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,EAAE,KAAK;EAChE,IAAIpB,KAAK,CAACC,OAAO,CAACiB,gBAAgB,CAAC,EAAE;IACjC,MAAMG,qBAAqB,GAAGF,aAAa,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzD,MAAMC,iBAAiB,GAAGL,gBAAgB,CAACf,IAAI,CAAEqB,EAAE,IAAKA,EAAE,CAACC,IAAI,KAAKJ,qBAAqB,CAAC;IAC1F,IAAIE,iBAAiB,EAAE;MACnB,OAAO;QACHG,SAAS,EAAEH,iBAAiB,CAACG,SAAS;QACtCC,eAAe,EAAEJ,iBAAiB,CAACI;MACvC,CAAC;IACL;EACJ,CAAC,MACI;IACD;AACR;AACA;AACA;IACQ,IAAI;MACA,OAAOT,gBAAgB,CAACC,aAAa,CAAC;IAC1C,CAAC,CACD,OAAOrH,CAAC,EAAE;MACNvB,aAAa,CAAC,uGAAuG,EAAE6I,EAAE,EAAEtH,CAAC,CAAC;IACjI;EACJ;EACA,OAAO2F,SAAS;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMmC,sBAAsB,GAAGA,CAACR,EAAE,EAAES,aAAa,KAAK;EAClD,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAI,CAAC,CAACH,EAAE,GAAGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,IAAI,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,MACxI,CAACH,EAAE,GAAGF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,IAAI,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,YAAY,CAAC,KAC7I,CAACH,EAAE,GAAGH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,QAAQ,CAAC,KACzI,CAACD,EAAE,GAAGJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,IAAI,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,CAAC,EAAE;IAChJ1J,eAAe,CAAC,+EAA+E,EAAE2I,EAAE,CAAC;EACxG;AACJ,CAAC;AACD,MAAMiB,kCAAkC,GAAGA,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,KAAK;EAC5E;EACA,IAAI,CAACA,aAAa,EACd;EACJ;EACA,QAAQS,YAAY;IAChB,KAAK,MAAM;IACX,KAAK,YAAY;IACjB,KAAK,OAAO;IACZ,KAAK,MAAM;MACP,IAAIT,aAAa,CAACJ,IAAI,KAAKhC,SAAS,EAAE;QAClChH,eAAe,CAAC,kBAAkB6J,YAAY,yDAAyD,EAAElB,EAAE,CAAC;MAChH;MACA;IACJ,KAAK,MAAM;MACP,IAAIS,aAAa,CAACO,IAAI,KAAK3C,SAAS,EAAE;QAClChH,eAAe,CAAC,4EAA4E,EAAE2I,EAAE,CAAC;MACrG;MACA;IACJ,KAAK,WAAW;IAChB,KAAK,WAAW;MACZ,IAAIS,aAAa,CAACJ,IAAI,KAAKhC,SAAS,IAAIoC,aAAa,CAACO,IAAI,KAAK3C,SAAS,EAAE;QACtEhH,eAAe,CAAC,kBAAkB6J,YAAY,kFAAkF,EAAElB,EAAE,CAAC;MACzI;MACA;EACR;AACJ,CAAC;AAED,MAAMmB,cAAc,GAAG,+jWAA+jW;AACtlW,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,8nTAA8nT;AACppT,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBzL,gBAAgB,CAAC,IAAI,EAAEwL,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAGxL,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACyL,SAAS,GAAGzL,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC0L,cAAc,GAAG1L,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC2L,QAAQ,GAAG3L,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC4L,OAAO,GAAG5L,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC6L,QAAQ,GAAG7L,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8L,SAAS,GAAG9L,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC+L,OAAO,GAAG,UAAUC,WAAW,EAAE,EAAE;IACxC,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,yBAAyB,GAAG,MAAM;MACnC,MAAM;QAAEC,QAAQ;QAAEC;MAAM,CAAC,GAAG,IAAI;MAChC,IAAI,CAACD,QAAQ,IAAI1D,KAAK,CAACC,OAAO,CAAC0D,KAAK,CAAC,EAAE;QACnC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgBlL,eAAe,CAAC;AAChC;AACA,mBAAmBkL,KAAK,CAACC,GAAG,CAAE5J,CAAC,IAAK,IAAIA,CAAC,GAAG,CAAC,CAAC6J,IAAI,CAAC,IAAI,CAAC;AACxD,CAAC,EAAE,IAAI,CAACzC,EAAE,CAAC;MACC;IACJ,CAAC;IACD,IAAI,CAAC0C,QAAQ,GAAIH,KAAK,IAAK;MACvB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACX,SAAS,CAACe,IAAI,CAAC;QAAEJ;MAAM,CAAC,CAAC;IAClC,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACK,0BAA0B,GAAG,MAAM;MACpC,IAAIlC,EAAE;MACN,MAAM;QAAEmC;MAAa,CAAC,GAAG,IAAI;MAC7B,OAAO,CAACnC,EAAE,GAAG,IAAI,CAACoC,aAAa,CAAC,CAAC,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGmC,YAAY;IACpF,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,MAAM;QAAErE;MAAY,CAAC,GAAG,IAAI;MAC5B,OAAOG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW;IACpE,CAAC;IACD,IAAI,CAACsE,kBAAkB,GAAG,MAAM;MAC5B,MAAMC,cAAc,GAAG,IAAI,CAAChD,EAAE,CAACiD,OAAO,CAAC,wBAAwB,CAAC;MAChE,IAAID,cAAc,EAAE;QAChBA,cAAc,CAAC3F,OAAO,CAAC,CAAC;MAC5B;IACJ,CAAC;IACD,IAAI,CAAC6F,eAAe,GAAIlE,KAAK,IAAK;MAC9B,IAAI,CAACmE,YAAY,GAAG1D,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,KAAK,CAAC;IAChD,CAAC;IACD,IAAI,CAACoE,cAAc,GAAG,CAACpE,KAAK,EAAEqE,UAAU,GAAG,KAAK,KAAK;MACjD;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACC,QAAQ,EAAE;QACf;MACJ;MACA,MAAM;QAAEhB,QAAQ;QAAExE,QAAQ;QAAEC,QAAQ;QAAEU;MAAY,CAAC,GAAG,IAAI;MAC1D;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM8E,cAAc,GAAG1K,aAAa,CAACmG,KAAK,EAAElB,QAAQ,EAAEC,QAAQ,CAAC;MAC/D,IAAI,CAACmF,eAAe,CAACK,cAAc,CAAC;MACpC,IAAIjB,QAAQ,EAAE;QACV,MAAM3D,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;QACjF,IAAI4E,UAAU,EAAE;UACZ,IAAI,CAAC5E,WAAW,GAAGE,gBAAgB,CAAC6E,MAAM,CAAEpM,CAAC,IAAK,CAACoB,SAAS,CAACpB,CAAC,EAAEmM,cAAc,CAAC,CAAC;QACpF,CAAC,MACI;UACD,IAAI,CAAC9E,WAAW,GAAG,CAAC,GAAGE,gBAAgB,EAAE4E,cAAc,CAAC;QAC5D;MACJ,CAAC,MACI;QACD,IAAI,CAAC9E,WAAW,GAAGgB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE6D,cAAc,CAAC;MACxD;MACA,MAAME,iBAAiB,GAAG,IAAI,CAACzD,EAAE,CAAC0D,aAAa,CAAC,kBAAkB,CAAC,KAAK,IAAI;MAC5E,IAAID,iBAAiB,IAAI,IAAI,CAACE,kBAAkB,EAAE;QAC9C;MACJ;MACA,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC;IACD,IAAI,CAACC,2BAA2B,GAAG,MAAM;MACrC,MAAMC,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA,MAAMC,IAAI,GAAG,IAAI,CAAC/D,EAAE,CAACgE,UAAU;MAC/B;AACZ;AACA;AACA;MACY,MAAMC,YAAY,GAAGH,eAAe,CAACJ,aAAa,CAAC,gCAAgC,CAAC;MACpF;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMQ,sBAAsB,GAAIC,EAAE,IAAK;QACnC,IAAIzD,EAAE;QACN,MAAM0D,MAAM,GAAGD,EAAE,CAAC,CAAC,CAAC;QACpB;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAI,CAAC,CAACzD,EAAE,GAAG0D,MAAM,CAACC,QAAQ,MAAM,IAAI,IAAI3D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAACwF,eAAe,CAACQ,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;UAChJ;QACJ;QACA,IAAI,CAACC,eAAe,CAACP,YAAY,CAAC;MACtC,CAAC;MACD,MAAMQ,EAAE,GAAG,IAAIC,gBAAgB,CAACR,sBAAsB,CAAC;MACvDO,EAAE,CAACE,OAAO,CAACb,eAAe,EAAE;QAAEc,eAAe,EAAE,CAAC,OAAO,CAAC;QAAEC,iBAAiB,EAAE;MAAK,CAAC,CAAC;MACpF,IAAI,CAACC,iBAAiB,GAAG,MAAM;QAC3BL,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,UAAU,CAAC,CAAC;MAC3D,CAAC;MACD;AACZ;AACA;AACA;MACYjB,eAAe,CAACkB,gBAAgB,CAAC,SAAS,EAAGb,EAAE,IAAK;QAChD,MAAMc,aAAa,GAAGlB,IAAI,CAACkB,aAAa;QACxC,IAAI,CAACA,aAAa,IAAI,CAACA,aAAa,CAACX,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;UACrE;QACJ;QACA,MAAMvF,KAAK,GAAGlG,uBAAuB,CAACmM,aAAa,CAAC;QACpD,IAAIC,YAAY;QAChB,QAAQf,EAAE,CAACgB,GAAG;UACV,KAAK,WAAW;YACZhB,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGxL,WAAW,CAACsF,KAAK,CAAC;YACjC;UACJ,KAAK,SAAS;YACVmF,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGzL,eAAe,CAACuF,KAAK,CAAC;YACrC;UACJ,KAAK,YAAY;YACbmF,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG1L,UAAU,CAACwF,KAAK,CAAC;YAChC;UACJ,KAAK,WAAW;YACZmF,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG5L,cAAc,CAAC0F,KAAK,CAAC;YACpC;UACJ,KAAK,MAAM;YACPmF,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG9L,cAAc,CAAC4F,KAAK,CAAC;YACpC;UACJ,KAAK,KAAK;YACNmF,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAG/L,YAAY,CAAC6F,KAAK,CAAC;YAClC;UACJ,KAAK,QAAQ;YACTmF,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGf,EAAE,CAACkB,QAAQ,GAAGpM,eAAe,CAAC+F,KAAK,CAAC,GAAGvG,gBAAgB,CAACuG,KAAK,CAAC;YAC7E;UACJ,KAAK,UAAU;YACXmF,EAAE,CAACiB,cAAc,CAAC,CAAC;YACnBF,YAAY,GAAGf,EAAE,CAACkB,QAAQ,GAAGtM,WAAW,CAACiG,KAAK,CAAC,GAAGrG,YAAY,CAACqG,KAAK,CAAC;YACrE;UACJ;AACpB;AACA;AACA;AACA;AACA;UACoB;YACI;QACR;QACA;AAChB;AACA;AACA;QACgB,IAAIf,aAAa,CAACiH,YAAY,EAAE,IAAI,CAACpH,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAAC,EAAE;UAC3D;QACJ;QACA,IAAI,CAACmF,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyD,YAAY,CAAC,EAAE+B,YAAY,CAAC,CAAC;QACvF;AAChB;AACA;AACA;QACgBI,qBAAqB,CAAC,MAAM,IAAI,CAACd,eAAe,CAACP,YAAY,CAAC,CAAC;MACnE,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACO,eAAe,GAAIP,YAAY,IAAK;MACrC;AACZ;AACA;AACA;AACA;MACY,MAAMsB,OAAO,GAAGtB,YAAY,CAACuB,gBAAgB,CAAC,uBAAuB,CAAC;MACtE,MAAM;QAAEpH;MAAI,CAAC,GAAG,IAAI,CAAC+E,YAAY;MACjC,IAAI/E,GAAG,KAAK,IAAI,EAAE;QACd;MACJ;MACA;AACZ;AACA;AACA;MACY,MAAMqH,KAAK,GAAGxB,YAAY,CAACP,aAAa,CAAC,qCAAqC6B,OAAO,CAACG,MAAM,GAAGtH,GAAG,iBAAiB,CAAC;MACpH,IAAIqH,KAAK,EAAE;QACPA,KAAK,CAACE,KAAK,CAAC,CAAC;MACjB;IACJ,CAAC;IACD,IAAI,CAACC,eAAe,GAAG,MAAM;MACzB,MAAM;QAAEC,GAAG;QAAEhD;MAAa,CAAC,GAAG,IAAI;MAClC,IAAIgD,GAAG,KAAKxH,SAAS,EAAE;QACnB,IAAI,CAACP,QAAQ,GAAGO,SAAS;QACzB;MACJ;MACA,IAAI,CAACP,QAAQ,GAAGnE,aAAa,CAACkM,GAAG,EAAEhD,YAAY,CAAC;IACpD,CAAC;IACD,IAAI,CAACiD,eAAe,GAAG,MAAM;MACzB,MAAM;QAAEC,GAAG;QAAElD;MAAa,CAAC,GAAG,IAAI;MAClC,IAAIkD,GAAG,KAAK1H,SAAS,EAAE;QACnB,IAAI,CAACN,QAAQ,GAAGM,SAAS;QACzB;MACJ;MACA,IAAI,CAACN,QAAQ,GAAGnE,aAAa,CAACmM,GAAG,EAAElD,YAAY,CAAC;IACpD,CAAC;IACD,IAAI,CAACmD,0BAA0B,GAAG,MAAM;MACpC,MAAMlC,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMmC,MAAM,GAAGnC,eAAe,CAAC0B,gBAAgB,CAAC,iBAAiB,CAAC;MAClE,MAAMU,UAAU,GAAGD,MAAM,CAAC,CAAC,CAAC;MAC5B,MAAME,YAAY,GAAGF,MAAM,CAAC,CAAC,CAAC;MAC9B,MAAMG,QAAQ,GAAGH,MAAM,CAAC,CAAC,CAAC;MAC1B,MAAMI,IAAI,GAAGlO,UAAU,CAAC,IAAI,CAAC;MAC7B,MAAMmO,qBAAqB,GAAGD,IAAI,KAAK,KAAK,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,cAAc,GAAG,CAAC;MAChH;AACZ;AACA;AACA;AACA;AACA;MACYnQ,SAAS,CAAC,MAAM;QACZyN,eAAe,CAAC2C,UAAU,GAAGP,UAAU,CAACQ,WAAW,IAAInP,KAAK,CAAC,IAAI,CAACyI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/E,MAAM2G,eAAe,GAAI3H,KAAK,IAAK;UAC/B,MAAM4H,GAAG,GAAG9C,eAAe,CAAC+C,qBAAqB,CAAC,CAAC;UACnD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAMC,KAAK,GAAGhD,eAAe,CAAC2C,UAAU,IAAI,CAAC,GAAGP,UAAU,GAAGE,QAAQ;UACrE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAMW,QAAQ,GAAGD,KAAK,CAACD,qBAAqB,CAAC,CAAC;UAC9C,IAAIG,IAAI,CAACC,GAAG,CAACF,QAAQ,CAAC3M,CAAC,GAAGwM,GAAG,CAACxM,CAAC,CAAC,GAAG,CAAC,EAChC;UACJ;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAM;YAAE8M;UAAgB,CAAC,GAAG,IAAI;UAChC,IAAIA,eAAe,KAAK7I,SAAS,EAAE;YAC/B,OAAO;cAAEyI,KAAK,EAAEI,eAAe,CAACJ,KAAK;cAAE9I,IAAI,EAAEkJ,eAAe,CAAClJ,IAAI;cAAEI,GAAG,EAAE8I,eAAe,CAAC9I;YAAI,CAAC;UACjG;UACA;AACpB;AACA;AACA;AACA;AACA;UACoB,IAAI0I,KAAK,KAAKZ,UAAU,EAAE;YACtB,OAAOzN,gBAAgB,CAACuG,KAAK,CAAC;UAClC,CAAC,MACI,IAAI8H,KAAK,KAAKV,QAAQ,EAAE;YACzB,OAAOzN,YAAY,CAACqG,KAAK,CAAC;UAC9B,CAAC,MACI;YACD;UACJ;QACJ,CAAC;QACD,MAAMmI,iBAAiB,GAAGA,CAAA,KAAM;UAC5B,IAAIb,qBAAqB,EAAE;YACvBxC,eAAe,CAACsD,KAAK,CAACC,cAAc,CAAC,gBAAgB,CAAC;YACtDC,uBAAuB,GAAG,KAAK;UACnC;UACA;AACpB;AACA;AACA;UACoB,MAAMC,OAAO,GAAGZ,eAAe,CAAC,IAAI,CAACxD,YAAY,CAAC;UAClD,IAAI,CAACoE,OAAO,EACR;UACJ,MAAM;YAAET,KAAK;YAAE1I,GAAG;YAAEJ;UAAK,CAAC,GAAGuJ,OAAO;UACpC,IAAIjI,eAAe,CAAC;YAAEwH,KAAK;YAAE9I,IAAI;YAAEI,GAAG,EAAE;UAAK,CAAC,EAAE;YAC5CN,QAAQ,EAAE2B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC5B,QAAQ,CAAC,EAAE;cAAEM,GAAG,EAAE;YAAK,CAAC,CAAC;YACxEL,QAAQ,EAAE0B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC3B,QAAQ,CAAC,EAAE;cAAEK,GAAG,EAAE;YAAK,CAAC;UAC3E,CAAC,CAAC,EAAE;YACA;UACJ;UACA;AACpB;AACA;AACA;AACA;UACoB0F,eAAe,CAACsD,KAAK,CAACI,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC;UACvD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoBnR,SAAS,CAAC,MAAM;YACZ,IAAI,CAAC6M,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyD,YAAY,CAAC,EAAE;cAAE2D,KAAK;cAAE1I,GAAG,EAAEA,GAAG;cAAEJ;YAAK,CAAC,CAAC,CAAC;YACpG8F,eAAe,CAAC2C,UAAU,GAAGN,YAAY,CAACO,WAAW,IAAInP,KAAK,CAAC,IAAI,CAACyI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACjF8D,eAAe,CAACsD,KAAK,CAACC,cAAc,CAAC,UAAU,CAAC;YAChD,IAAI,IAAI,CAACI,yBAAyB,EAAE;cAChC,IAAI,CAACA,yBAAyB,CAAC,CAAC;YACpC;UACJ,CAAC,CAAC;QACN,CAAC;QACD;AAChB;AACA;AACA;QACgB,IAAIC,aAAa;QACjB;AAChB;AACA;AACA;AACA;QACgB,IAAIJ,uBAAuB,GAAG,KAAK;QACnC,MAAMK,cAAc,GAAGA,CAAA,KAAM;UACzB,IAAID,aAAa,EAAE;YACfE,YAAY,CAACF,aAAa,CAAC;UAC/B;UACA;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,CAACJ,uBAAuB,IAAIhB,qBAAqB,EAAE;YACnDxC,eAAe,CAACsD,KAAK,CAACI,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;YAC3DF,uBAAuB,GAAG,IAAI;UAClC;UACA;UACAI,aAAa,GAAGG,UAAU,CAACV,iBAAiB,EAAE,EAAE,CAAC;QACrD,CAAC;QACDrD,eAAe,CAACkB,gBAAgB,CAAC,QAAQ,EAAE2C,cAAc,CAAC;QAC1D,IAAI,CAACG,uBAAuB,GAAG,MAAM;UACjChE,eAAe,CAACiE,mBAAmB,CAAC,QAAQ,EAAEJ,cAAc,CAAC;QACjE,CAAC;MACL,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACK,2BAA2B,GAAG,MAAM;MACrC,MAAM;QAAEF,uBAAuB;QAAEhD;MAAkB,CAAC,GAAG,IAAI;MAC3D,IAAIgD,uBAAuB,KAAKzJ,SAAS,EAAE;QACvCyJ,uBAAuB,CAAC,CAAC;MAC7B;MACA,IAAIhD,iBAAiB,KAAKzG,SAAS,EAAE;QACjCyG,iBAAiB,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACmD,YAAY,GAAI1F,KAAK,IAAK;MAC3B,MAAM2F,QAAQ,GAAG3F,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKlE,SAAS,KAAK,CAACO,KAAK,CAACC,OAAO,CAAC0D,KAAK,CAAC,IAAIA,KAAK,CAACmD,MAAM,GAAG,CAAC,CAAC;MACrG,MAAMyC,cAAc,GAAGD,QAAQ,GAAGpO,SAAS,CAACyI,KAAK,CAAC,GAAG,IAAI,CAACM,YAAY;MACtE,MAAM;QAAE/E,QAAQ;QAAEC,QAAQ;QAAEoF,YAAY;QAAEnD;MAAG,CAAC,GAAG,IAAI;MACrD,IAAI,CAACqC,yBAAyB,CAAC,CAAC;MAChC;AACZ;AACA;AACA;MACY,IAAI,CAAC8F,cAAc,EAAE;QACjB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAID,QAAQ,EAAE;QACVnO,sBAAsB,CAACoO,cAAc,EAAErK,QAAQ,EAAEC,QAAQ,CAAC;MAC9D;MACA;AACZ;AACA;AACA;AACA;MACY,MAAMqK,WAAW,GAAGxJ,KAAK,CAACC,OAAO,CAACsJ,cAAc,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc;MACtF,MAAME,WAAW,GAAG7L,SAAS,CAAC4L,WAAW,EAAEtK,QAAQ,EAAEC,QAAQ,CAAC;MAC9D,MAAM;QAAE+I,KAAK;QAAE1I,GAAG;QAAEJ,IAAI;QAAEsK,IAAI;QAAEC;MAAO,CAAC,GAAGF,WAAW;MACtD,MAAMG,IAAI,GAAG9L,SAAS,CAAC4L,IAAI,CAAC;MAC5B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAIJ,QAAQ,EAAE;QACV,IAAItJ,KAAK,CAACC,OAAO,CAACsJ,cAAc,CAAC,EAAE;UAC/B,IAAI,CAAC1J,WAAW,GAAG,CAAC,GAAG0J,cAAc,CAAC;QAC1C,CAAC,MACI;UACD,IAAI,CAAC1J,WAAW,GAAG;YACfqI,KAAK;YACL1I,GAAG;YACHJ,IAAI;YACJsK,IAAI;YACJC,MAAM;YACNC;UACJ,CAAC;QACL;MACJ,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;QACgB,IAAI,CAAC/J,WAAW,GAAG,EAAE;MACzB;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMgK,cAAc,GAAI3B,KAAK,KAAKzI,SAAS,IAAIyI,KAAK,KAAK3D,YAAY,CAAC2D,KAAK,IAAM9I,IAAI,KAAKK,SAAS,IAAIL,IAAI,KAAKmF,YAAY,CAACnF,IAAK;MAClI,MAAM0K,aAAa,GAAG1I,EAAE,CAACsE,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC;MAC7D,MAAM;QAAEoE,WAAW;QAAEC;MAAiB,CAAC,GAAG,IAAI;MAC9C,IAAIC,8BAA8B,GAAG,IAAI;MACzC,IAAIjK,KAAK,CAACC,OAAO,CAACsJ,cAAc,CAAC,EAAE;QAC/B,MAAMW,UAAU,GAAGX,cAAc,CAAC,CAAC,CAAC,CAACrB,KAAK;QAC1C,KAAK,MAAMzG,IAAI,IAAI8H,cAAc,EAAE;UAC/B,IAAI9H,IAAI,CAACyG,KAAK,KAAKgC,UAAU,EAAE;YAC3BD,8BAA8B,GAAG,KAAK;YACtC;UACJ;QACJ;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIA,8BAA8B,EAAE;QAChC,IAAIF,WAAW,IAAIF,cAAc,IAAIC,aAAa,IAAI,CAACE,gBAAgB,EAAE;UACrE,IAAI,CAACG,aAAa,CAACV,WAAW,CAAC;QACnC,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoB,IAAI,CAACnF,eAAe,CAAC;YACjB4D,KAAK;YACL1I,GAAG;YACHJ,IAAI;YACJsK,IAAI;YACJC,MAAM;YACNC;UACJ,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;IACD,IAAI,CAACO,aAAa;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOZ,WAAW,EAAK;QACxC,MAAM;UAAElF;QAAa,CAAC,GAAGzB,KAAI;QAC7B;AACZ;AACA;AACA;AACA;AACA;QACYA,KAAI,CAACwF,eAAe,GAAGmB,WAAW;QAClC;AACZ;AACA;AACA;AACA;AACA;AACA;QACY,MAAMa,yBAAyB,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;UACvD1H,KAAI,CAAC+F,yBAAyB,GAAG2B,OAAO;QAC5C,CAAC,CAAC;QACF;AACZ;AACA;AACA;QACY,MAAMC,mBAAmB,GAAG/Q,QAAQ,CAAC+P,WAAW,EAAElF,YAAY,CAAC;QAC/DkG,mBAAmB,GAAG3H,KAAI,CAAClC,SAAS,CAAC,CAAC,GAAGkC,KAAI,CAAC9B,SAAS,CAAC,CAAC;QACzD,MAAMsJ,yBAAyB;QAC/BxH,KAAI,CAAC+F,yBAAyB,GAAGpJ,SAAS;QAC1CqD,KAAI,CAACwF,eAAe,GAAG7I,SAAS;MACpC,CAAC;MAAA,iBAAAiL,EAAA;QAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC3H,QAAQ,CAACa,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAAC+G,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC3H,OAAO,CAACY,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACuF,QAAQ,GAAG,MAAM;MAClB,OAAO,IAAI,CAAC3F,KAAK,IAAI,IAAI;IAC7B,CAAC;IACD,IAAI,CAAC3C,SAAS,GAAG,MAAM;MACnB,MAAMkE,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA,MAAMlE,SAAS,GAAGkE,eAAe,CAACJ,aAAa,CAAC,8BAA8B,CAAC;MAC/E,IAAI,CAAC9D,SAAS,EAAE;QACZ;MACJ;MACA,MAAM+J,IAAI,GAAG/J,SAAS,CAACgK,WAAW,GAAG,CAAC;MACtC9F,eAAe,CAAC+F,QAAQ,CAAC;QACrBC,GAAG,EAAE,CAAC;QACNH,IAAI,EAAEA,IAAI,IAAIpS,KAAK,CAAC,IAAI,CAACyI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC+J,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACvK,SAAS,GAAG,MAAM;MACnB,MAAMsE,eAAe,GAAG,IAAI,CAACA,eAAe;MAC5C,IAAI,CAACA,eAAe,EAAE;QAClB;MACJ;MACA,MAAMtE,SAAS,GAAGsE,eAAe,CAACJ,aAAa,CAAC,+BAA+B,CAAC;MAChF,IAAI,CAAClE,SAAS,EAAE;QACZ;MACJ;MACAsE,eAAe,CAAC+F,QAAQ,CAAC;QACrBC,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE,CAAC;QACPI,QAAQ,EAAE;MACd,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,sBAAsB,GAAG,MAAM;MAChC,IAAI,CAACpB,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAClD,CAAC;IACD,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACnK,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC0E,YAAY,GAAG;MAChB2D,KAAK,EAAE,CAAC;MACR1I,GAAG,EAAE,EAAE;MACPJ,IAAI,EAAE,IAAI;MACVsK,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE;IACV,CAAC;IACD,IAAI,CAACyB,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC/C,eAAe,GAAG7I,SAAS;IAChC,IAAI,CAAC6L,KAAK,GAAG,SAAS;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACjI,OAAO;IACxB,IAAI,CAAChD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACuB,aAAa,GAAGpC,SAAS;IAC9B,IAAI,CAACiF,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC8G,aAAa,GAAG/L,SAAS;IAC9B,IAAI,CAACwH,GAAG,GAAGxH,SAAS;IACpB,IAAI,CAAC0H,GAAG,GAAG1H,SAAS;IACpB,IAAI,CAAC6C,YAAY,GAAG,WAAW;IAC/B,IAAI,CAACmJ,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB,IAAI,CAACC,SAAS,GAAG,OAAO;IACxB,IAAI,CAACC,UAAU,GAAGnM,SAAS;IAC3B,IAAI,CAACoM,WAAW,GAAGpM,SAAS;IAC5B,IAAI,CAACF,SAAS,GAAGE,SAAS;IAC1B,IAAI,CAACqM,UAAU,GAAGrM,SAAS;IAC3B,IAAI,CAACsM,YAAY,GAAGtM,SAAS;IAC7B,IAAI,CAACG,MAAM,GAAG,SAAS;IACvB,IAAI,CAACoM,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,2BAA2B,GAAGxM,SAAS;IAC5C,IAAI,CAACiE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACxC,gBAAgB,GAAGzB,SAAS;IACjC,IAAI,CAACkE,KAAK,GAAGlE,SAAS;IACtB,IAAI,CAACyM,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACnH,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACoH,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,SAAS,GAAG5M,SAAS;IAC1B,IAAI,CAAC6M,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACAC,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAEpL,EAAE;MAAES,aAAa;MAAES;IAAa,CAAC,GAAG,IAAI;IAChDD,kCAAkC,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,CAAC;IACnED,sBAAsB,CAACR,EAAE,EAAES,aAAa,CAAC;EAC7C;EACA4K,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC3F,eAAe,CAAC,CAAC;EAC1B;EACA4F,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC1F,eAAe,CAAC,CAAC;EAC1B;EACA2F,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAEzL,EAAE;MAAES,aAAa;MAAES;IAAa,CAAC,GAAG,IAAI;IAChDD,kCAAkC,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,CAAC;EACvE;EACA,IAAIkI,WAAWA,CAAA,EAAG;IACd,MAAM;MAAEzH,YAAY;MAAEiK;IAAY,CAAC,GAAG,IAAI;IAC1C,MAAMO,mBAAmB,GAAGxK,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;IACnH,OAAOwK,mBAAmB,IAAI,CAACP,WAAW;EAC9C;EACAQ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,gBAAgB,GAAG3R,uBAAuB,CAAC,IAAI,CAACuQ,UAAU,CAAC;EACpE;EACAqB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,iBAAiB,GAAG7R,uBAAuB,CAAC,IAAI,CAACwQ,WAAW,CAAC;EACtE;EACAsB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACC,eAAe,GAAG/R,uBAAuB,CAAC,IAAI,CAACkE,SAAS,CAAC;EAClE;EACA8N,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,gBAAgB,GAAGjS,uBAAuB,CAAC,IAAI,CAACyQ,UAAU,CAAC;EACpE;EACAyB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,kBAAkB,GAAGnS,uBAAuB,CAAC,IAAI,CAAC0Q,YAAY,CAAC;EACxE;EACA;AACJ;AACA;EACU0B,YAAYA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA;MACjB,MAAM;QAAE1G;MAAM,CAAC,GAAG+J,MAAI;MACtB,IAAIA,MAAI,CAACpE,QAAQ,CAAC,CAAC,EAAE;QACjBoE,MAAI,CAACrE,YAAY,CAAC1F,KAAK,CAAC;MAC5B;MACA+J,MAAI,CAAChB,SAAS,CAAC,CAAC;MAChBgB,MAAI,CAACzK,cAAc,CAACc,IAAI,CAAC;QAAEJ;MAAM,CAAC,CAAC;IAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACUqB,OAAOA,CAAA,EAAuB;IAAA,IAAA2I,MAAA;IAAA,OAAAtD,iBAAA,YAAtBuD,YAAY,GAAG,KAAK;MAC9B,MAAM;QAAEC,gBAAgB;QAAEhO,WAAW;QAAE0M,WAAW;QAAEhI;MAAa,CAAC,GAAGoJ,MAAI;MACzE;AACR;AACA;MACQ,IAAI9N,WAAW,KAAKJ,SAAS,IAAI,CAACoO,gBAAgB,EAAE;QAChD,MAAMC,kBAAkB,GAAG9N,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC;QACrD,IAAIiO,kBAAkB,IAAIjO,WAAW,CAACiH,MAAM,KAAK,CAAC,EAAE;UAChD,IAAIyF,WAAW,EAAE;YACb;AACpB;AACA;AACA;AACA;YACoBoB,MAAI,CAAC7J,QAAQ,CAACvI,gBAAgB,CAACgJ,YAAY,CAAC,CAAC;UACjD,CAAC,MACI;YACDoJ,MAAI,CAAC7J,QAAQ,CAACrE,SAAS,CAAC;UAC5B;QACJ,CAAC,MACI;UACDkO,MAAI,CAAC7J,QAAQ,CAACvI,gBAAgB,CAACsE,WAAW,CAAC,CAAC;QAChD;MACJ;MACA,IAAI+N,YAAY,EAAE;QACdD,MAAI,CAACxJ,kBAAkB,CAAC,CAAC;MAC7B;IAAC,GAAAwG,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;AACA;AACA;EACUmD,KAAKA,CAACC,SAAS,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA5D,iBAAA;MACnB4D,MAAI,CAAC5E,YAAY,CAAC2E,SAAS,CAAC;IAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACUE,MAAMA,CAAA,EAAuB;IAAA,IAAAC,MAAA;IAAA,OAAA9D,iBAAA,YAAtBuD,YAAY,GAAG,KAAK;MAC7BO,MAAI,CAACpL,SAAS,CAACgB,IAAI,CAAC,CAAC;MACrB,IAAI6J,YAAY,EAAE;QACdO,MAAI,CAAChK,kBAAkB,CAAC,CAAC;MAC7B;IAAC,GAAAwG,KAAA,OAAAC,SAAA;EACL;EACA,IAAIiD,gBAAgBA,CAAA,EAAG;IACnB,MAAM;MAAEvL;IAAa,CAAC,GAAG,IAAI;IAC7B,OAAOA,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;EAClG;EACA8L,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,iBAAiB,GAAGtW,iBAAiB,CAAC,IAAI,CAACqJ,EAAE,CAAC,CAACkN,OAAO;EAC/D;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACF,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACA,iBAAiB,GAAG5O,SAAS;IACtC;EACJ;EACA+O,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACpH,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACnC,2BAA2B,CAAC,CAAC;EACtC;EACAwJ,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAErN,EAAE;MAAEsN;IAAuB,CAAC,GAAG,IAAI;IAC3C;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,eAAe,GAAIC,OAAO,IAAK;MACjC,MAAMrJ,EAAE,GAAGqJ,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI,CAACrJ,EAAE,CAACsJ,cAAc,EAAE;QACpB;MACJ;MACA,IAAI,CAACL,mBAAmB,CAAC,CAAC;MAC1B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY/W,SAAS,CAAC,MAAM;QACZ,IAAI,CAAC2J,EAAE,CAACsE,SAAS,CAACoJ,GAAG,CAAC,gBAAgB,CAAC;MAC3C,CAAC,CAAC;IACN,CAAC;IACD,MAAMC,SAAS,GAAG,IAAIC,oBAAoB,CAACL,eAAe,EAAE;MAAEM,SAAS,EAAE,IAAI;MAAE9J,IAAI,EAAE/D;IAAG,CAAC,CAAC;IAC1F;AACR;AACA;AACA;AACA;AACA;IACQpJ,GAAG,CAAC,MAAM+W,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAChJ,OAAO,CAAC2I,sBAAsB,CAAC,CAAC;IAC1G;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMQ,cAAc,GAAIN,OAAO,IAAK;MAChC,MAAMrJ,EAAE,GAAGqJ,OAAO,CAAC,CAAC,CAAC;MACrB,IAAIrJ,EAAE,CAACsJ,cAAc,EAAE;QACnB;MACJ;MACA,IAAI,CAACzF,2BAA2B,CAAC,CAAC;MAClC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACY,gBAAgB,GAAG,KAAK;MAC7BvS,SAAS,CAAC,MAAM;QACZ,IAAI,CAAC2J,EAAE,CAACsE,SAAS,CAACyJ,MAAM,CAAC,gBAAgB,CAAC;MAC9C,CAAC,CAAC;IACN,CAAC;IACD,MAAMC,QAAQ,GAAG,IAAIJ,oBAAoB,CAACE,cAAc,EAAE;MAAED,SAAS,EAAE,CAAC;MAAE9J,IAAI,EAAE/D;IAAG,CAAC,CAAC;IACrFpJ,GAAG,CAAC,MAAMoX,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACrJ,OAAO,CAAC2I,sBAAsB,CAAC,CAAC;IACvG;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMvJ,IAAI,GAAGhN,cAAc,CAAC,IAAI,CAACiJ,EAAE,CAAC;IACpC+D,IAAI,CAACiB,gBAAgB,CAAC,UAAU,EAAGb,EAAE,IAAKA,EAAE,CAAC8J,eAAe,CAAC,CAAC,CAAC;IAC/DlK,IAAI,CAACiB,gBAAgB,CAAC,SAAS,EAAGb,EAAE,IAAKA,EAAE,CAAC8J,eAAe,CAAC,CAAC,CAAC;EAClE;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAEhN,YAAY;MAAEkB,gBAAgB;MAAE0B,eAAe;MAAEhG,QAAQ;MAAEqN,WAAW;MAAEjE;IAAgB,CAAC,GAAG,IAAI;IACxG;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMiH,eAAe,GAAG,CAAChD,WAAW,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC7M,QAAQ,CAAC4C,YAAY,CAAC;IACjG,IAAIpD,QAAQ,KAAKO,SAAS,IAAI8P,eAAe,IAAIrK,eAAe,EAAE;MAC9D,MAAMqC,YAAY,GAAGrC,eAAe,CAACJ,aAAa,CAAC,gCAAgC,CAAC;MACpF;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIyC,YAAY,IAAIe,eAAe,KAAK7I,SAAS,EAAE;QAC/CyF,eAAe,CAAC2C,UAAU,GAAGN,YAAY,CAACO,WAAW,IAAInP,KAAK,CAAC,IAAI,CAACyI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACrF;IACJ;IACA,IAAIoC,gBAAgB,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACA,gBAAgB,GAAGlB,YAAY;MACpC;IACJ;IACA,IAAIA,YAAY,KAAKkB,gBAAgB,EAAE;MACnC;IACJ;IACA,IAAI,CAACA,gBAAgB,GAAGlB,YAAY;IACpC,IAAI,CAAC8G,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACoF,mBAAmB,CAAC,CAAC;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACxE,gBAAgB,GAAG,KAAK;IAC7BhS,GAAG,CAAC,MAAM;MACN,IAAI,CAACqL,SAAS,CAACU,IAAI,CAAC,CAAC;IACzB,CAAC,CAAC;EACN;EACAyL,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEpO,EAAE;MAAES,aAAa;MAAEX,gBAAgB;MAAEwC,QAAQ;MAAEpB,YAAY;MAAEiK;IAAY,CAAC,GAAG,IAAI;IACzF,IAAI7I,QAAQ,EAAE;MACV,IAAIpB,YAAY,KAAK,MAAM,EAAE;QACzB7J,eAAe,CAAC,oEAAoE,EAAE2I,EAAE,CAAC;MAC7F;MACA,IAAImL,WAAW,EAAE;QACb9T,eAAe,CAAC,mEAAmE,EAAE2I,EAAE,CAAC;MAC5F;IACJ;IACA,IAAIF,gBAAgB,KAAKzB,SAAS,EAAE;MAChC,IAAI6C,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW,EAAE;QACzF7J,eAAe,CAAC,wGAAwG,EAAE2I,EAAE,CAAC;MACjI;MACA,IAAImL,WAAW,EAAE;QACb9T,eAAe,CAAC,yEAAyE,EAAE2I,EAAE,CAAC;MAClG;IACJ;IACA,IAAIS,aAAa,EAAE;MACfQ,kCAAkC,CAACjB,EAAE,EAAEkB,YAAY,EAAET,aAAa,CAAC;MACnED,sBAAsB,CAACR,EAAE,EAAES,aAAa,CAAC;IAC7C;IACA,MAAMiK,UAAU,GAAI,IAAI,CAACwB,gBAAgB,GAAGjS,uBAAuB,CAAC,IAAI,CAACyQ,UAAU,CAAE;IACrF,MAAMC,YAAY,GAAI,IAAI,CAACyB,kBAAkB,GAAGnS,uBAAuB,CAAC,IAAI,CAAC0Q,YAAY,CAAE;IAC3F,MAAMF,WAAW,GAAI,IAAI,CAACqB,iBAAiB,GAAG7R,uBAAuB,CAAC,IAAI,CAACwQ,WAAW,CAAE;IACxF,MAAMD,UAAU,GAAI,IAAI,CAACoB,gBAAgB,GAAG3R,uBAAuB,CAAC,IAAI,CAACuQ,UAAU,CAAE;IACrF,MAAMrM,SAAS,GAAI,IAAI,CAAC6N,eAAe,GAAG/R,uBAAuB,CAAC,IAAI,CAACkE,SAAS,CAAE;IAClF,MAAMO,UAAU,GAAI,IAAI,CAACA,UAAU,GAAG5E,SAAS,CAACO,QAAQ,CAAC,CAAC,CAAE;IAC5D,IAAI,CAACuL,eAAe,CAAC,CAAC;IACtB,IAAI,CAACE,eAAe,CAAC,CAAC;IACtB,IAAI,CAACjD,YAAY,GAAGtI,mBAAmB,CAAC;MACpC2D,QAAQ,EAAEQ,UAAU;MACpB+L,WAAW;MACXtM,SAAS;MACTqM,UAAU;MACVE,UAAU;MACVC,YAAY;MACZ7M,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA;IACnB,CAAC,CAAC;IACF,IAAI,CAACkK,YAAY,CAAC,IAAI,CAAC1F,KAAK,CAAC;IAC7B,IAAI,CAAC+I,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,IAAI,CAACtJ,QAAQ,CAACW,IAAI,CAAC;MACf0L,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE,IAAI;MACd,sBAAsB,EAAE,IAAI,CAACpP;IACjC,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIqP,YAAYA,CAAA,EAAG;IACX,MAAM;MAAErP,QAAQ;MAAEoE,QAAQ;MAAEK,kBAAkB;MAAEoH;IAAgB,CAAC,GAAG,IAAI;IACxE;AACR;AACA;AACA;AACA;IACQ,MAAMyD,gBAAgB,GAAGtP,QAAQ,IAAIoE,QAAQ;IAC7C,MAAMG,iBAAiB,GAAG,IAAI,CAACzD,EAAE,CAAC0D,aAAa,CAAC,kBAAkB,CAAC,KAAK,IAAI;IAC5E,IAAI,CAACD,iBAAiB,IAAI,CAACE,kBAAkB,IAAI,CAACoH,eAAe,EAAE;MAC/D;IACJ;IACA,MAAM0D,gBAAgB,GAAGA,CAAA,KAAM;MAC3B,IAAI,CAAC9B,KAAK,CAAC,CAAC;MACZ,IAAI,CAACjK,QAAQ,CAACrE,SAAS,CAAC;IAC5B,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,OAAQ/H,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAkB,CAAC,EAAEpY,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAmB,CAAC,EAAEpY,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;QACjG,CAAC,yBAAyB,GAAG,IAAI;QACjC,CAAC,kBAAkB,GAAG,IAAI,CAAC3D;MAC/B;IAAE,CAAC,EAAEzU,CAAC,CAAC,MAAM,EAAE;MAAE6T,IAAI,EAAE;IAAU,CAAC,EAAE7T,CAAC,CAAC,aAAa,EAAE,IAAI,EAAEqN,kBAAkB,IAAKrN,CAAC,CAAC,YAAY,EAAE;MAAEqY,EAAE,EAAE,eAAe;MAAEzE,KAAK,EAAE,IAAI,CAACA,KAAK;MAAE0E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC9B,MAAM,CAAC,IAAI,CAAC;MAAE5N,QAAQ,EAAEsP;IAAiB,CAAC,EAAE,IAAI,CAACnE,UAAU,CAAE,EAAE/T,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAoC,CAAC,EAAE3D,eAAe,IAAKzU,CAAC,CAAC,YAAY,EAAE;MAAEqY,EAAE,EAAE,cAAc;MAAEzE,KAAK,EAAE,IAAI,CAACA,KAAK;MAAE0E,OAAO,EAAEA,CAAA,KAAMH,gBAAgB,CAAC,CAAC;MAAEvP,QAAQ,EAAEsP;IAAiB,CAAC,EAAE,IAAI,CAACjE,SAAS,CAAE,EAAE5G,kBAAkB,IAAKrN,CAAC,CAAC,YAAY,EAAE;MAAEqY,EAAE,EAAE,gBAAgB;MAAEzE,KAAK,EAAE,IAAI,CAACA,KAAK;MAAE0E,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChL,OAAO,CAAC,IAAI,CAAC;MAAE1E,QAAQ,EAAEsP;IAAiB,CAAC,EAAE,IAAI,CAAClE,QAAQ,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxmB;EACA;AACJ;AACA;EACIuE,iBAAiBA,CAACC,iBAAiB,GAAG,IAAI,CAAC5N,YAAY,EAAE;IACrD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM6N,WAAW,GAAGD,iBAAiB,KAAK,WAAW,GAC/C,CAAC,IAAI,CAACE,uBAAuB,CAACF,iBAAiB,CAAC,EAAE,IAAI,CAACG,uBAAuB,CAACH,iBAAiB,CAAC,CAAC,GAClG,CAAC,IAAI,CAACG,uBAAuB,CAACH,iBAAiB,CAAC,EAAE,IAAI,CAACE,uBAAuB,CAACF,iBAAiB,CAAC,CAAC;IACxG,OAAOxY,CAAC,CAAC,qBAAqB,EAAE,IAAI,EAAEyY,WAAW,CAAC;EACtD;EACAE,uBAAuBA,CAACH,iBAAiB,EAAE;IACvC,OAAOA,iBAAiB,KAAK,WAAW,IAAIA,iBAAiB,KAAK,WAAW,GACvE,IAAI,CAACI,8BAA8B,CAAC,CAAC,GACrC,IAAI,CAACC,iCAAiC,CAACL,iBAAiB,CAAC;EACnE;EACAI,8BAA8BA,CAAA,EAAG;IAC7B,MAAM;MAAErM,YAAY;MAAE3D,QAAQ;MAAEiE,YAAY;MAAE3E,MAAM;MAAEV,QAAQ;MAAEC,QAAQ;MAAEW,UAAU;MAAE0L;IAAc,CAAC,GAAG,IAAI;IAC5G,MAAMgF,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD;AACR;AACA;AACA;IACQ,MAAMyM,cAAc,GAAG5U,cAAc,CAAC0I,YAAY,CAAC;IACnD,MAAMmM,SAAS,GAAGD,cAAc,CAACA,cAAc,CAAC3J,MAAM,GAAG,CAAC,CAAC;IAC3D;AACR;AACA;IACQ2J,cAAc,CAAC,CAAC,CAAC,CAACjR,GAAG,GAAG,CAAC;IACzBkR,SAAS,CAAClR,GAAG,GAAGzD,iBAAiB,CAAC2U,SAAS,CAACxI,KAAK,EAAEwI,SAAS,CAACtR,IAAI,CAAC;IAClE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM6H,GAAG,GAAG/H,QAAQ,KAAKO,SAAS,IAAI9F,OAAO,CAACuF,QAAQ,EAAEuR,cAAc,CAAC,CAAC,CAAC,CAAC,GAAGvR,QAAQ,GAAGuR,cAAc,CAAC,CAAC,CAAC;IACzG,MAAMtJ,GAAG,GAAGhI,QAAQ,KAAKM,SAAS,IAAI/F,QAAQ,CAACyF,QAAQ,EAAEuR,SAAS,CAAC,GAAGvR,QAAQ,GAAGuR,SAAS;IAC1F,MAAMC,MAAM,GAAG1U,yBAAyB,CAAC2D,MAAM,EAAEE,UAAU,EAAEmH,GAAG,EAAEE,GAAG,EAAE,IAAI,CAACiG,eAAe,EAAE,IAAI,CAACF,iBAAiB,CAAC;IACpH,IAAI0D,KAAK,GAAGD,MAAM,CAACC,KAAK;IACxB,MAAMxQ,KAAK,GAAGuQ,MAAM,CAACvQ,KAAK;IAC1B,IAAIoL,aAAa,EAAE;MACfoF,KAAK,GAAGA,KAAK,CAAChN,GAAG,CAAC,CAACiN,UAAU,EAAEC,KAAK,KAAK;QACrC,MAAMC,cAAc,GAAG3Q,KAAK,CAAC0Q,KAAK,CAAC;QACnC,IAAIxQ,QAAQ;QACZ,IAAI;UACA;AACpB;AACA;AACA;AACA;UACoBA,QAAQ,GAAG,CAACkL,aAAa,CAACjQ,gBAAgB,CAACwV,cAAc,CAAC,CAAC;QAC/D,CAAC,CACD,OAAOjX,CAAC,EAAE;UACNvB,aAAa,CAAC,oGAAoG,EAAEuB,CAAC,CAAC;QAC1H;QACA,OAAO+G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+P,UAAU,CAAC,EAAE;UAAEvQ;QAAS,CAAC,CAAC;MACrE,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;IACQ,MAAM0Q,WAAW,GAAGzM,YAAY,CAAC/E,GAAG,KAAK,IAAI,GACvC,GAAG+E,YAAY,CAACnF,IAAI,IAAImF,YAAY,CAAC2D,KAAK,IAAI3D,YAAY,CAAC/E,GAAG,EAAE,GAChE,GAAGyE,YAAY,CAAC7E,IAAI,IAAI6E,YAAY,CAACiE,KAAK,IAAIjE,YAAY,CAACzE,GAAG,EAAE;IACtE,OAAQ9H,CAAC,CAAC,4BAA4B,EAAE;MAAEoY,KAAK,EAAE,aAAa;MAAExE,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEhL,QAAQ,EAAEA,QAAQ;MAAEsQ,KAAK,EAAEA,KAAK;MAAEjN,KAAK,EAAEqN,WAAW;MAAEC,WAAW,EAAG1L,EAAE,IAAK;QACtJ;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC2D,uBAAuB,EAAE;UAC9B,IAAI,CAACA,uBAAuB,CAAC,CAAC;QAClC;QACA,MAAM;UAAEvF;QAAM,CAAC,GAAG4B,EAAE,CAAC2L,MAAM;QAC3B,MAAMC,QAAQ,GAAG/Q,KAAK,CAACD,IAAI,CAAC,CAAC;UAAE+H,KAAK;UAAE1I,GAAG;UAAEJ;QAAK,CAAC,KAAKuE,KAAK,KAAK,GAAGvE,IAAI,IAAI8I,KAAK,IAAI1I,GAAG,EAAE,CAAC;QAC1F,IAAI,CAAC8E,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyD,YAAY,CAAC,EAAE4M,QAAQ,CAAC,CAAC;QAC9E,IAAI,CAAC3M,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAEW,QAAQ,CAAC,CAAC;QAC3E;QACA;QACA,IAAI,CAAC/J,0BAA0B,CAAC,CAAC;QACjC7B,EAAE,CAAC8J,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,CAAC;EACZ;EACAkB,iCAAiCA,CAACL,iBAAiB,EAAE;IACjD,MAAM;MAAE3L,YAAY;MAAEiH;IAAc,CAAC,GAAG,IAAI;IAC5C,MAAM4F,kBAAkB,GAAGlB,iBAAiB,KAAK,MAAM,IAAIA,iBAAiB,KAAK,MAAM;IACvF,MAAM7I,MAAM,GAAG+J,kBAAkB,GAC3BjV,kBAAkB,CAAC,IAAI,CAACyD,MAAM,EAAE2E,YAAY,EAAE,IAAI,CAACrF,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC+N,iBAAiB,CAAC,GACnG,EAAE;IACR,MAAMmE,gBAAgB,GAAGnB,iBAAiB,KAAK,MAAM;IACrD,IAAIoB,IAAI,GAAGD,gBAAgB,GACrBhV,gBAAgB,CAAC,IAAI,CAACuD,MAAM,EAAE2E,YAAY,EAAE,IAAI,CAACrF,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACiO,eAAe,CAAC,GAC/F,EAAE;IACR,IAAI5B,aAAa,EAAE;MACf8F,IAAI,GAAGA,IAAI,CAAC1N,GAAG,CAAE2N,SAAS,IAAK;QAC3B,MAAM;UAAE5N;QAAM,CAAC,GAAG4N,SAAS;QAC3B,MAAMC,QAAQ,GAAG,OAAO7N,KAAK,KAAK,QAAQ,GAAG8N,QAAQ,CAAC9N,KAAK,CAAC,GAAGA,KAAK;QACpE,MAAMoN,cAAc,GAAG;UACnB7I,KAAK,EAAE3D,YAAY,CAAC2D,KAAK;UACzB1I,GAAG,EAAEgS,QAAQ;UACbpS,IAAI,EAAEmF,YAAY,CAACnF;QACvB,CAAC;QACD,IAAIkB,QAAQ;QACZ,IAAI;UACA;AACpB;AACA;AACA;AACA;UACoBA,QAAQ,GAAG,CAACkL,aAAa,CAACjQ,gBAAgB,CAACwV,cAAc,CAAC,CAAC;QAC/D,CAAC,CACD,OAAOjX,CAAC,EAAE;UACNvB,aAAa,CAAC,oGAAoG,EAAEuB,CAAC,CAAC;QAC1H;QACA,OAAO+G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyQ,SAAS,CAAC,EAAE;UAAEjR;QAAS,CAAC,CAAC;MACpE,CAAC,CAAC;IACN;IACA,MAAMoR,iBAAiB,GAAGxB,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,MAAM;IACvF,MAAMyB,KAAK,GAAGD,iBAAiB,GACzBnV,iBAAiB,CAAC,IAAI,CAACqD,MAAM,EAAE,IAAI,CAACqE,YAAY,EAAE,IAAI,CAAC/E,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC6N,gBAAgB,CAAC,GACtG,EAAE;IACR;AACR;AACA;IACQ,MAAM4E,cAAc,GAAGnV,kBAAkB,CAAC,IAAI,CAACmD,MAAM,EAAE;MAAEsI,KAAK,EAAE,SAAS;MAAE1I,GAAG,EAAE;IAAU,CAAC,CAAC;IAC5F,IAAI2Q,WAAW,GAAG,EAAE;IACpB,IAAIyB,cAAc,EAAE;MAChBzB,WAAW,GAAG,CACV,IAAI,CAAC0B,uBAAuB,CAACxK,MAAM,CAAC,EACpC,IAAI,CAACyK,qBAAqB,CAACR,IAAI,CAAC,EAChC,IAAI,CAACS,sBAAsB,CAACJ,KAAK,CAAC,CACrC;IACL,CAAC,MACI;MACDxB,WAAW,GAAG,CACV,IAAI,CAAC2B,qBAAqB,CAACR,IAAI,CAAC,EAChC,IAAI,CAACO,uBAAuB,CAACxK,MAAM,CAAC,EACpC,IAAI,CAAC0K,sBAAsB,CAACJ,KAAK,CAAC,CACrC;IACL;IACA,OAAOxB,WAAW;EACtB;EACA2B,qBAAqBA,CAACR,IAAI,EAAE;IACxB,IAAIxP,EAAE;IACN,IAAIwP,IAAI,CAACxK,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO,EAAE;IACb;IACA,MAAM;MAAExG,QAAQ;MAAEiE;IAAa,CAAC,GAAG,IAAI;IACvC,MAAMiM,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,OAAQtM,CAAC,CAAC,4BAA4B,EAAE;MAAEoY,KAAK,EAAE,YAAY;MAAExE,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEhL,QAAQ,EAAEA,QAAQ;MAAEsQ,KAAK,EAAEU,IAAI;MAAE3N,KAAK,EAAE,CAAC7B,EAAE,GAAIyC,YAAY,CAAC/E,GAAG,KAAK,IAAI,GAAG+E,YAAY,CAAC/E,GAAG,GAAG,IAAI,CAACyE,YAAY,CAACzE,GAAI,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGrC,SAAS;MAAEwR,WAAW,EAAG1L,EAAE,IAAK;QACjQ;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC2D,uBAAuB,EAAE;UAC9B,IAAI,CAACA,uBAAuB,CAAC,CAAC;QAClC;QACA,IAAI,CAAC5E,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyD,YAAY,CAAC,EAAE;UAAE/E,GAAG,EAAE+F,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC9F,IAAI,CAACa,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAE;UAAEhR,GAAG,EAAE+F,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC3F;QACA;QACA,IAAI,CAACyD,0BAA0B,CAAC,CAAC;QACjC7B,EAAE,CAAC8J,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,CAAC;EACZ;EACAwC,uBAAuBA,CAACxK,MAAM,EAAE;IAC5B,IAAIA,MAAM,CAACP,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,EAAE;IACb;IACA,MAAM;MAAExG,QAAQ;MAAEiE;IAAa,CAAC,GAAG,IAAI;IACvC,MAAMiM,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,OAAQtM,CAAC,CAAC,4BAA4B,EAAE;MAAEoY,KAAK,EAAE,cAAc;MAAExE,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEhL,QAAQ,EAAEA,QAAQ;MAAEsQ,KAAK,EAAEvJ,MAAM;MAAE1D,KAAK,EAAEY,YAAY,CAAC2D,KAAK;MAAE+I,WAAW,EAAG1L,EAAE,IAAK;QAC/J;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC2D,uBAAuB,EAAE;UAC9B,IAAI,CAACA,uBAAuB,CAAC,CAAC;QAClC;QACA,IAAI,CAAC5E,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyD,YAAY,CAAC,EAAE;UAAE2D,KAAK,EAAE3C,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAChG,IAAI,CAACa,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAE;UAAEtI,KAAK,EAAE3C,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC7F;QACA;QACA,IAAI,CAACyD,0BAA0B,CAAC,CAAC;QACjC7B,EAAE,CAAC8J,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,CAAC;EACZ;EACA0C,sBAAsBA,CAACJ,KAAK,EAAE;IAC1B,IAAIA,KAAK,CAAC7K,MAAM,KAAK,CAAC,EAAE;MACpB,OAAO,EAAE;IACb;IACA,MAAM;MAAExG,QAAQ;MAAEiE;IAAa,CAAC,GAAG,IAAI;IACvC,MAAMiM,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,OAAQtM,CAAC,CAAC,4BAA4B,EAAE;MAAEoY,KAAK,EAAE,aAAa;MAAExE,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEhL,QAAQ,EAAEA,QAAQ;MAAEsQ,KAAK,EAAEe,KAAK;MAAEhO,KAAK,EAAEY,YAAY,CAACnF,IAAI;MAAE6R,WAAW,EAAG1L,EAAE,IAAK;QAC5J;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC2D,uBAAuB,EAAE;UAC9B,IAAI,CAACA,uBAAuB,CAAC,CAAC;QAClC;QACA,IAAI,CAAC5E,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyD,YAAY,CAAC,EAAE;UAAEnF,IAAI,EAAEmG,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC/F,IAAI,CAACa,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAE;UAAEpR,IAAI,EAAEmG,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC5F;QACA;QACA,IAAI,CAACyD,0BAA0B,CAAC,CAAC;QACjC7B,EAAE,CAAC8J,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,CAAC;EACZ;EACAe,uBAAuBA,CAACF,iBAAiB,EAAE;IACvC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,CAAC,CAACxQ,QAAQ,CAACwQ,iBAAiB,CAAC,EAAE;MACrE,OAAO,EAAE;IACb;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMM,UAAU,GAAG,IAAI,CAACtM,aAAa,CAAC,CAAC;IACvC,MAAM8N,mBAAmB,GAAGxB,UAAU,KAAK/Q,SAAS;IACpD,MAAM;MAAEwS,SAAS;MAAEC,WAAW;MAAEC;IAAc,CAAC,GAAGxV,kBAAkB,CAAC,IAAI,CAACiD,MAAM,EAAE,IAAI,CAAC2E,YAAY,EAAE,IAAI,CAAC8H,SAAS,EAAE2F,mBAAmB,GAAG,IAAI,CAAC9S,QAAQ,GAAGO,SAAS,EAAEuS,mBAAmB,GAAG,IAAI,CAAC7S,QAAQ,GAAGM,SAAS,EAAE,IAAI,CAAC6N,gBAAgB,EAAE,IAAI,CAACE,kBAAkB,CAAC;IACtQ,OAAO,CACH,IAAI,CAAC4E,sBAAsB,CAACH,SAAS,CAAC,EACtC,IAAI,CAACI,wBAAwB,CAACH,WAAW,CAAC,EAC1C,IAAI,CAACI,2BAA2B,CAACH,aAAa,CAAC,CAClD;EACL;EACAC,sBAAsBA,CAACH,SAAS,EAAE;IAC9B,MAAM;MAAE3R,QAAQ;MAAEiE;IAAa,CAAC,GAAG,IAAI;IACvC,IAAI0N,SAAS,CAACnL,MAAM,KAAK,CAAC,EACtB,OAAO,EAAE;IACb,MAAM0J,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,OAAQtM,CAAC,CAAC,4BAA4B,EAAE;MAAE4T,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEhL,QAAQ,EAAEA,QAAQ;MAAEqD,KAAK,EAAE6M,UAAU,CAAC9G,IAAI;MAAEkH,KAAK,EAAEqB,SAAS;MAAEM,YAAY,EAAE,IAAI;MAAEtB,WAAW,EAAG1L,EAAE,IAAK;QAC5J,IAAI,CAACjB,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyD,YAAY,CAAC,EAAE;UAAEmF,IAAI,EAAEnE,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC/F,IAAI,CAACa,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAE;UAAE9G,IAAI,EAAEnE,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC5F4B,EAAE,CAAC8J,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,CAAC;EACZ;EACAgD,wBAAwBA,CAACH,WAAW,EAAE;IAClC,MAAM;MAAE5R,QAAQ;MAAEiE;IAAa,CAAC,GAAG,IAAI;IACvC,IAAI2N,WAAW,CAACpL,MAAM,KAAK,CAAC,EACxB,OAAO,EAAE;IACb,MAAM0J,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,OAAQtM,CAAC,CAAC,4BAA4B,EAAE;MAAE4T,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEhL,QAAQ,EAAEA,QAAQ;MAAEqD,KAAK,EAAE6M,UAAU,CAAC7G,MAAM;MAAEiH,KAAK,EAAEsB,WAAW;MAAEK,YAAY,EAAE,IAAI;MAAEtB,WAAW,EAAG1L,EAAE,IAAK;QAChK,IAAI,CAACjB,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyD,YAAY,CAAC,EAAE;UAAEoF,MAAM,EAAEpE,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QACjG,IAAI,CAACa,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAE;UAAE7G,MAAM,EAAEpE,EAAE,CAAC2L,MAAM,CAACvN;QAAM,CAAC,CAAC,CAAC;QAC9F4B,EAAE,CAAC8J,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,CAAC;EACZ;EACAiD,2BAA2BA,CAACH,aAAa,EAAE;IACvC,MAAM;MAAE7R,QAAQ;MAAEiE;IAAa,CAAC,GAAG,IAAI;IACvC,IAAI4N,aAAa,CAACrL,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO,EAAE;IACb;IACA,MAAM0J,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,MAAMwO,cAAc,GAAG5V,oBAAoB,CAAC,IAAI,CAACgD,MAAM,CAAC;IACxD,OAAQlI,CAAC,CAAC,4BAA4B,EAAE;MAAE8Q,KAAK,EAAEgK,cAAc,GAAG;QAAEC,KAAK,EAAE;MAAK,CAAC,GAAG,CAAC,CAAC;MAAEnH,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEhL,QAAQ,EAAEA,QAAQ;MAAEqD,KAAK,EAAE6M,UAAU,CAAC5G,IAAI;MAAEgH,KAAK,EAAEuB,aAAa;MAAElB,WAAW,EAAG1L,EAAE,IAAK;QAC1L,MAAMmE,IAAI,GAAG1L,qBAAqB,CAACuG,YAAY,EAAEgB,EAAE,CAAC2L,MAAM,CAACvN,KAAK,CAAC;QACjE,IAAI,CAACW,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyD,YAAY,CAAC,EAAE;UAAEqF,IAAI,EAAErE,EAAE,CAAC2L,MAAM,CAACvN,KAAK;UAAE+F;QAAK,CAAC,CAAC,CAAC;QACrG,IAAI,CAAClF,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAE;UAAE5G,IAAI,EAAErE,EAAE,CAAC2L,MAAM,CAACvN,KAAK;UAAE+F;QAAK,CAAC,CAAC,CAAC;QAClGnE,EAAE,CAAC8J,eAAe,CAAC,CAAC;MACxB;IAAE,CAAC,CAAC;EACZ;EACAqD,eAAeA,CAACxC,iBAAiB,EAAE;IAC/B,MAAM;MAAEtQ;IAAO,CAAC,GAAG,IAAI;IACvB,MAAMgS,cAAc,GAAGnV,kBAAkB,CAACmD,MAAM,CAAC;IACjD,MAAM+S,WAAW,GAAGf,cAAc,GAAG,aAAa,GAAG,YAAY;IACjE,OAAQla,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;QAClB,CAAC,eAAe6C,WAAW,EAAE,GAAG;MACpC;IAAE,CAAC,EAAE,IAAI,CAAC1C,iBAAiB,CAACC,iBAAiB,CAAC,CAAC;EACvD;EACA;AACJ;AACA;EACI0C,oBAAoBA,CAACnL,IAAI,EAAE;IACvB,MAAM;MAAEnH;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMuS,YAAY,GAAGpL,IAAI,KAAK,KAAK,GAAGvO,WAAW,GAAGC,YAAY;IAChE,MAAM2Z,aAAa,GAAGrL,IAAI,KAAK,KAAK,GAAGxO,cAAc,GAAGI,cAAc;IACtE,MAAM0Z,iBAAiB,GAAGzS,QAAQ,IAAIK,mBAAmB,CAAC,IAAI,CAAC4D,YAAY,EAAE,IAAI,CAACrF,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAAC;IAC1G,MAAM6T,iBAAiB,GAAG1S,QAAQ,IAAIS,mBAAmB,CAAC,IAAI,CAACwD,YAAY,EAAE,IAAI,CAACpF,QAAQ,CAAC;IAC3F;IACA,MAAM8T,OAAO,GAAG,IAAI,CAAC7R,EAAE,CAAC8R,YAAY,CAAC,KAAK,CAAC,IAAIzT,SAAS;IACxD,OAAQ/H,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAkB,CAAC,EAAEpY,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAA0B,CAAC,EAAEpY,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAsB,CAAC,EAAEpY,CAAC,CAAC,UAAU,EAAE;MAAEyb,IAAI,EAAE,mBAAmB;MAAEC,GAAG,EAAGhS,EAAE,IAAM,IAAI,CAACiS,sBAAsB,GAAGjS,EAAG;MAAEkS,MAAM,EAAE,IAAI;MAAE,YAAY,EAAE,kBAAkB;MAAEpC,MAAM,EAAE,KAAK;MAAEqC,KAAK,EAAE,MAAM;MAAEjT,QAAQ,EAAEA,QAAQ;MAAE0P,OAAO,EAAEA,CAAA,KAAM;QAC9U,IAAIlO,EAAE;QACN,IAAI,CAACsJ,sBAAsB,CAAC,CAAC;QAC7B;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,MAAM;UAAEiI;QAAuB,CAAC,GAAG,IAAI;QACvC,IAAIA,sBAAsB,EAAE;UACxB,MAAMG,GAAG,GAAG,CAAC1R,EAAE,GAAGuR,sBAAsB,CAACjO,UAAU,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,aAAa,CAAC,cAAc,CAAC;UAC1H,IAAI0O,GAAG,EAAE;YACL,MAAMC,kBAAkB,GAAG,IAAI,CAACzJ,gBAAgB,GAAG,kBAAkB,GAAG,kBAAkB;YAC1FwJ,GAAG,CAACE,YAAY,CAAC,YAAY,EAAED,kBAAkB,CAAC;UACtD;QACJ;MACJ;IAAE,CAAC,EAAE/b,CAAC,CAAC,WAAW,EAAE,IAAI,EAAEsF,eAAe,CAAC,IAAI,CAAC4C,MAAM,EAAE,IAAI,CAAC2E,YAAY,CAAC,EAAE7M,CAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEic,IAAI,EAAE,IAAI,CAAC3J,gBAAgB,GAAG6I,YAAY,GAAGC,aAAa;MAAEc,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEnc,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAqB,CAAC,EAAEpY,CAAC,CAAC,aAAa,EAAE,IAAI,EAAEA,CAAC,CAAC,YAAY,EAAE;MAAE,YAAY,EAAE,gBAAgB;MAAE4I,QAAQ,EAAEyS,iBAAiB;MAAE/C,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACpP,SAAS,CAAC;IAAE,CAAC,EAAElJ,CAAC,CAAC,UAAU,EAAE;MAAEoc,GAAG,EAAEb,OAAO;MAAE,aAAa,EAAE,MAAM;MAAEc,IAAI,EAAE,WAAW;MAAEJ,IAAI,EAAE5a,WAAW;MAAE6a,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,EAAEnc,CAAC,CAAC,YAAY,EAAE;MAAE,YAAY,EAAE,YAAY;MAAE4I,QAAQ,EAAE0S,iBAAiB;MAAEhD,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChP,SAAS,CAAC;IAAE,CAAC,EAAEtJ,CAAC,CAAC,UAAU,EAAE;MAAEoc,GAAG,EAAEb,OAAO;MAAE,aAAa,EAAE,MAAM;MAAEc,IAAI,EAAE,WAAW;MAAEJ,IAAI,EAAE1a,cAAc;MAAE2a,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEnc,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE,uBAAuB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAEhT,aAAa,CAAC,IAAI,CAAC8C,MAAM,EAAE6H,IAAI,EAAE,IAAI,CAACuE,cAAc,GAAG,CAAC,CAAC,CAACpI,GAAG,CAAEtM,CAAC,IAAK;MACt3B,OAAOI,CAAC,CAAC,KAAK,EAAE;QAAEoY,KAAK,EAAE;MAAc,CAAC,EAAExY,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC,CAAC;EACR;EACA0c,WAAWA,CAAC9L,KAAK,EAAE9I,IAAI,EAAE;IACrB,MAAM;MAAEkB,QAAQ;MAAEoE;IAAS,CAAC,GAAG,IAAI;IACnC,MAAMuP,WAAW,GAAG,IAAI,CAACjH,gBAAgB,KAAKvN,SAAS,IAAI,IAAI,CAACuN,gBAAgB,CAACtN,QAAQ,CAACN,IAAI,CAAC;IAC/F,MAAM8U,YAAY,GAAG,IAAI,CAAChH,iBAAiB,KAAKzN,SAAS,IAAI,IAAI,CAACyN,iBAAiB,CAACxN,QAAQ,CAACwI,KAAK,CAAC;IACnG,MAAMiM,kBAAkB,GAAG,CAACF,WAAW,IAAI,CAACC,YAAY;IACxD,MAAME,kBAAkB,GAAG9T,QAAQ,IAAIoE,QAAQ;IAC/C,MAAM2P,aAAa,GAAG/T,QAAQ,IAC1BI,eAAe,CAAC;MACZwH,KAAK;MACL9I,IAAI;MACJI,GAAG,EAAE;IACT,CAAC,EAAE;MACC;MACA;MACA;MACAN,QAAQ,EAAE2B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC5B,QAAQ,CAAC,EAAE;QAAEM,GAAG,EAAE;MAAK,CAAC,CAAC;MACxEL,QAAQ,EAAE0B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC3B,QAAQ,CAAC,EAAE;QAAEK,GAAG,EAAE;MAAK,CAAC;IAC3E,CAAC,CAAC;IACN;IACA;IACA;IACA,MAAM8U,cAAc,GAAG,IAAI,CAAC/P,YAAY,CAAC2D,KAAK,KAAKA,KAAK,IAAI,IAAI,CAAC3D,YAAY,CAACnF,IAAI,KAAKA,IAAI;IAC3F,MAAMoR,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,OAAQtM,CAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,CAAC4c,cAAc,GAAG,MAAM,GAAG,IAAI;MAAExE,KAAK,EAAE;QAClE,gBAAgB,EAAE,IAAI;QACtB;QACA,yBAAyB,EAAE,CAACwE,cAAc,IAAID;MAClD;IAAE,CAAC,EAAE3c,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAsB,CAAC,EAAE5S,cAAc,CAACgL,KAAK,EAAE9I,IAAI,EAAE,IAAI,CAAC4M,cAAc,GAAG,CAAC,CAAC,CAACpI,GAAG,CAAC,CAAC2Q,UAAU,EAAEzD,KAAK,KAAK;MAChI,MAAM;QAAEtR,GAAG;QAAEgV;MAAU,CAAC,GAAGD,UAAU;MACrC,MAAM;QAAEnT,EAAE;QAAEF,gBAAgB;QAAEsK,aAAa;QAAE9H;MAAS,CAAC,GAAG,IAAI;MAC9D,MAAMqN,cAAc,GAAG;QAAE7I,KAAK;QAAE1I,GAAG;QAAEJ;MAAK,CAAC;MAC3C,MAAMqV,iBAAiB,GAAGjV,GAAG,KAAK,IAAI;MACtC,MAAM;QAAEU,QAAQ;QAAEG,OAAO;QAAEG,SAAS;QAAED,YAAY;QAAED,QAAQ,EAAEjB,aAAa;QAAEoB;MAAM,CAAC,GAAGd,mBAAmB,CAAC,IAAI,CAACC,MAAM,EAAEmR,cAAc,EAAE,IAAI,CAAClR,WAAW,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACZ,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACiO,eAAe,CAAC;MAC9N,MAAMjM,aAAa,GAAG5F,gBAAgB,CAACwV,cAAc,CAAC;MACtD,IAAI2D,gBAAgB,GAAGP,kBAAkB,IAAI9U,aAAa;MAC1D,IAAI,CAACqV,gBAAgB,IAAIlJ,aAAa,KAAK/L,SAAS,EAAE;QAClD,IAAI;UACA;AACpB;AACA;AACA;AACA;UACoBiV,gBAAgB,GAAG,CAAClJ,aAAa,CAACrK,aAAa,CAAC;QACpD,CAAC,CACD,OAAOrH,CAAC,EAAE;UACNvB,aAAa,CAAC,oGAAoG,EAAE6I,EAAE,EAAEtH,CAAC,CAAC;QAC9H;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,MAAM6a,mBAAmB,GAAGD,gBAAgB,IAAIN,kBAAkB;MAClE,MAAMxE,gBAAgB,GAAG8E,gBAAgB,IAAIN,kBAAkB;MAC/D,IAAIQ,SAAS,GAAGnV,SAAS;MACzB;AACZ;AACA;AACA;MACY,IAAIyB,gBAAgB,KAAKzB,SAAS,IAAI,CAACS,QAAQ,IAAIV,GAAG,KAAK,IAAI,EAAE;QAC7DoV,SAAS,GAAG3T,kBAAkB,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,EAAE,CAAC;MACvE;MACA,IAAIyT,SAAS,GAAGpV,SAAS;MACzB;MACA;MACA,IAAI,CAACgV,iBAAiB,EAAE;QACpBI,SAAS,GAAG,eAAe3U,QAAQ,GAAG,SAAS,GAAG,EAAE,GAAGG,OAAO,GAAG,QAAQ,GAAG,EAAE,GAAGqU,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAE;MAC1H;MACA,OAAQhd,CAAC,CAAC,KAAK,EAAE;QAAEoY,KAAK,EAAE;MAAuB,CAAC,EAAEpY,CAAC,CAAC,QAAQ,EAAE;QAC5D;QACA;QACA;QACA;QACA;QACA;QACA0b,GAAG,EAAGhS,EAAE,IAAK;UACT,IAAIA,EAAE,EAAE;YACJA,EAAE,CAACoH,KAAK,CAACI,WAAW,CAAC,OAAO,EAAE,GAAGgM,SAAS,GAAGA,SAAS,CAAClT,SAAS,GAAG,EAAE,EAAE,EAAE,WAAW,CAAC;YACrFN,EAAE,CAACoH,KAAK,CAACI,WAAW,CAAC,kBAAkB,EAAE,GAAGgM,SAAS,GAAGA,SAAS,CAACjT,eAAe,GAAG,EAAE,EAAE,EAAE,WAAW,CAAC;UAC1G;QACJ,CAAC;QAAEmT,QAAQ,EAAE,IAAI;QAAE,UAAU,EAAEtV,GAAG;QAAE,YAAY,EAAE0I,KAAK;QAAE,WAAW,EAAE9I,IAAI;QAAE,YAAY,EAAE0R,KAAK;QAAE,kBAAkB,EAAE0D,SAAS;QAAElU,QAAQ,EAAEsP,gBAAgB;QAAEE,KAAK,EAAE;UAC/J,sBAAsB,EAAE2E,iBAAiB;UACzC,cAAc,EAAE,IAAI;UACpB,qBAAqB,EAAEvU,QAAQ;UAC/B,0BAA0B,EAAEyU,mBAAmB;UAC/C,oBAAoB,EAAEtU;QAC1B,CAAC;QAAE8S,IAAI,EAAE0B,SAAS;QAAE,aAAa,EAAEJ,iBAAiB,GAAG,MAAM,GAAG,IAAI;QAAE,eAAe,EAAElU,YAAY;QAAE,YAAY,EAAEC,SAAS;QAAEwP,OAAO,EAAEA,CAAA,KAAM;UACzI,IAAIyE,iBAAiB,EAAE;YACnB;UACJ;UACA,IAAI,CAACnQ,eAAe,CAACzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyD,YAAY,CAAC,EAAE;YAAE2D,KAAK;YAC5E1I,GAAG;YACHJ;UAAK,CAAC,CAAC,CAAC;UACZ;UACA,IAAIsE,QAAQ,EAAE;YACV,IAAI,CAACc,cAAc,CAAC;cAChB0D,KAAK;cACL1I,GAAG;cACHJ;YACJ,CAAC,EAAEc,QAAQ,CAAC;UAChB,CAAC,MACI;YACD,IAAI,CAACsE,cAAc,CAAC3D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,EAAE;cAAEtI,KAAK;cACpE1I,GAAG;cACHJ;YAAK,CAAC,CAAC,CAAC;UAChB;QACJ;MACJ,CAAC,EAAEqB,IAAI,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC;EACR;EACAsU,kBAAkBA,CAAA,EAAG;IACjB,OAAQrd,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE,6BAA6B;MAAEsD,GAAG,EAAGhS,EAAE,IAAM,IAAI,CAAC8D,eAAe,GAAG9D,EAAG;MAAE0T,QAAQ,EAAE;IAAI,CAAC,EAAEjZ,cAAc,CAAC,IAAI,CAAC0I,YAAY,EAAE,IAAI,CAAC+D,eAAe,CAAC,CAAC1E,GAAG,CAAC,CAAC;MAAEsE,KAAK;MAAE9I;IAAK,CAAC,KAAK;MACjM,OAAO,IAAI,CAAC4U,WAAW,CAAC9L,KAAK,EAAE9I,IAAI,CAAC;IACxC,CAAC,CAAC,CAAC;EACP;EACA4V,cAAcA,CAACvN,IAAI,EAAE;IACjB,OAAQ/P,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE,mBAAmB;MAAEvJ,GAAG,EAAE;IAAoB,CAAC,EAAE,IAAI,CAACqM,oBAAoB,CAACnL,IAAI,CAAC,EAAE,IAAI,CAACsN,kBAAkB,CAAC,CAAC,CAAC;EAC1I;EACAE,eAAeA,CAAA,EAAG;IACd,MAAMC,mBAAmB,GAAG,IAAI,CAAC9T,EAAE,CAAC0D,aAAa,CAAC,qBAAqB,CAAC,KAAK,IAAI;IACjF,IAAI,CAACoQ,mBAAmB,IAAI,CAAC,IAAI,CAAC9I,oBAAoB,EAAE;MACpD;IACJ;IACA,OAAO1U,CAAC,CAAC,MAAM,EAAE;MAAE6T,IAAI,EAAE;IAAa,CAAC,EAAE,MAAM,CAAC;EACpD;EACA4J,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAChB,MAAM;MAAE9U,QAAQ;MAAE+L,SAAS;MAAEhB,iBAAiB;MAAEzL,MAAM;MAAEiC;IAAc,CAAC,GAAG,IAAI;IAC9E,MAAMwT,iBAAiB,GAAGjY,YAAY,CAACwC,MAAM,EAAEyM,SAAS,CAAC;IACzD,MAAMmE,UAAU,GAAG,IAAI,CAACxM,0BAA0B,CAAC,CAAC;IACpD,OAAO,CACHtM,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAc,CAAC,EAAE,IAAI,CAACmF,eAAe,CAAC,CAAC,CAAC,EAC1Dvd,CAAC,CAAC,QAAQ,EAAE;MAAEoY,KAAK,EAAE;QACb,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAEzE;MACxB,CAAC;MAAE8H,IAAI,EAAE,cAAc9H,iBAAiB,GAAG,SAAS,GAAG,EAAE,EAAE;MAAE,eAAe,EAAE,OAAO;MAAE,eAAe,EAAE,MAAM;MAAE/K,QAAQ,EAAEA,QAAQ;MAAE0P,OAAO;QAAA,IAAAsF,KAAA,GAAAjL,iBAAA,CAAE,WAAO9E,EAAE,EAAK;UACvJ,MAAM;YAAEgQ;UAAW,CAAC,GAAGH,MAAI;UAC3B,IAAIG,UAAU,EAAE;YACZH,MAAI,CAAC/J,iBAAiB,GAAG,IAAI;YAC7BkK,UAAU,CAAC/W,OAAO,CAAC,IAAIgX,WAAW,CAAC,iBAAiB,EAAE;cAClDtE,MAAM,EAAE;gBACJuE,eAAe,EAAElQ,EAAE,CAACmQ;cACxB;YACJ,CAAC,CAAC,CAAC;YACH,MAAMH,UAAU,CAACI,aAAa,CAAC,CAAC;YAChCP,MAAI,CAAC/J,iBAAiB,GAAG,KAAK;UAClC;QACJ,CAAC;QAAA,gBAZmI2E,OAAOA,CAAA4F,GAAA;UAAA,OAAAN,KAAA,CAAA3K,KAAA,OAAAC,SAAA;QAAA;MAAA;IAYzI,CAAC,EAAEtN,gBAAgB,CAACsC,MAAM,EAAE4Q,UAAU,EAAE6E,iBAAiB,EAAExT,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,IAAI,CAAC,CAAC,EACnJ1K,CAAC,CAAC,aAAa,EAAE;MAAEme,SAAS,EAAE,QAAQ;MAAEC,WAAW,EAAE,IAAI;MAAEC,YAAY,EAAE,CAAC;MAAEC,KAAK,EAAE,KAAK;MAAEC,aAAa,EAAG1Q,EAAE,IAAK;QACzG;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;QACoB,MAAM2Q,IAAI,GAAG3Q,EAAE,CAACmQ,MAAM,CAAC9O,gBAAgB,CAAC,4BAA4B,CAAC;QACrE;QACAsP,IAAI,CAACC,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,wBAAwB,CAAC,CAAC,CAAC;MACzD,CAAC;MAAE7N,KAAK,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,aAAa,EAAE;MACnB,CAAC;MACD;MACA;MACA8N,cAAc,EAAE,IAAI;MAAElD,GAAG,EAAGhS,EAAE,IAAM,IAAI,CAACmU,UAAU,GAAGnU;IAAI,CAAC,EAAE,IAAI,CAAC6O,iBAAiB,CAAC,MAAM,CAAC,CAAC,CACnG;EACL;EACAsG,yBAAyBA,CAAA,EAAG;IACxB,IAAIzU,EAAE;IACN,MAAM;MAAEjC,WAAW;MAAEgC,aAAa;MAAE6B,QAAQ;MAAEuI;IAA4B,CAAC,GAAG,IAAI;IAClF,MAAMhM,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC;IAC1C,IAAI2W,UAAU;IACd,IAAI9S,QAAQ,IAAIzD,OAAO,IAAIJ,WAAW,CAACiH,MAAM,KAAK,CAAC,EAAE;MACjD0P,UAAU,GAAG,GAAG3W,WAAW,CAACiH,MAAM,OAAO,CAAC,CAAC;MAC3C,IAAImF,2BAA2B,KAAKxM,SAAS,EAAE;QAC3C,IAAI;UACA+W,UAAU,GAAGvK,2BAA2B,CAAC1Q,gBAAgB,CAACsE,WAAW,CAAC,CAAC;QAC3E,CAAC,CACD,OAAO/F,CAAC,EAAE;UACNvB,aAAa,CAAC,uDAAuD,EAAEuB,CAAC,CAAC;QAC7E;MACJ;IACJ,CAAC,MACI;MACD;MACA0c,UAAU,GAAGhZ,oBAAoB,CAAC,IAAI,CAACoC,MAAM,EAAE,IAAI,CAACoE,0BAA0B,CAAC,CAAC,EAAE,CAAClC,EAAE,GAAGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACJ,IAAI,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;QAAE2U,OAAO,EAAE,OAAO;QAAEvO,KAAK,EAAE,OAAO;QAAE1I,GAAG,EAAE;MAAU,CAAC,CAAC;IACpQ;IACA,OAAOgX,UAAU;EACrB;EACAE,YAAYA,CAACC,kBAAkB,GAAG,IAAI,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACxV,EAAE,CAAC0D,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;IACxE,IAAI,CAAC8R,eAAe,IAAI,CAAC,IAAI,CAAC1K,gBAAgB,EAAE;MAC5C;IACJ;IACA,OAAQxU,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAkB,CAAC,EAAEpY,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAiB,CAAC,EAAEpY,CAAC,CAAC,MAAM,EAAE;MAAE6T,IAAI,EAAE;IAAQ,CAAC,EAAE,aAAa,CAAC,CAAC,EAAEoL,kBAAkB,IAAIjf,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAyB,CAAC,EAAE,IAAI,CAACyG,yBAAyB,CAAC,CAAC,CAAC,CAAC;EAC7O;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,UAAUA,CAAA,EAAG;IACT,MAAM;MAAEvU;IAAa,CAAC,GAAG,IAAI;IAC7B,MAAMwU,oBAAoB,GAAGxU,YAAY,KAAK,MAAM;IACpD,OAAQ5K,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAgB,CAAC,EAAEgH,oBAAoB,GAAG,IAAI,CAAC7G,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACkF,iBAAiB,CAAC,CAAC,CAAC;EAC5H;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI4B,iCAAiCA,CAAA,EAAG;IAChC,OAAOrf,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE;IAAgB,CAAC,EAAE,IAAI,CAAC4C,eAAe,CAAC,YAAY,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;EACIsE,cAAcA,CAACvP,IAAI,EAAE;IACjB,MAAM;MAAEnF,YAAY;MAAEiK;IAAY,CAAC,GAAG,IAAI;IAC1C;AACR;AACA;AACA;IACQ,MAAM0K,eAAe,GAAG3U,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;IAC/G,IAAIiK,WAAW,IAAI0K,eAAe,EAAE;MAChC,OAAO,CAAC,IAAI,CAACP,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAChE,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC/C,YAAY,CAAC,CAAC,CAAC;IAClF;IACA,QAAQrN,YAAY;MAChB,KAAK,WAAW;QACZ,OAAO,CACH,IAAI,CAACoU,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC1B,cAAc,CAACvN,IAAI,CAAC,EACzB,IAAI,CAACsP,iCAAiC,CAAC,CAAC,EACxC,IAAI,CAACF,UAAU,CAAC,CAAC,EACjB,IAAI,CAAClH,YAAY,CAAC,CAAC,CACtB;MACL,KAAK,WAAW;QACZ,OAAO,CACH,IAAI,CAAC+G,YAAY,CAAC,CAAC,EACnB,IAAI,CAACG,UAAU,CAAC,CAAC,EACjB,IAAI,CAAC7B,cAAc,CAACvN,IAAI,CAAC,EACzB,IAAI,CAACsP,iCAAiC,CAAC,CAAC,EACxC,IAAI,CAACpH,YAAY,CAAC,CAAC,CACtB;MACL,KAAK,MAAM;QACP,OAAO,CAAC,IAAI,CAAC+G,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAACG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAClH,YAAY,CAAC,CAAC,CAAC;MAC7E,KAAK,OAAO;MACZ,KAAK,YAAY;MACjB,KAAK,MAAM;QACP,OAAO,CAAC,IAAI,CAAC+G,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAChE,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC/C,YAAY,CAAC,CAAC,CAAC;MAClF;QACI,OAAO,CACH,IAAI,CAAC+G,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC1B,cAAc,CAACvN,IAAI,CAAC,EACzB,IAAI,CAACsP,iCAAiC,CAAC,CAAC,EACxC,IAAI,CAACpH,YAAY,CAAC,CAAC,CACtB;IACT;EACJ;EACAuH,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE3L,IAAI;MAAE5H,KAAK;MAAErD,QAAQ;MAAEc,EAAE;MAAEkK,KAAK;MAAE5G,QAAQ;MAAEsF,gBAAgB;MAAEuC,WAAW;MAAEjK,YAAY;MAAEgK,IAAI;MAAEvC;IAAa,CAAC,GAAG,IAAI;IAC5H,MAAMtC,IAAI,GAAGlO,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4d,0BAA0B,GAAG7U,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,YAAY;IACvH,MAAM8U,sBAAsB,GAAGpN,gBAAgB,IAAImN,0BAA0B;IAC7E,MAAME,mBAAmB,GAAGrN,gBAAgB,IAAI,CAACmN,0BAA0B;IAC3E,MAAMrK,mBAAmB,GAAGxK,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,WAAW;IACnH,MAAM2U,eAAe,GAAGnK,mBAAmB,IAAIP,WAAW;IAC1DtU,iBAAiB,CAAC,IAAI,EAAEmJ,EAAE,EAAEmK,IAAI,EAAE7N,WAAW,CAACiG,KAAK,CAAC,EAAErD,QAAQ,CAAC;IAC/D,OAAQ5I,CAAC,CAACE,IAAI,EAAE;MAAE2O,GAAG,EAAE,0CAA0C;MAAE,eAAe,EAAEjG,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEuK,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEgF,KAAK,EAAEjP,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjI,kBAAkB,CAACyS,KAAK,EAAE;QACrM,CAAC7D,IAAI,GAAG,IAAI;QACZ,CAAC,mBAAmB,GAAG/C,QAAQ;QAC/B,CAAC,mBAAmB,GAAGpE,QAAQ;QAC/B,qBAAqB,EAAE8W,sBAAsB;QAC7C,wBAAwB,EAAEC,mBAAmB;QAC7C,CAAC,yBAAyB/U,YAAY,EAAE,GAAG,IAAI;QAC/C,CAAC,iBAAiBgK,IAAI,EAAE,GAAG,IAAI;QAC/B,CAAC,uBAAuB,GAAG2K,eAAe;QAC1C,CAAC,eAAe,GAAGlN;MACvB,CAAC,CAAC;IAAE,CAAC,EAAErS,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE,sBAAsB;MAAEsD,GAAG,EAAGhS,EAAE,IAAM,IAAI,CAACsN,sBAAsB,GAAGtN;IAAI,CAAC,CAAC,EAAE,IAAI,CAAC4V,cAAc,CAACvP,IAAI,CAAC,CAAC;EACxL;EACA,IAAIrG,EAAEA,CAAA,EAAG;IAAE,OAAOtJ,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwf,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,eAAe,EAAE,CAAC,sBAAsB,CAAC;MACzC,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,YAAY,EAAE,CAAC,mBAAmB,CAAC;MACnC,aAAa,EAAE,CAAC,oBAAoB,CAAC;MACrC,WAAW,EAAE,CAAC,kBAAkB,CAAC;MACjC,YAAY,EAAE,CAAC,mBAAmB,CAAC;MACnC,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAI/T,WAAW,GAAG,CAAC;AACnBZ,QAAQ,CAAC6F,KAAK,GAAG;EACb+O,GAAG,EAAE/U,oBAAoB;EACzBgV,EAAE,EAAE9U;AACR,CAAC;;AAED;AACA;AACA;AACA,MAAM+U,iBAAiB,GAAIC,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAG/Y,eAAe,CAAC,CAAC;EACvC,MAAMgZ,iBAAiB,GAAGhZ,eAAe,CAAC,CAAC;EAC3C,MAAMiZ,gBAAgB,GAAGjZ,eAAe,CAAC,CAAC;EAC1CgZ,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAAC5S,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDiT,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCJ,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAAC5S,aAAa,CAAC,iBAAiB,CAAC,CAAC,CACnDiT,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC9D,OAAOJ,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBQ,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACR,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMQ,iBAAiB,GAAIX,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAG/Y,eAAe,CAAC,CAAC;EACvC,MAAMgZ,iBAAiB,GAAGhZ,eAAe,CAAC,CAAC;EAC3C,MAAMiZ,gBAAgB,GAAGjZ,eAAe,CAAC,CAAC;EAC1CgZ,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAAC5S,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDiT,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,IAAI,CAAC;EACvDF,gBAAgB,CACXC,UAAU,CAACJ,MAAM,CAAC5S,aAAa,CAAC,iBAAiB,CAAC,CAAC,CACnDiT,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC9D,OAAOJ,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBQ,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACR,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMS,YAAY,GAAG,ykLAAykL;AAC9lL,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,61KAA61K;AACj3K,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjB9V,WAAWA,CAACC,OAAO,EAAE;IACjBxL,gBAAgB,CAAC,IAAI,EAAEwL,OAAO,CAAC;IAC/B,IAAI,CAAC8V,UAAU,GAAGphB,WAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACqhB,WAAW,GAAGrhB,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACshB,WAAW,GAAGthB,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACuhB,UAAU,GAAGvhB,WAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACwhB,mBAAmB,GAAGxhB,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACyhB,oBAAoB,GAAGzhB,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC0hB,oBAAoB,GAAG1hB,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC2hB,mBAAmB,GAAG3hB,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC4hB,kBAAkB,GAAGjb,wBAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAACkb,cAAc,GAAGnb,oBAAoB,CAAC,CAAC;IAC5C,IAAI,CAACob,iBAAiB,GAAGlb,uBAAuB,CAAC,CAAC;IAClD,IAAI,CAACmb,aAAa,GAAG,MAAM;MACvB,IAAI,CAAC7a,OAAO,CAACgB,SAAS,EAAErB,QAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACmb,qBAAqB,GAAIhU,EAAE,IAAK;MACjC,MAAMiU,IAAI,GAAGjU,EAAE,CAAC2L,MAAM,CAACsI,IAAI;MAC3B,IAAInb,QAAQ,CAACmb,IAAI,CAAC,EAAE;QAChB,MAAMC,YAAY,GAAG,IAAI,CAACC,OAAO,CAACvZ,IAAI,CAAE7G,CAAC,IAAKA,CAAC,CAACkgB,IAAI,KAAK,QAAQ,CAAC;QAClE,IAAI,CAACG,iBAAiB,CAACF,YAAY,CAAC;MACxC;IACJ,CAAC;IACD,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC7D,YAAY,GAAGtW,SAAS;IAC7B,IAAI,CAACoa,QAAQ,GAAGpa,SAAS;IACzB,IAAI,CAACqa,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAGva,SAAS;IAC/B,IAAI,CAACwa,cAAc,GAAGxa,SAAS;IAC/B,IAAI,CAACia,OAAO,GAAG,EAAE;IACjB,IAAI,CAACQ,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAG1a,SAAS;IACzB,IAAI,CAAC0Y,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACiC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,cAAc,GAAG9a,SAAS;IAC/B,IAAI,CAAC+a,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAGhb,SAAS;EAC5B;EACAib,cAAcA,CAACC,QAAQ,EAAElV,QAAQ,EAAE;IAC/B,IAAIkV,QAAQ,KAAK,IAAI,IAAIlV,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAACjH,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAImc,QAAQ,KAAK,KAAK,IAAIlV,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAAChH,OAAO,CAAC,CAAC;IAClB;EACJ;EACAmc,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEH,OAAO;MAAErZ,EAAE;MAAEiY;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAIoB,OAAO,EAAE;MACTpB,iBAAiB,CAACwB,gBAAgB,CAACzZ,EAAE,EAAEqZ,OAAO,CAAC;IACnD;EACJ;EACArM,iBAAiBA,CAAA,EAAG;IAChB9P,cAAc,CAAC,IAAI,CAAC8C,EAAE,CAAC;IACvB,IAAI,CAACwZ,cAAc,CAAC,CAAC;EACzB;EACArM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC8K,iBAAiB,CAACyB,mBAAmB,CAAC,CAAC;EAChD;EACAtL,iBAAiBA,CAAA,EAAG;IAChBjR,YAAY,CAAC,IAAI,CAAC6C,EAAE,CAAC;EACzB;EACAqN,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC+L,MAAM,KAAK,IAAI,EAAE;MACtBxiB,GAAG,CAAC,MAAM,IAAI,CAACwG,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoc,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACUpc,OAAOA,CAAA,EAAG;IAAA,IAAAuc,MAAA;IAAA,OAAA1Q,iBAAA;MACZ,MAAM2Q,MAAM,SAASD,MAAI,CAAC3B,cAAc,CAAC6B,IAAI,CAAC,CAAC;MAC/C,MAAMF,MAAI,CAAC5B,kBAAkB,CAAC+B,eAAe,CAAC,CAAC;MAC/C,MAAM1c,OAAO,CAACuc,MAAI,EAAE,aAAa,EAAEtD,iBAAiB,EAAEA,iBAAiB,EAAEhY,SAAS,CAAC;MACnF,IAAIsb,MAAI,CAAC5C,QAAQ,GAAG,CAAC,EAAE;QACnB4C,MAAI,CAACI,eAAe,GAAGlS,UAAU,CAAC,MAAM8R,MAAI,CAACtc,OAAO,CAAC,CAAC,EAAEsc,MAAI,CAAC5C,QAAQ,CAAC;MAC1E;MACA6C,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUvc,OAAOA,CAAC2c,IAAI,EAAE5B,IAAI,EAAE;IAAA,IAAA6B,MAAA;IAAA,OAAAhR,iBAAA;MACtB,MAAM2Q,MAAM,SAASK,MAAI,CAACjC,cAAc,CAAC6B,IAAI,CAAC,CAAC;MAC/C,IAAII,MAAI,CAACF,eAAe,EAAE;QACtBnS,YAAY,CAACqS,MAAI,CAACF,eAAe,CAAC;MACtC;MACA,MAAMG,SAAS,SAAS7c,OAAO,CAAC4c,MAAI,EAAED,IAAI,EAAE5B,IAAI,EAAE,aAAa,EAAEnB,iBAAiB,EAAEA,iBAAiB,CAAC;MACtG,IAAIiD,SAAS,EAAE;QACXD,MAAI,CAAClC,kBAAkB,CAACoC,iBAAiB,CAAC,CAAC;MAC/C;MACAP,MAAM,CAAC,CAAC;MACR,OAAOM,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAO9c,WAAW,CAAC,IAAI,CAAC0C,EAAE,EAAE,qBAAqB,CAAC;EACtD;EACA;AACJ;AACA;EACIuU,aAAaA,CAAA,EAAG;IACZ,OAAOjX,WAAW,CAAC,IAAI,CAAC0C,EAAE,EAAE,sBAAsB,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIqa,SAASA,CAAClQ,IAAI,EAAE;IACZ,OAAOhB,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC0P,OAAO,CAAC/Z,IAAI,CAAEub,MAAM,IAAKA,MAAM,CAACnQ,IAAI,KAAKA,IAAI,CAAC,CAAC;EAC/E;EACMoQ,WAAWA,CAACrI,MAAM,EAAE;IAAA,IAAAsI,MAAA;IAAA,OAAAvR,iBAAA;MACtB,MAAMmP,IAAI,GAAGlG,MAAM,CAACkG,IAAI;MACxB,IAAInb,QAAQ,CAACmb,IAAI,CAAC,EAAE;QAChB,OAAOoC,MAAI,CAACnd,OAAO,CAACgB,SAAS,EAAE+Z,IAAI,CAAC;MACxC;MACA,MAAMqC,aAAa,SAASD,MAAI,CAACjC,iBAAiB,CAACrG,MAAM,CAAC;MAC1D,IAAIuI,aAAa,EAAE;QACf,OAAOD,MAAI,CAACnd,OAAO,CAACmd,MAAI,CAACE,WAAW,CAAC,CAAC,EAAExI,MAAM,CAACkG,IAAI,CAAC;MACxD;MACA,OAAOjP,OAAO,CAACC,OAAO,CAAC,CAAC;IAAC;EAC7B;EACMmP,iBAAiBA,CAACrG,MAAM,EAAE;IAAA,IAAAyI,MAAA;IAAA,OAAA1R,iBAAA;MAC5B,IAAIiJ,MAAM,EAAE;QACR;QACA;QACA,MAAM0I,GAAG,SAASrd,QAAQ,CAAC2U,MAAM,CAAC2I,OAAO,EAAEF,MAAI,CAACD,WAAW,CAAC,CAAC,CAAC;QAC9D,IAAIE,GAAG,KAAK,KAAK,EAAE;UACf;UACA,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IAAC;EAChB;EACAF,WAAWA,CAAA,EAAG;IACV,MAAMI,QAAQ,GAAG,CAAC,CAAC;IACnB,IAAI,CAAChC,OAAO,CAAC/D,OAAO,CAAC,CAACC,GAAG,EAAEtF,KAAK,KAAK;MACjC,MAAMqL,cAAc,GAAG/F,GAAG,CAACgG,aAAa,KAAK3c,SAAS,GAAG2W,GAAG,CAACiG,OAAO,CAACjG,GAAG,CAACgG,aAAa,CAAC,GAAG3c,SAAS;MACnGyc,QAAQ,CAAC9F,GAAG,CAAC7K,IAAI,CAAC,GAAG;QACjB9K,IAAI,EAAE0b,cAAc,GAAGA,cAAc,CAAC1b,IAAI,GAAGhB,SAAS;QACtDkE,KAAK,EAAEwY,cAAc,GAAGA,cAAc,CAACxY,KAAK,GAAGlE,SAAS;QACxD6c,WAAW,EAAExL;MACjB,CAAC;IACL,CAAC,CAAC;IACF,OAAOoL,QAAQ;EACnB;EACAhF,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEqD;IAAe,CAAC,GAAG,IAAI;IAC/B,MAAM9S,IAAI,GAAGlO,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQ7B,CAAC,CAACE,IAAI,EAAEiJ,MAAM,CAACC,MAAM,CAAC;MAAEyF,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,MAAM;MAAEuO,QAAQ,EAAE;IAAK,CAAC,EAAEyF,cAAc,EAAE;MAAE/R,KAAK,EAAE;QAC1I+T,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACxG,YAAY;MACxC,CAAC;MAAEjG,KAAK,EAAEjP,MAAM,CAACC,MAAM,CAAC;QAAE,CAAC2G,IAAI,GAAG,IAAI;QAClC;QACA,CAAC,UAAUA,IAAI,EAAE,GAAG,IAAI;QAAE,gBAAgB,EAAE;MAAK,CAAC,EAAE3O,WAAW,CAAC,IAAI,CAACqhB,QAAQ,CAAC,CAAC;MAAEqC,gBAAgB,EAAE,IAAI,CAAClD,aAAa;MAAEmD,sBAAsB,EAAE,IAAI,CAAClD;IAAsB,CAAC,CAAC,EAAE7hB,CAAC,CAAC,cAAc,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEmW,OAAO,EAAE,IAAI,CAACtC,YAAY;MAAEuC,QAAQ,EAAE,IAAI,CAACtC;IAAgB,CAAC,CAAC,EAAE3iB,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuO,QAAQ,EAAE;IAAI,CAAC,CAAC,EAAEpd,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE,oCAAoC;MAAE0J,IAAI,EAAE;IAAS,CAAC,EAAE9hB,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAAC4J,OAAO,CAAC9V,GAAG,CAAEtK,CAAC,IAAM5B,CAAC,CAAC,KAAK,EAAE;MAAEoY,KAAK,EAAE8M,kBAAkB,CAACtjB,CAAC;IAAE,CAAC,EAAE5B,CAAC,CAAC,QAAQ,EAAE;MAAEmlB,IAAI,EAAE,QAAQ;MAAE7M,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC2L,WAAW,CAACriB,CAAC,CAAC;MAAEwW,KAAK,EAAEgN,WAAW,CAACxjB,CAAC;IAAE,CAAC,EAAEA,CAAC,CAACmH,IAAI,CAAC,CAAE,CAAC,CAAC,EAAE/I,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE;IAAiB,CAAC,EAAEpY,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE;IAAyB,CAAC,CAAC,EAAE,IAAI,CAAC8J,SAAS,IAAI,IAAI,CAACM,OAAO,CAACtW,GAAG,CAAEhL,CAAC,IAAKlB,CAAC,CAAC,mBAAmB,EAAE;MAAE0e,GAAG,EAAExd;IAAE,CAAC,CAAC,CAAC,EAAElB,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE;IAAyB,CAAC,CAAC,CAAC,CAAC,EAAEpY,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuO,QAAQ,EAAE;IAAI,CAAC,CAAC,CAAC;EAC/rC;EACA,IAAI1T,EAAEA,CAAA,EAAG;IAAE,OAAOtJ,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwf,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD,MAAMsF,kBAAkB,GAAItJ,MAAM,IAAK;EACnC,OAAO;IACH,CAAC,kBAAkBA,MAAM,CAACkG,IAAI,EAAE,GAAGlG,MAAM,CAACkG,IAAI,KAAK/Z,SAAS;IAC5D,uBAAuB,EAAE;EAC7B,CAAC;AACL,CAAC;AACD,MAAMqd,WAAW,GAAIxJ,MAAM,IAAK;EAC5B,OAAOzS,MAAM,CAACC,MAAM,CAAC;IAAE,eAAe,EAAE,IAAI;IAAE,iBAAiB,EAAE;EAAK,CAAC,EAAEhI,WAAW,CAACwa,MAAM,CAAC6G,QAAQ,CAAC,CAAC;AAC1G,CAAC;AACDzB,MAAM,CAAClQ,KAAK,GAAG;EACX+O,GAAG,EAAEgB,kBAAkB;EACvBf,EAAE,EAAEiB;AACR,CAAC;AAED,MAAMsE,kBAAkB,GAAG,i9EAAi9E;AAC5+E,MAAMC,wBAAwB,GAAGD,kBAAkB;AAEnD,MAAME,iBAAiB,GAAG,kiEAAkiE;AAC5jE,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,eAAe,GAAG,MAAM;EAC1Bva,WAAWA,CAACC,OAAO,EAAE;IACjBxL,gBAAgB,CAAC,IAAI,EAAEwL,OAAO,CAAC;IAC/B,IAAI,CAACua,kBAAkB,GAAG7lB,WAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IACpE,IAAI,CAAC8lB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAAC9hB,CAAC,GAAG,CAAC;IACV,IAAI,CAAC+hB,SAAS,GAAG,IAAI;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACtH,GAAG,GAAG3W,SAAS;EACxB;EACAke,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,GAAG,IAAI;EAC5B;EACMtP,iBAAiBA,CAAA,EAAG;IAAA,IAAAwP,MAAA;IAAA,OAAAvT,iBAAA;MACtB,IAAIwT,kBAAkB,GAAG,CAAC;MAC1B,IAAIC,iBAAiB,GAAG,IAAI;MAC5B,MAAMrW,IAAI,GAAGlO,UAAU,CAACqkB,MAAI,CAAC;MAC7B,IAAInW,IAAI,KAAK,KAAK,EAAE;QAChBoW,kBAAkB,GAAG,CAAC,IAAI;QAC1BC,iBAAiB,GAAG,CAAC;MACzB;MACAF,MAAI,CAACN,YAAY,GAAGO,kBAAkB;MACtCD,MAAI,CAACL,WAAW,GAAGO,iBAAiB;MACpCF,MAAI,CAACG,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEC,aAAa,CAAC;QAC/D5c,EAAE,EAAEwc,MAAI,CAACxc,EAAE;QACX6c,WAAW,EAAE,cAAc;QAC3BC,eAAe,EAAE,GAAG;QACpBjP,SAAS,EAAE,CAAC;QACZkP,OAAO,EAAE,KAAK;QACdC,OAAO,EAAG7Y,EAAE,IAAKqY,MAAI,CAACQ,OAAO,CAAC7Y,EAAE,CAAC;QACjC8Y,MAAM,EAAG9Y,EAAE,IAAKqY,MAAI,CAACS,MAAM,CAAC9Y,EAAE,CAAC;QAC/B+Y,KAAK,EAAG/Y,EAAE,IAAKqY,MAAI,CAACU,KAAK,CAAC/Y,EAAE;MAChC,CAAC,CAAC;MACFqY,MAAI,CAACG,OAAO,CAACQ,MAAM,CAAC,CAAC;MACrB;MACA;MACA;MACA;MACAX,MAAI,CAACY,KAAK,GAAGvV,UAAU,CAAC,MAAM;QAC1B2U,MAAI,CAACH,SAAS,GAAG,KAAK;QACtB;QACA;QACA;QACAG,MAAI,CAACa,OAAO,CAAC,IAAI,CAAC;MACtB,CAAC,EAAE,GAAG,CAAC;IAAC;EACZ;EACAhQ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACiQ,WAAW,CAAC,CAAC;EACtB;EACAC,kBAAkBA,CAAA,EAAG;IACjB;IACA,IAAI,IAAI,CAACjB,YAAY,EAAE;MACnB;MACA;MACA;MACA,IAAI,CAACgB,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC;MAC7B,IAAI,CAAChB,YAAY,GAAG,KAAK;IAC7B;EACJ;EACAnP,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACqQ,KAAK,KAAKnf,SAAS,EACxBof,oBAAoB,CAAC,IAAI,CAACD,KAAK,CAAC;IACpC,IAAI,IAAI,CAACJ,KAAK,EACVxV,YAAY,CAAC,IAAI,CAACwV,KAAK,CAAC;IAC5B,IAAI,IAAI,CAACT,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACzP,OAAO,CAAC,CAAC;MACtB,IAAI,CAACyP,OAAO,GAAGte,SAAS;IAC5B;EACJ;EACAqf,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC1B,kBAAkB,CAACrZ,IAAI,CAAC,IAAI,CAACqS,GAAG,CAAC;EAC1C;EACA2I,WAAWA,CAAC3C,aAAa,EAAEjE,QAAQ,EAAE;IACjC;IACA;IACA,MAAMzc,CAAC,GAAG0gB,aAAa,GAAG,CAAC,CAAC,GAAG,EAAEA,aAAa,GAAG,IAAI,CAACiB,SAAS,CAAC,GAAG,CAAC;IACpE,IAAI,CAACG,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,IAAI,CAACoB,KAAK,KAAKnf,SAAS,EACxBof,oBAAoB,CAAC,IAAI,CAACD,KAAK,CAAC;IACpC,IAAI,CAACI,MAAM,CAACtjB,CAAC,EAAEyc,QAAQ,EAAE,IAAI,CAAC;IAC9B,IAAI,CAAC2G,aAAa,CAAC,CAAC;EACxB;EACAE,MAAMA,CAACtjB,CAAC,EAAEyc,QAAQ,EAAE8G,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd;IACJ;IACA;IACA,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,UAAU,GAAG,CAAC;IAClB,MAAM;MAAEhJ,GAAG;MAAEkH;IAAa,CAAC,GAAG,IAAI;IAClC,MAAM+B,YAAY,GAAGjJ,GAAG,CAACgG,aAAa;IACtC,MAAMA,aAAa,GAAIhG,GAAG,CAACgG,aAAa,GAAG,IAAI,CAACkD,SAAS,CAAC,CAAC5jB,CAAC,CAAE;IAC9D,MAAM6jB,WAAW,GAAGpH,QAAQ,KAAK,CAAC,GAAG,EAAE,GAAGA,QAAQ,GAAG,IAAI;IACzD,MAAMqH,QAAQ,GAAG,SAAS,IAAI,CAACjC,WAAW,GAAG;IAC7C,MAAMkC,QAAQ,GAAG,IAAI,CAACP,MAAM,CAACO,QAAQ;IACrC,KAAK,IAAI/mB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+mB,QAAQ,CAAC3Y,MAAM,EAAEpO,CAAC,EAAE,EAAE;MACtC,MAAM4a,MAAM,GAAGmM,QAAQ,CAAC/mB,CAAC,CAAC;MAC1B,MAAMgnB,GAAG,GAAGtJ,GAAG,CAACiG,OAAO,CAAC3jB,CAAC,CAAC;MAC1B,MAAMinB,SAAS,GAAGjnB,CAAC,GAAG,IAAI,CAAC2kB,SAAS,GAAG3hB,CAAC;MACxC,IAAIkkB,SAAS,GAAG,EAAE;MAClB,IAAItC,YAAY,KAAK,CAAC,EAAE;QACpB,MAAMuC,OAAO,GAAGF,SAAS,GAAGrC,YAAY;QACxC,IAAIlV,IAAI,CAACC,GAAG,CAACwX,OAAO,CAAC,IAAI,EAAE,EAAE;UACzBV,UAAU,GAAG,CAAC;UACdC,UAAU,GAAG,EAAE;UACfQ,SAAS,GAAG,WAAWC,OAAO,OAAO;QACzC,CAAC,MACI;UACDV,UAAU,GAAG,CAAC,IAAI;QACtB;MACJ,CAAC,MACI;QACDC,UAAU,GAAG,CAAC;QACdD,UAAU,GAAGQ,SAAS;MAC1B;MACA,MAAMzD,QAAQ,GAAGE,aAAa,KAAK1jB,CAAC;MACpCknB,SAAS,IAAI,mBAAmBT,UAAU,MAAMC,UAAU,MAAM;MAChE,IAAI,IAAI,CAAC7B,WAAW,KAAK,CAAC,IAAI,CAACrB,QAAQ,EAAE;QACrC0D,SAAS,IAAIJ,QAAQ;MACzB;MACA;MACA,IAAI,IAAI,CAAC/B,SAAS,EAAE;QAChBiC,GAAG,CAACvH,QAAQ,GAAG,CAAC;QAChB7E,MAAM,CAAC9K,KAAK,CAACsX,kBAAkB,GAAG,EAAE;MACxC,CAAC,MACI,IAAI3H,QAAQ,KAAKuH,GAAG,CAACvH,QAAQ,EAAE;QAChCuH,GAAG,CAACvH,QAAQ,GAAGA,QAAQ;QACvB7E,MAAM,CAAC9K,KAAK,CAACsX,kBAAkB,GAAGP,WAAW;MACjD;MACA;MACA,IAAIK,SAAS,KAAKF,GAAG,CAACE,SAAS,EAAE;QAC7BF,GAAG,CAACE,SAAS,GAAGA,SAAS;MAC7B;MACAtM,MAAM,CAAC9K,KAAK,CAACoX,SAAS,GAAGA,SAAS;MAClC;AACZ;AACA;AACA;MACYF,GAAG,CAACxD,QAAQ,GAAGA,QAAQ;MACvB,IAAIA,QAAQ,EAAE;QACV5I,MAAM,CAAC5N,SAAS,CAACoJ,GAAG,CAACiR,mBAAmB,CAAC;MAC7C,CAAC,MACI;QACDzM,MAAM,CAAC5N,SAAS,CAACyJ,MAAM,CAAC4Q,mBAAmB,CAAC;MAChD;IACJ;IACA,IAAI,CAAC3J,GAAG,CAACiJ,YAAY,GAAGA,YAAY;IACpC,IAAIJ,KAAK,EAAE;MACP,IAAI,CAACvjB,CAAC,GAAGA,CAAC;IACd;IACA,IAAI,IAAI,CAACskB,SAAS,KAAK5D,aAAa,EAAE;MAClC;MACAvd,sBAAsB,CAAC,CAAC;MACxB,IAAI,CAACmhB,SAAS,GAAG5D,aAAa;IAClC;EACJ;EACA6D,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACzC,QAAQ,KAAK,CAAC,EAAE;MACrB;MACA,IAAI,CAACA,QAAQ,IAAI0C,qBAAqB;MACtC;MACA,IAAI,CAAC1C,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,CAAC,GAAGpV,IAAI,CAACjB,GAAG,CAAC,IAAI,CAACqW,QAAQ,EAAE,CAAC,CAAC,GAAGpV,IAAI,CAACnB,GAAG,CAAC,IAAI,CAACuW,QAAQ,EAAE,CAAC,CAAC,CAAC;MAC5F,IAAI9hB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAAC8hB,QAAQ;MAC9B,IAAI9hB,CAAC,GAAG,IAAI,CAACykB,IAAI,EAAE;QACf;QACAzkB,CAAC,GAAG,IAAI,CAACykB,IAAI;QACb,IAAI,CAAC3C,QAAQ,GAAG,CAAC;MACrB,CAAC,MACI,IAAI9hB,CAAC,GAAG,IAAI,CAAC0kB,IAAI,EAAE;QACpB;QACA1kB,CAAC,GAAG,IAAI,CAAC0kB,IAAI;QACb,IAAI,CAAC5C,QAAQ,GAAG,CAAC;MACrB;MACA,IAAI,CAACwB,MAAM,CAACtjB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACvB,MAAM2kB,WAAW,GAAGjY,IAAI,CAACkY,KAAK,CAAC5kB,CAAC,CAAC,GAAG,IAAI,CAAC2hB,SAAS,KAAK,CAAC,IAAIjV,IAAI,CAACC,GAAG,CAAC,IAAI,CAACmV,QAAQ,CAAC,GAAG,CAAC;MACvF,IAAI6C,WAAW,EAAE;QACb;QACA,IAAI,CAACzB,KAAK,GAAGlY,qBAAqB,CAAC,MAAM,IAAI,CAACuZ,UAAU,CAAC,CAAC,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAACzC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAACsB,aAAa,CAAC,CAAC;QACpBhgB,kBAAkB,CAAC,CAAC;MACxB;IACJ,CAAC,MACI,IAAI,IAAI,CAACpD,CAAC,GAAG,IAAI,CAAC2hB,SAAS,KAAK,CAAC,EAAE;MACpC;MACA,MAAMkD,UAAU,GAAGnY,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC3M,CAAC,GAAG,IAAI,CAAC2hB,SAAS,CAAC;MACpD;MACA,IAAI,CAACG,QAAQ,GAAG+C,UAAU,GAAG,IAAI,CAAClD,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACxD,IAAI,CAAC4C,UAAU,CAAC,CAAC;IACrB;EACJ;EACAX,SAASA,CAAC5jB,CAAC,EAAE;IACT,OAAO0M,IAAI,CAACnB,GAAG,CAACmB,IAAI,CAACjB,GAAG,CAACiB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACkY,KAAK,CAAC5kB,CAAC,GAAG,IAAI,CAAC2hB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACjH,GAAG,CAACiG,OAAO,CAACvV,MAAM,GAAG,CAAC,CAAC;EACvG;EACAsX,OAAOA,CAAClN,MAAM,EAAE;IACZ;IACA;IACA;IACA,IAAIA,MAAM,CAACsP,KAAK,CAACC,UAAU,EAAE;MACzBvP,MAAM,CAACsP,KAAK,CAACha,cAAc,CAAC,CAAC;IACjC;IACA0K,MAAM,CAACsP,KAAK,CAACnR,eAAe,CAAC,CAAC;IAC9BtQ,oBAAoB,CAAC,CAAC;IACtB;IACA,IAAI,IAAI,CAAC6f,KAAK,KAAKnf,SAAS,EACxBof,oBAAoB,CAAC,IAAI,CAACD,KAAK,CAAC;IACpC,MAAMvC,OAAO,GAAG,IAAI,CAACjG,GAAG,CAACiG,OAAO;IAChC,IAAI8D,IAAI,GAAG9D,OAAO,CAACvV,MAAM,GAAG,CAAC;IAC7B,IAAIsZ,IAAI,GAAG,CAAC;IACZ,KAAK,IAAI1nB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2jB,OAAO,CAACvV,MAAM,EAAEpO,CAAC,EAAE,EAAE;MACrC,IAAI,CAAC2jB,OAAO,CAAC3jB,CAAC,CAAC,CAAC4H,QAAQ,EAAE;QACtB6f,IAAI,GAAG/X,IAAI,CAACnB,GAAG,CAACkZ,IAAI,EAAEznB,CAAC,CAAC;QACxB0nB,IAAI,GAAGhY,IAAI,CAACjB,GAAG,CAACiZ,IAAI,EAAE1nB,CAAC,CAAC;MAC5B;IACJ;IACA,IAAI,CAACynB,IAAI,GAAG,EAAEA,IAAI,GAAG,IAAI,CAAC9C,SAAS,CAAC;IACpC,IAAI,CAAC+C,IAAI,GAAG,EAAEA,IAAI,GAAG,IAAI,CAAC/C,SAAS,CAAC;EACxC;EACAgB,MAAMA,CAACnN,MAAM,EAAE;IACX,IAAIA,MAAM,CAACsP,KAAK,CAACC,UAAU,EAAE;MACzBvP,MAAM,CAACsP,KAAK,CAACha,cAAc,CAAC,CAAC;IACjC;IACA0K,MAAM,CAACsP,KAAK,CAACnR,eAAe,CAAC,CAAC;IAC9B;IACA,IAAI3T,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGwV,MAAM,CAACwP,MAAM;IAC9B,IAAIhlB,CAAC,GAAG,IAAI,CAACykB,IAAI,EAAE;MACf;MACAzkB,CAAC,GAAG0M,IAAI,CAACuY,GAAG,CAACjlB,CAAC,EAAE,GAAG,CAAC;MACpB,IAAI,CAACklB,UAAU,GAAGllB,CAAC;IACvB,CAAC,MACI,IAAIA,CAAC,GAAG,IAAI,CAAC0kB,IAAI,EAAE;MACpB;MACA1kB,CAAC,IAAI0M,IAAI,CAACuY,GAAG,CAAC,IAAI,CAACP,IAAI,GAAG1kB,CAAC,EAAE,GAAG,CAAC;MACjC,IAAI,CAACklB,UAAU,GAAGllB,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACklB,UAAU,GAAG,CAAC;IACvB;IACA,IAAI,CAAC5B,MAAM,CAACtjB,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;EAC5B;EACA4iB,KAAKA,CAACpN,MAAM,EAAE;IACV,IAAI,IAAI,CAAC0P,UAAU,GAAG,CAAC,EAAE;MACrB;MACA,IAAI,CAAC5B,MAAM,CAAC,IAAI,CAACmB,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;MACjC,IAAI,CAACrB,aAAa,CAAC,CAAC;MACpB;IACJ,CAAC,MACI,IAAI,IAAI,CAAC8B,UAAU,GAAG,CAAC,EAAE;MAC1B;MACA,IAAI,CAAC5B,MAAM,CAAC,IAAI,CAACoB,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;MACjC,IAAI,CAACtB,aAAa,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACtB,QAAQ,GAAGnlB,KAAK,CAAC,CAACwoB,gBAAgB,EAAE3P,MAAM,CAAC4P,SAAS,GAAG,EAAE,EAAED,gBAAgB,CAAC;IACjF,IAAI,IAAI,CAACrD,QAAQ,KAAK,CAAC,IAAItM,MAAM,CAACwP,MAAM,KAAK,CAAC,EAAE;MAC5C,MAAMhB,GAAG,GAAGxO,MAAM,CAACsP,KAAK,CAAC9K,MAAM,CAACrR,OAAO,CAAC,aAAa,CAAC;MACtD,IAAIqb,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACqB,YAAY,CAAC,WAAW,CAAC,EAAE;QACzE,IAAI,CAAChC,WAAW,CAACtN,QAAQ,CAACiO,GAAG,CAACxM,YAAY,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE8N,mBAAmB,CAAC;MACtF;IACJ,CAAC,MACI;MACD,IAAI,CAACtlB,CAAC,IAAIwV,MAAM,CAACwP,MAAM;MACvB,IAAItY,IAAI,CAACC,GAAG,CAAC6I,MAAM,CAAC4P,SAAS,CAAC,GAAG,IAAI,EAAE;QACnC,MAAMG,aAAa,GAAG/P,MAAM,CAACwP,MAAM,GAAG,CAAC;QACvC,MAAMQ,iBAAiB,GAAI9Y,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC3M,CAAC,CAAC,GAAG,IAAI,CAAC2hB,SAAS,GAAI,IAAI,CAACA,SAAS;QAC9E,IAAI4D,aAAa,IAAIC,iBAAiB,GAAG,GAAG,EAAE;UAC1C,IAAI,CAAC1D,QAAQ,GAAGpV,IAAI,CAACC,GAAG,CAAC,IAAI,CAACmV,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC,MACI,IAAI,CAACyD,aAAa,IAAIC,iBAAiB,IAAI,GAAG,EAAE;UACjD,IAAI,CAAC1D,QAAQ,GAAGpV,IAAI,CAACC,GAAG,CAAC,IAAI,CAACmV,QAAQ,CAAC;QAC3C;MACJ;MACA,IAAI,CAACyC,UAAU,CAAC,CAAC;IACrB;EACJ;EACAxB,OAAOA,CAAC0C,YAAY,EAAE7G,QAAQ,EAAE;IAC5B,IAAIxY,EAAE;IACN,IAAImF,GAAG,GAAG,IAAI,CAACmP,GAAG,CAACiG,OAAO,CAACvV,MAAM,GAAG,CAAC;IACrC,IAAIK,GAAG,GAAG,CAAC;IACX,MAAMkV,OAAO,GAAG,IAAI,CAACjG,GAAG,CAACiG,OAAO;IAChC,KAAK,IAAI3jB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2jB,OAAO,CAACvV,MAAM,EAAEpO,CAAC,EAAE,EAAE;MACrC,IAAI,CAAC2jB,OAAO,CAAC3jB,CAAC,CAAC,CAAC4H,QAAQ,EAAE;QACtB2G,GAAG,GAAGmB,IAAI,CAACnB,GAAG,CAACA,GAAG,EAAEvO,CAAC,CAAC;QACtByO,GAAG,GAAGiB,IAAI,CAACjB,GAAG,CAACA,GAAG,EAAEzO,CAAC,CAAC;MAC1B;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC8kB,QAAQ,KAAK,CAAC,EAAE;MACrB;IACJ;IACA,MAAMpB,aAAa,GAAG/jB,KAAK,CAAC4O,GAAG,EAAE,CAACnF,EAAE,GAAG,IAAI,CAACsU,GAAG,CAACgG,aAAa,MAAM,IAAI,IAAIta,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,EAAEqF,GAAG,CAAC;IACvG,IAAI,IAAI,CAACiP,GAAG,CAACiJ,YAAY,KAAKjD,aAAa,IAAI+E,YAAY,EAAE;MACzD,MAAMzlB,CAAC,GAAG0gB,aAAa,GAAG,IAAI,CAACiB,SAAS,GAAG,CAAC,CAAC;MAC7C,MAAMlF,QAAQ,GAAGmC,QAAQ,GAAG0G,mBAAmB,GAAG,CAAC;MACnD,IAAI,CAACxD,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACwB,MAAM,CAACtjB,CAAC,EAAEyc,QAAQ,EAAE,IAAI,CAAC;IAClC;EACJ;EACAuG,WAAWA,CAACyC,YAAY,EAAE7G,QAAQ,EAAE;IAChC,MAAM8G,KAAK,GAAG,IAAI,CAAClC,MAAM;IACzB,IAAIkC,KAAK,EAAE;MACP;MACA;MACA,IAAI,CAAC/D,SAAS,GAAG+D,KAAK,CAACC,iBAAiB,GAAGD,KAAK,CAACC,iBAAiB,CAACC,YAAY,GAAG,CAAC;IACvF;IACA,IAAI,CAAC7C,OAAO,CAAC0C,YAAY,EAAE7G,QAAQ,CAAC;EACxC;EACApD,MAAMA,CAAA,EAAG;IACL,MAAMd,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,MAAM3O,IAAI,GAAGlO,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQ7B,CAAC,CAACE,IAAI,EAAE;MAAE2O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAEjP,MAAM,CAACC,MAAM,CAAC;QAAE,CAAC2G,IAAI,GAAG,IAAI;QAAE,YAAY,EAAE,IAAI;QAAE,kBAAkB,EAAE,IAAI,CAAC2O,GAAG,CAACmL,KAAK,KAAK,MAAM;QAAE,mBAAmB,EAAE,IAAI,CAACnL,GAAG,CAACmL,KAAK,KAAK;MAAQ,CAAC,EAAEzoB,WAAW,CAACsd,GAAG,CAAC+D,QAAQ,CAAC,CAAC;MAAE3R,KAAK,EAAE;QACzP,WAAW,EAAE,IAAI,CAAC4N,GAAG,CAACoL;MAC1B;IAAE,CAAC,EAAEpL,GAAG,CAACqL,MAAM,IAAK/pB,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE,eAAe;MAAEtH,KAAK,EAAE;QAAEkZ,KAAK,EAAEtL,GAAG,CAACuL;MAAY;IAAE,CAAC,EAAEvL,GAAG,CAACqL,MAAM,CAAE,EAAE/pB,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE,aAAa;MAAEtH,KAAK,EAAE;QAAEoZ,QAAQ,EAAExL,GAAG,CAACyL;MAAa,CAAC;MAAEzO,GAAG,EAAGhS,EAAE,IAAM,IAAI,CAAC8d,MAAM,GAAG9d;IAAI,CAAC,EAAEgV,GAAG,CAACiG,OAAO,CAACzY,GAAG,CAAC,CAAC5K,CAAC,EAAE8X,KAAK,KAAMpZ,CAAC,CAAC,QAAQ,EAAE;MAAE,YAAY,EAAEsB,CAAC,CAACwH,SAAS;MAAEsP,KAAK,EAAE;QAAE,YAAY,EAAE,IAAI;QAAE,qBAAqB,EAAE,CAAC,CAAC9W,CAAC,CAACsH;MAAS,CAAC;MAAE,WAAW,EAAEwQ;IAAM,CAAC,EAAE9X,CAAC,CAACyH,IAAI,CAAE,CAAC,CAAC,EAAE2V,GAAG,CAAC0L,MAAM,IAAKpqB,CAAC,CAAC,KAAK,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEuJ,KAAK,EAAE,eAAe;MAAEtH,KAAK,EAAE;QAAEkZ,KAAK,EAAEtL,GAAG,CAAC2L;MAAY;IAAE,CAAC,EAAE3L,GAAG,CAAC0L,MAAM,CAAE,CAAC;EACloB;EACA,IAAI1gB,EAAEA,CAAA,EAAG;IAAE,OAAOtJ,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwf,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,KAAK,EAAE,CAAC,YAAY;IACxB,CAAC;EAAE;AACP,CAAC;AACD,MAAMyI,mBAAmB,GAAG,qBAAqB;AACjD,MAAMG,qBAAqB,GAAG,IAAI;AAClC,MAAMW,gBAAgB,GAAG,EAAE;AAC3B,MAAMG,mBAAmB,GAAG,GAAG;AAC/B7D,eAAe,CAAC3U,KAAK,GAAG;EACpB+O,GAAG,EAAEyF,wBAAwB;EAC7BxF,EAAE,EAAE0F;AACR,CAAC;AAED,SAASva,QAAQ,IAAIqf,YAAY,EAAEtJ,MAAM,IAAIuJ,UAAU,EAAE9E,eAAe,IAAI+E,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}