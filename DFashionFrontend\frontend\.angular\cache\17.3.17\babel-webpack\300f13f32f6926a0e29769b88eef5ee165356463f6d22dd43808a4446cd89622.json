{"ast": null, "code": "import { BehaviorSubject, tap, catchError, throwError, map } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = environment.apiUrl;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n  initializeAuth() {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: response => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // Clear invalid token without redirecting\n          this.clearAuth();\n        }\n      });\n    }\n  }\n  login(credentials) {\n    console.log('🔐 AuthService.login() called with:', credentials);\n    console.log('🌐 API_URL:', this.API_URL);\n    console.log('📱 Making HTTP POST request to:', `${this.API_URL}/auth/login`);\n    return this.loginWithRetry(credentials, this.API_URL);\n  }\n  loginWithRetry(credentials, apiUrl) {\n    return this.http.post(`${apiUrl}/auth/login`, credentials).pipe(tap(response => {\n      console.log('✅ Login response received:', response);\n      // Handle backend response format: { success: true, data: { token, user } }\n      const authData = response.data || response;\n      this.setToken(authData.token);\n      this.currentUserSubject.next(authData.user);\n      this.isAuthenticatedSubject.next(true);\n      // Trigger cart and wishlist refresh after successful login\n      this.refreshUserDataOnLogin();\n    }), catchError(error => {\n      console.error('❌ Login error:', error);\n      console.error('❌ Error details:', JSON.stringify(error, null, 2));\n      // For mobile apps, try alternative URLs if available\n      if (this.isMobileApp() && environment.fallbackApiUrls) {\n        console.log('📱 Trying fallback URLs for mobile...');\n        return this.tryFallbackUrls(credentials, environment.fallbackApiUrls);\n      }\n      return throwError(() => error);\n    }));\n  }\n  tryFallbackUrls(credentials, fallbackUrls) {\n    if (fallbackUrls.length === 0) {\n      return throwError(() => new Error('All API URLs failed'));\n    }\n    const [firstUrl, ...remainingUrls] = fallbackUrls;\n    console.log('🔄 Trying fallback URL:', firstUrl);\n    return this.http.post(`${firstUrl}/auth/login`, credentials).pipe(tap(response => {\n      console.log('✅ Login successful with fallback URL:', firstUrl);\n      const authData = response.data || response;\n      this.setToken(authData.token);\n      this.currentUserSubject.next(authData.user);\n      this.isAuthenticatedSubject.next(true);\n      this.refreshUserDataOnLogin();\n    }), catchError(error => {\n      console.error('❌ Fallback URL failed:', firstUrl, error);\n      if (remainingUrls.length > 0) {\n        return this.tryFallbackUrls(credentials, remainingUrls);\n      }\n      return throwError(() => error);\n    }));\n  }\n  isMobileApp() {\n    return window.location.protocol === 'capacitor:' || window.location.protocol === 'ionic:' || window.Capacitor !== undefined;\n  }\n  register(userData) {\n    return this.http.post(`${this.API_URL}/auth/register`, userData).pipe(tap(response => {\n      // Handle backend response format: { success: true, data: { token, user } }\n      const authData = response.data || response;\n      this.setToken(authData.token);\n      this.currentUserSubject.next(authData.user);\n      this.isAuthenticatedSubject.next(true);\n    }));\n  }\n  logout() {\n    this.clearAuth();\n    this.router.navigate(['/auth/login']);\n  }\n  clearAuth() {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    // Clear cart and wishlist data on logout\n    this.clearUserDataOnLogout();\n  }\n  // Method to refresh user data (cart, wishlist) after login\n  refreshUserDataOnLogin() {\n    // Use setTimeout to avoid circular dependency issues\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({\n          CartService\n        }) => {\n          const cartService = new CartService(this.http, null, null);\n          cartService.loadCartCountOnLogin();\n        });\n        import('./wishlist.service').then(({\n          WishlistService\n        }) => {\n          const wishlistService = new WishlistService(this.http);\n          wishlistService.syncWithServer().subscribe();\n        });\n      } catch (error) {\n        console.error('Error refreshing user data on login:', error);\n      }\n    }, 100);\n  }\n  // Method to clear user data on logout\n  clearUserDataOnLogout() {\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({\n          CartService\n        }) => {\n          const cartService = new CartService(this.http, null, null);\n          cartService.clearCartData();\n        });\n        import('./wishlist.service').then(({\n          WishlistService\n        }) => {\n          const wishlistService = new WishlistService(this.http);\n          wishlistService.clearWishlist().subscribe();\n        });\n      } catch (error) {\n        console.error('Error clearing user data on logout:', error);\n      }\n    }, 100);\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/auth/me`).pipe(map(response => {\n      // Handle backend response format: { success: true, data: { user } }\n      return response.data || response;\n    }));\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  setToken(token) {\n    localStorage.setItem('token', token);\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  get isAuthenticated() {\n    return this.isAuthenticatedSubject.value;\n  }\n  isAdmin() {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n  isVendor() {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n  isCustomer() {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n  // Helper methods for checking authentication before actions\n  requireAuth(action = 'perform this action') {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    return true;\n  }\n  requireSuperAdminAuth(action = 'perform this action') {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    if (!this.isAdmin()) {\n      this.showRoleError('super admin', action);\n      return false;\n    }\n    return true;\n  }\n  requireCustomerAuth(action = 'perform this action') {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    if (!this.isCustomer()) {\n      this.showRoleError('customer', action);\n      return false;\n    }\n    return true;\n  }\n  showLoginPrompt(action) {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: this.router.url\n        }\n      });\n    }\n  }\n  showRoleError(requiredRole, action) {\n    alert(`Only ${requiredRole}s can ${action}. Please login with a ${requiredRole} account.`);\n  }\n  // Social interaction methods with authentication checks\n  canLike() {\n    return this.requireCustomerAuth('like posts');\n  }\n  canComment() {\n    return this.requireCustomerAuth('comment on posts');\n  }\n  canAddToCart() {\n    return this.requireCustomerAuth('add items to cart');\n  }\n  canAddToWishlist() {\n    return this.requireCustomerAuth('add items to wishlist');\n  }\n  canBuy() {\n    return this.requireCustomerAuth('purchase items');\n  }\n  // Get auth headers for API calls\n  getAuthHeaders() {\n    const token = this.getToken();\n    return token ? {\n      'Authorization': `Bearer ${token}`\n    } : {};\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "catchError", "throwError", "map", "environment", "AuthService", "constructor", "http", "router", "API_URL", "apiUrl", "currentUserSubject", "isAuthenticatedSubject", "currentUser$", "asObservable", "isAuthenticated$", "initializeAuth", "token", "getToken", "getCurrentUser", "subscribe", "next", "response", "user", "error", "clearAuth", "login", "credentials", "console", "log", "loginWithRetry", "post", "pipe", "authData", "data", "setToken", "refreshUserDataOnLogin", "JSON", "stringify", "isMobileApp", "fallbackApiUrls", "tryFallbackUrls", "fallbackUrls", "length", "Error", "firstUrl", "remainingUrls", "window", "location", "protocol", "Capacitor", "undefined", "register", "userData", "logout", "navigate", "localStorage", "removeItem", "clearUserDataOnLogout", "setTimeout", "then", "CartService", "cartService", "loadCartCountOnLogin", "WishlistService", "wishlistService", "syncWithServer", "clearCartData", "clearWishlist", "get", "getItem", "setItem", "currentUserValue", "value", "isAuthenticated", "isAdmin", "role", "isVendor", "isCustomer", "requireAuth", "action", "showLoginPrompt", "requireSuperAdminAuth", "showRoleError", "requireCustomerAuth", "message", "confirm", "queryParams", "returnUrl", "url", "requiredRole", "alert", "canLike", "canComment", "canAddToCart", "canAddToWishlist", "canBuy", "getAuthHeaders", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable, Inject, forwardRef } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap, catchError, throwError, of, map } from 'rxjs';\nimport { Router } from '@angular/router';\n\nimport { User, LoginRequest, RegisterRequest, AuthResponse } from '../models/user.model';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = environment.apiUrl;\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {}\n\n  initializeAuth(): void {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: (response) => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // Clear invalid token without redirecting\n          this.clearAuth();\n        }\n      });\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<any> {\n    console.log('🔐 AuthService.login() called with:', credentials);\n    console.log('🌐 API_URL:', this.API_URL);\n    console.log('📱 Making HTTP POST request to:', `${this.API_URL}/auth/login`);\n\n    return this.loginWithRetry(credentials, this.API_URL);\n  }\n\n  private loginWithRetry(credentials: LoginRequest, apiUrl: string): Observable<any> {\n    return this.http.post<any>(`${apiUrl}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          console.log('✅ Login response received:', response);\n          // Handle backend response format: { success: true, data: { token, user } }\n          const authData = response.data || response;\n          this.setToken(authData.token);\n          this.currentUserSubject.next(authData.user);\n          this.isAuthenticatedSubject.next(true);\n\n          // Trigger cart and wishlist refresh after successful login\n          this.refreshUserDataOnLogin();\n        }),\n        catchError(error => {\n          console.error('❌ Login error:', error);\n          console.error('❌ Error details:', JSON.stringify(error, null, 2));\n\n          // For mobile apps, try alternative URLs if available\n          if (this.isMobileApp() && environment.fallbackApiUrls) {\n            console.log('📱 Trying fallback URLs for mobile...');\n            return this.tryFallbackUrls(credentials, environment.fallbackApiUrls);\n          }\n\n          return throwError(() => error);\n        })\n      );\n  }\n\n  private tryFallbackUrls(credentials: LoginRequest, fallbackUrls: string[]): Observable<any> {\n    if (fallbackUrls.length === 0) {\n      return throwError(() => new Error('All API URLs failed'));\n    }\n\n    const [firstUrl, ...remainingUrls] = fallbackUrls;\n    console.log('🔄 Trying fallback URL:', firstUrl);\n\n    return this.http.post<any>(`${firstUrl}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          console.log('✅ Login successful with fallback URL:', firstUrl);\n          const authData = response.data || response;\n          this.setToken(authData.token);\n          this.currentUserSubject.next(authData.user);\n          this.isAuthenticatedSubject.next(true);\n          this.refreshUserDataOnLogin();\n        }),\n        catchError(error => {\n          console.error('❌ Fallback URL failed:', firstUrl, error);\n          if (remainingUrls.length > 0) {\n            return this.tryFallbackUrls(credentials, remainingUrls);\n          }\n          return throwError(() => error);\n        })\n      );\n  }\n\n  private isMobileApp(): boolean {\n    return window.location.protocol === 'capacitor:' ||\n           window.location.protocol === 'ionic:' ||\n           (window as any).Capacitor !== undefined;\n  }\n\n\n\n\n\n  register(userData: RegisterRequest): Observable<any> {\n    return this.http.post<any>(`${this.API_URL}/auth/register`, userData)\n      .pipe(\n        tap(response => {\n          // Handle backend response format: { success: true, data: { token, user } }\n          const authData = response.data || response;\n          this.setToken(authData.token);\n          this.currentUserSubject.next(authData.user);\n          this.isAuthenticatedSubject.next(true);\n        })\n      );\n  }\n\n\n\n  logout(): void {\n    this.clearAuth();\n    this.router.navigate(['/auth/login']);\n  }\n\n  private clearAuth(): void {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n\n    // Clear cart and wishlist data on logout\n    this.clearUserDataOnLogout();\n  }\n\n  // Method to refresh user data (cart, wishlist) after login\n  private refreshUserDataOnLogin(): void {\n    // Use setTimeout to avoid circular dependency issues\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({ CartService }) => {\n          const cartService = new CartService(this.http, null as any, null as any);\n          cartService.loadCartCountOnLogin();\n        });\n\n        import('./wishlist.service').then(({ WishlistService }) => {\n          const wishlistService = new WishlistService(this.http);\n          wishlistService.syncWithServer().subscribe();\n        });\n      } catch (error) {\n        console.error('Error refreshing user data on login:', error);\n      }\n    }, 100);\n  }\n\n  // Method to clear user data on logout\n  private clearUserDataOnLogout(): void {\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({ CartService }) => {\n          const cartService = new CartService(this.http, null as any, null as any);\n          cartService.clearCartData();\n        });\n\n        import('./wishlist.service').then(({ WishlistService }) => {\n          const wishlistService = new WishlistService(this.http);\n          wishlistService.clearWishlist().subscribe();\n        });\n      } catch (error) {\n        console.error('Error clearing user data on logout:', error);\n      }\n    }, 100);\n  }\n\n  getCurrentUser(): Observable<{ user: User }> {\n    return this.http.get<any>(`${this.API_URL}/auth/me`).pipe(\n      map(response => {\n        // Handle backend response format: { success: true, data: { user } }\n        return response.data || response;\n      })\n    );\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  private setToken(token: string): void {\n    localStorage.setItem('token', token);\n  }\n\n  get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  get isAuthenticated(): boolean {\n    return this.isAuthenticatedSubject.value;\n  }\n\n  isAdmin(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n\n  isVendor(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n\n  isCustomer(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n\n  // Helper methods for checking authentication before actions\n  requireAuth(action: string = 'perform this action'): boolean {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    return true;\n  }\n\n  requireSuperAdminAuth(action: string = 'perform this action'): boolean {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n\n    if (!this.isAdmin()) {\n      this.showRoleError('super admin', action);\n      return false;\n    }\n\n    return true;\n  }\n\n  requireCustomerAuth(action: string = 'perform this action'): boolean {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n\n    if (!this.isCustomer()) {\n      this.showRoleError('customer', action);\n      return false;\n    }\n\n    return true;\n  }\n\n  private showLoginPrompt(action: string): void {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: { returnUrl: this.router.url }\n      });\n    }\n  }\n\n  private showRoleError(requiredRole: string, action: string): void {\n    alert(`Only ${requiredRole}s can ${action}. Please login with a ${requiredRole} account.`);\n  }\n\n  // Social interaction methods with authentication checks\n  canLike(): boolean {\n    return this.requireCustomerAuth('like posts');\n  }\n\n  canComment(): boolean {\n    return this.requireCustomerAuth('comment on posts');\n  }\n\n  canAddToCart(): boolean {\n    return this.requireCustomerAuth('add items to cart');\n  }\n\n  canAddToWishlist(): boolean {\n    return this.requireCustomerAuth('add items to wishlist');\n  }\n\n  canBuy(): boolean {\n    return this.requireCustomerAuth('purchase items');\n  }\n\n  // Get auth headers for API calls\n  getAuthHeaders(): { [key: string]: string } {\n    const token = this.getToken();\n    return token ? { 'Authorization': `Bearer ${token}` } : {};\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,EAAEC,UAAU,EAAEC,UAAU,EAAMC,GAAG,QAAQ,MAAM;AAIxF,SAASC,WAAW,QAAQ,mCAAmC;;;;AAK/D,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM;IACrC,KAAAC,kBAAkB,GAAG,IAAIZ,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAa,sBAAsB,GAAG,IAAIb,eAAe,CAAU,KAAK,CAAC;IAE7D,KAAAc,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACE,YAAY,EAAE;EAKjE;EAEHE,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,cAAc,EAAE,CAACC,SAAS,CAAC;QAC9BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACX,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;UAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QACxC,CAAC;QACDG,KAAK,EAAEA,CAAA,KAAK;UACV;UACA,IAAI,CAACC,SAAS,EAAE;QAClB;OACD,CAAC;;EAEN;EAEAC,KAAKA,CAACC,WAAyB;IAC7BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEF,WAAW,CAAC;IAC/DC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACpB,OAAO,CAAC;IACxCmB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,GAAG,IAAI,CAACpB,OAAO,aAAa,CAAC;IAE5E,OAAO,IAAI,CAACqB,cAAc,CAACH,WAAW,EAAE,IAAI,CAAClB,OAAO,CAAC;EACvD;EAEQqB,cAAcA,CAACH,WAAyB,EAAEjB,MAAc;IAC9D,OAAO,IAAI,CAACH,IAAI,CAACwB,IAAI,CAAM,GAAGrB,MAAM,aAAa,EAAEiB,WAAW,CAAC,CAC5DK,IAAI,CACHhC,GAAG,CAACsB,QAAQ,IAAG;MACbM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEP,QAAQ,CAAC;MACnD;MACA,MAAMW,QAAQ,GAAGX,QAAQ,CAACY,IAAI,IAAIZ,QAAQ;MAC1C,IAAI,CAACa,QAAQ,CAACF,QAAQ,CAAChB,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACY,QAAQ,CAACV,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;MAEtC;MACA,IAAI,CAACe,sBAAsB,EAAE;IAC/B,CAAC,CAAC,EACFnC,UAAU,CAACuB,KAAK,IAAG;MACjBI,OAAO,CAACJ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCI,OAAO,CAACJ,KAAK,CAAC,kBAAkB,EAAEa,IAAI,CAACC,SAAS,CAACd,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAEjE;MACA,IAAI,IAAI,CAACe,WAAW,EAAE,IAAInC,WAAW,CAACoC,eAAe,EAAE;QACrDZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,OAAO,IAAI,CAACY,eAAe,CAACd,WAAW,EAAEvB,WAAW,CAACoC,eAAe,CAAC;;MAGvE,OAAOtC,UAAU,CAAC,MAAMsB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEQiB,eAAeA,CAACd,WAAyB,EAAEe,YAAsB;IACvE,IAAIA,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAOzC,UAAU,CAAC,MAAM,IAAI0C,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,MAAM,CAACC,QAAQ,EAAE,GAAGC,aAAa,CAAC,GAAGJ,YAAY;IACjDd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,QAAQ,CAAC;IAEhD,OAAO,IAAI,CAACtC,IAAI,CAACwB,IAAI,CAAM,GAAGc,QAAQ,aAAa,EAAElB,WAAW,CAAC,CAC9DK,IAAI,CACHhC,GAAG,CAACsB,QAAQ,IAAG;MACbM,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEgB,QAAQ,CAAC;MAC9D,MAAMZ,QAAQ,GAAGX,QAAQ,CAACY,IAAI,IAAIZ,QAAQ;MAC1C,IAAI,CAACa,QAAQ,CAACF,QAAQ,CAAChB,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACY,QAAQ,CAACV,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;MACtC,IAAI,CAACe,sBAAsB,EAAE;IAC/B,CAAC,CAAC,EACFnC,UAAU,CAACuB,KAAK,IAAG;MACjBI,OAAO,CAACJ,KAAK,CAAC,wBAAwB,EAAEqB,QAAQ,EAAErB,KAAK,CAAC;MACxD,IAAIsB,aAAa,CAACH,MAAM,GAAG,CAAC,EAAE;QAC5B,OAAO,IAAI,CAACF,eAAe,CAACd,WAAW,EAAEmB,aAAa,CAAC;;MAEzD,OAAO5C,UAAU,CAAC,MAAMsB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEQe,WAAWA,CAAA;IACjB,OAAOQ,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,YAAY,IACzCF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,IACpCF,MAAc,CAACG,SAAS,KAAKC,SAAS;EAChD;EAMAC,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAAC9C,IAAI,CAACwB,IAAI,CAAM,GAAG,IAAI,CAACtB,OAAO,gBAAgB,EAAE4C,QAAQ,CAAC,CAClErB,IAAI,CACHhC,GAAG,CAACsB,QAAQ,IAAG;MACb;MACA,MAAMW,QAAQ,GAAGX,QAAQ,CAACY,IAAI,IAAIZ,QAAQ;MAC1C,IAAI,CAACa,QAAQ,CAACF,QAAQ,CAAChB,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACY,QAAQ,CAACV,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,CACH;EACL;EAIAiC,MAAMA,CAAA;IACJ,IAAI,CAAC7B,SAAS,EAAE;IAChB,IAAI,CAACjB,MAAM,CAAC+C,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEQ9B,SAASA,CAAA;IACf+B,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAAC9C,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IAEvC;IACA,IAAI,CAACqC,qBAAqB,EAAE;EAC9B;EAEA;EACQtB,sBAAsBA,CAAA;IAC5B;IACAuB,UAAU,CAAC,MAAK;MACd,IAAI;QACF;QACA,MAAM,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC;QAAW,CAAE,KAAI;UAChD,MAAMC,WAAW,GAAG,IAAID,WAAW,CAAC,IAAI,CAACtD,IAAI,EAAE,IAAW,EAAE,IAAW,CAAC;UACxEuD,WAAW,CAACC,oBAAoB,EAAE;QACpC,CAAC,CAAC;QAEF,MAAM,CAAC,oBAAoB,CAAC,CAACH,IAAI,CAAC,CAAC;UAAEI;QAAe,CAAE,KAAI;UACxD,MAAMC,eAAe,GAAG,IAAID,eAAe,CAAC,IAAI,CAACzD,IAAI,CAAC;UACtD0D,eAAe,CAACC,cAAc,EAAE,CAAC9C,SAAS,EAAE;QAC9C,CAAC,CAAC;OACH,CAAC,OAAOI,KAAK,EAAE;QACdI,OAAO,CAACJ,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;IAEhE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACQkC,qBAAqBA,CAAA;IAC3BC,UAAU,CAAC,MAAK;MACd,IAAI;QACF;QACA,MAAM,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC;QAAW,CAAE,KAAI;UAChD,MAAMC,WAAW,GAAG,IAAID,WAAW,CAAC,IAAI,CAACtD,IAAI,EAAE,IAAW,EAAE,IAAW,CAAC;UACxEuD,WAAW,CAACK,aAAa,EAAE;QAC7B,CAAC,CAAC;QAEF,MAAM,CAAC,oBAAoB,CAAC,CAACP,IAAI,CAAC,CAAC;UAAEI;QAAe,CAAE,KAAI;UACxD,MAAMC,eAAe,GAAG,IAAID,eAAe,CAAC,IAAI,CAACzD,IAAI,CAAC;UACtD0D,eAAe,CAACG,aAAa,EAAE,CAAChD,SAAS,EAAE;QAC7C,CAAC,CAAC;OACH,CAAC,OAAOI,KAAK,EAAE;QACdI,OAAO,CAACJ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;;IAE/D,CAAC,EAAE,GAAG,CAAC;EACT;EAEAL,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACZ,IAAI,CAAC8D,GAAG,CAAM,GAAG,IAAI,CAAC5D,OAAO,UAAU,CAAC,CAACuB,IAAI,CACvD7B,GAAG,CAACmB,QAAQ,IAAG;MACb;MACA,OAAOA,QAAQ,CAACY,IAAI,IAAIZ,QAAQ;IAClC,CAAC,CAAC,CACH;EACH;EAEAJ,QAAQA,CAAA;IACN,OAAOsC,YAAY,CAACc,OAAO,CAAC,OAAO,CAAC;EACtC;EAEQnC,QAAQA,CAAClB,KAAa;IAC5BuC,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEtD,KAAK,CAAC;EACtC;EAEA,IAAIuD,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC7D,kBAAkB,CAAC8D,KAAK;EACtC;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC9D,sBAAsB,CAAC6D,KAAK;EAC1C;EAEAE,OAAOA,CAAA;IACL,MAAMpD,IAAI,GAAG,IAAI,CAACiD,gBAAgB;IAClC,OAAOjD,IAAI,EAAEqD,IAAI,KAAK,OAAO;EAC/B;EAEAC,QAAQA,CAAA;IACN,MAAMtD,IAAI,GAAG,IAAI,CAACiD,gBAAgB;IAClC,OAAOjD,IAAI,EAAEqD,IAAI,KAAK,QAAQ;EAChC;EAEAE,UAAUA,CAAA;IACR,MAAMvD,IAAI,GAAG,IAAI,CAACiD,gBAAgB;IAClC,OAAOjD,IAAI,EAAEqD,IAAI,KAAK,UAAU;EAClC;EAEA;EACAG,WAAWA,CAACC,MAAA,GAAiB,qBAAqB;IAChD,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAACO,eAAe,CAACD,MAAM,CAAC;MAC5B,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAEAE,qBAAqBA,CAACF,MAAA,GAAiB,qBAAqB;IAC1D,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAACO,eAAe,CAACD,MAAM,CAAC;MAC5B,OAAO,KAAK;;IAGd,IAAI,CAAC,IAAI,CAACL,OAAO,EAAE,EAAE;MACnB,IAAI,CAACQ,aAAa,CAAC,aAAa,EAAEH,MAAM,CAAC;MACzC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEAI,mBAAmBA,CAACJ,MAAA,GAAiB,qBAAqB;IACxD,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAACO,eAAe,CAACD,MAAM,CAAC;MAC5B,OAAO,KAAK;;IAGd,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE,EAAE;MACtB,IAAI,CAACK,aAAa,CAAC,UAAU,EAAEH,MAAM,CAAC;MACtC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQC,eAAeA,CAACD,MAAc;IACpC,MAAMK,OAAO,GAAG,mBAAmBL,MAAM,EAAE;IAC3C,IAAIM,OAAO,CAAC,GAAGD,OAAO,gCAAgC,CAAC,EAAE;MACvD,IAAI,CAAC7E,MAAM,CAAC+C,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QACpCgC,WAAW,EAAE;UAAEC,SAAS,EAAE,IAAI,CAAChF,MAAM,CAACiF;QAAG;OAC1C,CAAC;;EAEN;EAEQN,aAAaA,CAACO,YAAoB,EAAEV,MAAc;IACxDW,KAAK,CAAC,QAAQD,YAAY,SAASV,MAAM,yBAAyBU,YAAY,WAAW,CAAC;EAC5F;EAEA;EACAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACR,mBAAmB,CAAC,YAAY,CAAC;EAC/C;EAEAS,UAAUA,CAAA;IACR,OAAO,IAAI,CAACT,mBAAmB,CAAC,kBAAkB,CAAC;EACrD;EAEAU,YAAYA,CAAA;IACV,OAAO,IAAI,CAACV,mBAAmB,CAAC,mBAAmB,CAAC;EACtD;EAEAW,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACX,mBAAmB,CAAC,uBAAuB,CAAC;EAC1D;EAEAY,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACZ,mBAAmB,CAAC,gBAAgB,CAAC;EACnD;EAEA;EACAa,cAAcA,CAAA;IACZ,MAAMhF,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,OAAOD,KAAK,GAAG;MAAE,eAAe,EAAE,UAAUA,KAAK;IAAE,CAAE,GAAG,EAAE;EAC5D;;;uBAjSWZ,WAAW,EAAA6F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXlG,WAAW;MAAAmG,OAAA,EAAXnG,WAAW,CAAAoG,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}