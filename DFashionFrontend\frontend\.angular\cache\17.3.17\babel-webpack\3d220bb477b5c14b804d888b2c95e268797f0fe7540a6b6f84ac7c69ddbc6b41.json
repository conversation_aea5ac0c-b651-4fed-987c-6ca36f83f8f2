{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction VendorAnalyticsComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"span\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", data_r1.percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(3, 3, data_r1.value, \"1.0-0\"), \"\");\n  }\n}\nfunction VendorAnalyticsComponent_span_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const label_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(label_r2);\n  }\n}\nfunction VendorAnalyticsComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"div\", 41);\n    i0.ɵɵelementStart(2, \"span\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", item_r3.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r3.value, \"%\");\n  }\n}\nfunction VendorAnalyticsComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45);\n    i0.ɵɵelement(2, \"img\", 46);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r4.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 7, product_r4.views, \"1.0-0\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r4.orders);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(12, 10, product_r4.revenue, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", product_r4.conversion, \"%\");\n  }\n}\nexport let VendorAnalyticsComponent = /*#__PURE__*/(() => {\n  class VendorAnalyticsComponent {\n    constructor() {\n      this.selectedPeriod = '30';\n      this.analytics = {\n        revenue: 125000,\n        orders: 150,\n        views: 12500,\n        conversionRate: 3.2\n      };\n      this.revenueData = [{\n        value: 15000,\n        percentage: 60\n      }, {\n        value: 18000,\n        percentage: 72\n      }, {\n        value: 22000,\n        percentage: 88\n      }, {\n        value: 25000,\n        percentage: 100\n      }, {\n        value: 20000,\n        percentage: 80\n      }, {\n        value: 23000,\n        percentage: 92\n      }, {\n        value: 28000,\n        percentage: 100\n      }];\n      this.chartLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n      this.orderStatusData = [{\n        label: 'Delivered',\n        value: 65,\n        color: '#28a745'\n      }, {\n        label: 'Shipped',\n        value: 20,\n        color: '#007bff'\n      }, {\n        label: 'Confirmed',\n        value: 10,\n        color: '#ffc107'\n      }, {\n        label: 'Pending',\n        value: 5,\n        color: '#dc3545'\n      }];\n      this.topProducts = [{\n        name: 'Summer Dress',\n        image: '/assets/images/product1.jpg',\n        views: 2500,\n        orders: 45,\n        revenue: 134550,\n        conversion: 1.8\n      }, {\n        name: 'Casual Shirt',\n        image: '/assets/images/product2.jpg',\n        views: 1800,\n        orders: 32,\n        revenue: 51168,\n        conversion: 1.7\n      }, {\n        name: 'Sneakers',\n        image: '/assets/images/product3.jpg',\n        views: 1200,\n        orders: 28,\n        revenue: 139720,\n        conversion: 2.3\n      }];\n      this.socialStats = {\n        posts: {\n          total: 12,\n          likes: 456,\n          comments: 89,\n          shares: 34\n        },\n        stories: {\n          total: 8,\n          views: 1234,\n          replies: 67,\n          productClicks: 234\n        }\n      };\n    }\n    ngOnInit() {\n      this.loadAnalytics();\n    }\n    onPeriodChange() {\n      this.loadAnalytics();\n    }\n    loadAnalytics() {\n      // TODO: Implement API call to get analytics data based on selected period\n      console.log('Loading analytics for period:', this.selectedPeriod);\n    }\n    static {\n      this.ɵfac = function VendorAnalyticsComponent_Factory(t) {\n        return new (t || VendorAnalyticsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VendorAnalyticsComponent,\n        selectors: [[\"app-vendor-analytics\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 139,\n        vars: 23,\n        consts: [[1, \"vendor-analytics-container\"], [1, \"header\"], [1, \"date-filter\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"7\"], [\"value\", \"30\"], [\"value\", \"90\"], [\"value\", \"365\"], [1, \"metrics-grid\"], [1, \"metric-card\"], [1, \"metric-icon\"], [1, \"fas\", \"fa-rupee-sign\"], [1, \"metric-content\"], [1, \"metric-change\", \"positive\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-percentage\"], [1, \"metric-change\", \"negative\"], [1, \"charts-section\"], [1, \"chart-card\"], [1, \"chart-placeholder\"], [1, \"chart-bars\"], [\"class\", \"bar\", 3, \"height\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-labels\"], [4, \"ngFor\", \"ngForOf\"], [1, \"pie-chart\"], [\"class\", \"pie-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"top-products-section\"], [1, \"products-table\"], [1, \"table-header\"], [\"class\", \"table-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"social-performance\"], [1, \"performance-grid\"], [1, \"performance-card\"], [1, \"performance-stats\"], [1, \"stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"bar\"], [1, \"bar-value\"], [1, \"pie-item\"], [1, \"pie-color\"], [1, \"pie-label\"], [1, \"pie-value\"], [1, \"table-row\"], [1, \"product-info\"], [3, \"src\", \"alt\"]],\n        template: function VendorAnalyticsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Analytics Dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"select\", 3);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function VendorAnalyticsComponent_Template_select_ngModelChange_5_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedPeriod, $event) || (ctx.selectedPeriod = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"change\", function VendorAnalyticsComponent_Template_select_change_5_listener() {\n              return ctx.onPeriodChange();\n            });\n            i0.ɵɵelementStart(6, \"option\", 4);\n            i0.ɵɵtext(7, \"Last 7 days\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"option\", 5);\n            i0.ɵɵtext(9, \"Last 30 days\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"option\", 6);\n            i0.ɵɵtext(11, \"Last 3 months\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"option\", 7);\n            i0.ɵɵtext(13, \"Last year\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9)(16, \"div\", 10);\n            i0.ɵɵelement(17, \"i\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 12)(19, \"h3\");\n            i0.ɵɵtext(20);\n            i0.ɵɵpipe(21, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"p\");\n            i0.ɵɵtext(23, \"Total Revenue\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"span\", 13);\n            i0.ɵɵtext(25, \"+12.5%\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 9)(27, \"div\", 10);\n            i0.ɵɵelement(28, \"i\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 12)(30, \"h3\");\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"p\");\n            i0.ɵɵtext(33, \"Total Orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"span\", 13);\n            i0.ɵɵtext(35, \"+8.3%\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10);\n            i0.ɵɵelement(38, \"i\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\", 12)(40, \"h3\");\n            i0.ɵɵtext(41);\n            i0.ɵɵpipe(42, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"p\");\n            i0.ɵɵtext(44, \"Product Views\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"span\", 13);\n            i0.ɵɵtext(46, \"+15.7%\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(47, \"div\", 9)(48, \"div\", 10);\n            i0.ɵɵelement(49, \"i\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"div\", 12)(51, \"h3\");\n            i0.ɵɵtext(52);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"p\");\n            i0.ɵɵtext(54, \"Conversion Rate\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"span\", 17);\n            i0.ɵɵtext(56, \"-2.1%\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(57, \"div\", 18)(58, \"div\", 19)(59, \"h3\");\n            i0.ɵɵtext(60, \"Revenue Trend\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"div\", 20)(62, \"div\", 21);\n            i0.ɵɵtemplate(63, VendorAnalyticsComponent_div_63_Template, 4, 6, \"div\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"div\", 23);\n            i0.ɵɵtemplate(65, VendorAnalyticsComponent_span_65_Template, 2, 1, \"span\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(66, \"div\", 19)(67, \"h3\");\n            i0.ɵɵtext(68, \"Order Status Distribution\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"div\", 25);\n            i0.ɵɵtemplate(70, VendorAnalyticsComponent_div_70_Template, 6, 4, \"div\", 26);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(71, \"div\", 27)(72, \"h3\");\n            i0.ɵɵtext(73, \"Top Performing Products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"div\", 28)(75, \"div\", 29)(76, \"span\");\n            i0.ɵɵtext(77, \"Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"span\");\n            i0.ɵɵtext(79, \"Views\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"span\");\n            i0.ɵɵtext(81, \"Orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"span\");\n            i0.ɵɵtext(83, \"Revenue\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"span\");\n            i0.ɵɵtext(85, \"Conversion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(86, VendorAnalyticsComponent_div_86_Template, 15, 13, \"div\", 30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(87, \"div\", 31)(88, \"h3\");\n            i0.ɵɵtext(89, \"Content Performance\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(90, \"div\", 32)(91, \"div\", 33)(92, \"h4\");\n            i0.ɵɵtext(93, \"Posts\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"div\", 34)(95, \"div\", 35)(96, \"span\", 36);\n            i0.ɵɵtext(97);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"span\", 37);\n            i0.ɵɵtext(99, \"Total Posts\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(100, \"div\", 35)(101, \"span\", 36);\n            i0.ɵɵtext(102);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"span\", 37);\n            i0.ɵɵtext(104, \"Total Likes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(105, \"div\", 35)(106, \"span\", 36);\n            i0.ɵɵtext(107);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"span\", 37);\n            i0.ɵɵtext(109, \"Comments\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(110, \"div\", 35)(111, \"span\", 36);\n            i0.ɵɵtext(112);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(113, \"span\", 37);\n            i0.ɵɵtext(114, \"Shares\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(115, \"div\", 33)(116, \"h4\");\n            i0.ɵɵtext(117, \"Stories\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"div\", 34)(119, \"div\", 35)(120, \"span\", 36);\n            i0.ɵɵtext(121);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(122, \"span\", 37);\n            i0.ɵɵtext(123, \"Total Stories\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(124, \"div\", 35)(125, \"span\", 36);\n            i0.ɵɵtext(126);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(127, \"span\", 37);\n            i0.ɵɵtext(128, \"Total Views\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(129, \"div\", 35)(130, \"span\", 36);\n            i0.ɵɵtext(131);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(132, \"span\", 37);\n            i0.ɵɵtext(133, \"Replies\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(134, \"div\", 35)(135, \"span\", 36);\n            i0.ɵɵtext(136);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"span\", 37);\n            i0.ɵɵtext(138, \"Product Clicks\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedPeriod);\n            i0.ɵɵadvance(15);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(21, 17, ctx.analytics.revenue, \"1.0-0\"), \"\");\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate(ctx.analytics.orders);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(42, 20, ctx.analytics.views, \"1.0-0\"));\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"\", ctx.analytics.conversionRate, \"%\");\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.revenueData);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.chartLabels);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.orderStatusData);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.topProducts);\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate(ctx.socialStats.posts.total);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.socialStats.posts.likes);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.socialStats.posts.comments);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.socialStats.posts.shares);\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate(ctx.socialStats.stories.total);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.socialStats.stories.views);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.socialStats.stories.replies);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.socialStats.stories.productClicks);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.DecimalPipe, FormsModule, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel],\n        styles: [\".vendor-analytics-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600}.date-filter[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:8px 12px;border:1px solid #ddd;border-radius:6px;font-size:.9rem}.metrics-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:30px}.metric-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:24px;box-shadow:0 2px 8px #0000001a;display:flex;align-items:center;gap:16px}.metric-icon[_ngcontent-%COMP%]{width:50px;height:50px;background:#f0f8ff;border-radius:12px;display:flex;align-items:center;justify-content:center;color:#007bff;font-size:1.2rem}.metric-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:700;margin-bottom:4px;color:#333}.metric-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:8px}.metric-change[_ngcontent-%COMP%]{font-size:.8rem;font-weight:500}.metric-change.positive[_ngcontent-%COMP%]{color:#28a745}.metric-change.negative[_ngcontent-%COMP%]{color:#dc3545}.charts-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr;gap:24px;margin-bottom:30px}.chart-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:24px;box-shadow:0 2px 8px #0000001a}.chart-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:20px}.chart-placeholder[_ngcontent-%COMP%]{height:200px;position:relative}.chart-bars[_ngcontent-%COMP%]{display:flex;align-items:flex-end;height:160px;gap:8px;margin-bottom:10px}.bar[_ngcontent-%COMP%]{flex:1;background:linear-gradient(to top,#007bff,#66b3ff);border-radius:4px 4px 0 0;position:relative;min-height:20px}.bar-value[_ngcontent-%COMP%]{position:absolute;top:-20px;left:50%;transform:translate(-50%);font-size:.7rem;color:#666}.chart-labels[_ngcontent-%COMP%]{display:flex;justify-content:space-between;font-size:.8rem;color:#666}.pie-chart[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.pie-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.pie-color[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:50%}.pie-label[_ngcontent-%COMP%]{flex:1;font-size:.9rem}.pie-value[_ngcontent-%COMP%]{font-weight:600;color:#333}.top-products-section[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:24px;box-shadow:0 2px 8px #0000001a;margin-bottom:30px}.top-products-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:20px}.products-table[_ngcontent-%COMP%]{display:flex;flex-direction:column}.table-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1fr 1fr;gap:16px;padding:12px 0;border-bottom:2px solid #eee;font-weight:600;color:#333}.table-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1fr 1fr;gap:16px;padding:16px 0;border-bottom:1px solid #f0f0f0;align-items:center}.product-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.product-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:cover;border-radius:6px}.social-performance[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:24px;box-shadow:0 2px 8px #0000001a}.social-performance[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:20px}.performance-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:24px}.performance-card[_ngcontent-%COMP%]{border:1px solid #eee;border-radius:8px;padding:20px}.performance-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:16px;color:#333}.performance-stats[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:16px}.stat[_ngcontent-%COMP%]{text-align:center}.stat-value[_ngcontent-%COMP%]{display:block;font-size:1.5rem;font-weight:700;color:#333;margin-bottom:4px}.stat-label[_ngcontent-%COMP%]{font-size:.8rem;color:#666;text-transform:uppercase;letter-spacing:.5px}@media (max-width: 768px){.header[_ngcontent-%COMP%]{flex-direction:column;gap:16px;align-items:stretch}.charts-section[_ngcontent-%COMP%], .performance-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:8px}.table-header[_ngcontent-%COMP%]{display:none}.table-row[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:16px;margin-bottom:8px}}\"]\n      });\n    }\n  }\n  return VendorAnalyticsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}