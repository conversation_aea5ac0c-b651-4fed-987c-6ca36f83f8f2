{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\nvar ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n  getEngine() {\n    const tapticEngine = window.TapticEngine;\n    if (tapticEngine) {\n      // Cordova\n      // TODO FW-4707 - Remove this in Ionic 8\n      return tapticEngine;\n    }\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n      // Capacitor\n      return capacitor.Plugins.Haptics;\n    }\n    return undefined;\n  },\n  available() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return false;\n    }\n    const capacitor = getCapacitor();\n    /**\n     * Developers can manually import the\n     * Haptics plugin in their app which will cause\n     * getEngine to return the Haptics engine. However,\n     * the Haptics engine will throw an error if\n     * used in a web browser that does not support\n     * the Vibrate API. This check avoids that error\n     * if the browser does not support the Vibrate API.\n     */\n    if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n    }\n    return true;\n  },\n  isCordova() {\n    return window.TapticEngine !== undefined;\n  },\n  isCapacitor() {\n    return getCapacitor() !== undefined;\n  },\n  impact(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? options.style : options.style.toLowerCase();\n    engine.impact({\n      style\n    });\n  },\n  notification(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const type = this.isCapacitor() ? options.type : options.type.toLowerCase();\n    engine.notification({\n      type\n    });\n  },\n  selection() {\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? ImpactStyle.Light : 'light';\n    this.impact({\n      style\n    });\n  },\n  selectionStart() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionStart();\n    } else {\n      engine.gestureSelectionStart();\n    }\n  },\n  selectionChanged() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionChanged();\n    } else {\n      engine.gestureSelectionChanged();\n    }\n  },\n  selectionEnd() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionEnd();\n    } else {\n      engine.gestureSelectionEnd();\n    }\n  }\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n  return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n  hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n  hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n  hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n  hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = options => {\n  hapticAvailable() && HapticEngine.impact(options);\n};\nexport { ImpactStyle as I, hapticSelectionStart as a, hapticSelectionChanged as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };", "map": {"version": 3, "names": ["g", "getCapacitor", "ImpactStyle", "NotificationType", "HapticEngine", "getEngine", "tapticEngine", "window", "TapticEngine", "capacitor", "isPluginAvailable", "Plugins", "Haptics", "undefined", "available", "engine", "getPlatform", "navigator", "vibrate", "<PERSON><PERSON><PERSON><PERSON>", "isCapacitor", "impact", "options", "style", "toLowerCase", "notification", "type", "selection", "Light", "selectionStart", "gestureSelectionStart", "selectionChanged", "gestureSelectionChanged", "selectionEnd", "gestureSelectionEnd", "hapticAvailable", "hapticSelection", "hapticSelectionStart", "hapticSelectionChanged", "hapticSelectionEnd", "hapticImpact", "I", "a", "b", "c", "d", "h"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/haptic-554688a5.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\n\nvar ImpactStyle;\n(function (ImpactStyle) {\n    /**\n     * A collision between large, heavy user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Heavy\"] = \"HEAVY\";\n    /**\n     * A collision between moderately sized user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Medium\"] = \"MEDIUM\";\n    /**\n     * A collision between small, light user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n    /**\n     * A notification feedback type indicating that a task has completed successfully\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Success\"] = \"SUCCESS\";\n    /**\n     * A notification feedback type indicating that a task has produced a warning\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Warning\"] = \"WARNING\";\n    /**\n     * A notification feedback type indicating that a task has failed\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n    getEngine() {\n        const tapticEngine = window.TapticEngine;\n        if (tapticEngine) {\n            // Cordova\n            // TODO FW-4707 - Remove this in Ionic 8\n            return tapticEngine;\n        }\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n            // Capacitor\n            return capacitor.Plugins.Haptics;\n        }\n        return undefined;\n    },\n    available() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return false;\n        }\n        const capacitor = getCapacitor();\n        /**\n         * Developers can manually import the\n         * Haptics plugin in their app which will cause\n         * getEngine to return the Haptics engine. However,\n         * the Haptics engine will throw an error if\n         * used in a web browser that does not support\n         * the Vibrate API. This check avoids that error\n         * if the browser does not support the Vibrate API.\n         */\n        if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n            // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n            return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n        }\n        return true;\n    },\n    isCordova() {\n        return window.TapticEngine !== undefined;\n    },\n    isCapacitor() {\n        return getCapacitor() !== undefined;\n    },\n    impact(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        /**\n         * To provide backwards compatibility with Cordova apps,\n         * we convert the style to lowercase.\n         *\n         * TODO: FW-4707 - Remove this in Ionic 8\n         */\n        const style = this.isCapacitor() ? options.style : options.style.toLowerCase();\n        engine.impact({ style });\n    },\n    notification(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        /**\n         * To provide backwards compatibility with Cordova apps,\n         * we convert the style to lowercase.\n         *\n         * TODO: FW-4707 - Remove this in Ionic 8\n         */\n        const type = this.isCapacitor() ? options.type : options.type.toLowerCase();\n        engine.notification({ type });\n    },\n    selection() {\n        /**\n         * To provide backwards compatibility with Cordova apps,\n         * we convert the style to lowercase.\n         *\n         * TODO: FW-4707 - Remove this in Ionic 8\n         */\n        const style = this.isCapacitor() ? ImpactStyle.Light : 'light';\n        this.impact({ style });\n    },\n    selectionStart() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        if (this.isCapacitor()) {\n            engine.selectionStart();\n        }\n        else {\n            engine.gestureSelectionStart();\n        }\n    },\n    selectionChanged() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        if (this.isCapacitor()) {\n            engine.selectionChanged();\n        }\n        else {\n            engine.gestureSelectionChanged();\n        }\n    },\n    selectionEnd() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        if (this.isCapacitor()) {\n            engine.selectionEnd();\n        }\n        else {\n            engine.gestureSelectionEnd();\n        }\n    },\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n    return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n    hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n    hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n    hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n    hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = (options) => {\n    hapticAvailable() && HapticEngine.impact(options);\n};\n\nexport { ImpactStyle as I, hapticSelectionStart as a, hapticSelectionChanged as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,YAAY,QAAQ,yBAAyB;AAE3D,IAAIC,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpB;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACvC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,YAAY,GAAG;EACjBC,SAASA,CAAA,EAAG;IACR,MAAMC,YAAY,GAAGC,MAAM,CAACC,YAAY;IACxC,IAAIF,YAAY,EAAE;MACd;MACA;MACA,OAAOA,YAAY;IACvB;IACA,MAAMG,SAAS,GAAGR,YAAY,CAAC,CAAC;IAChC,IAAIQ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,SAAS,CAAC,EAAE;MAC9F;MACA,OAAOD,SAAS,CAACE,OAAO,CAACC,OAAO;IACpC;IACA,OAAOC,SAAS;EACpB,CAAC;EACDC,SAASA,CAAA,EAAG;IACR,MAAMC,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACU,MAAM,EAAE;MACT,OAAO,KAAK;IAChB;IACA,MAAMN,SAAS,GAAGR,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACQ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACO,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE;MAC3F;MACA,OAAO,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAKL,SAAS;IAC9E;IACA,OAAO,IAAI;EACf,CAAC;EACDM,SAASA,CAAA,EAAG;IACR,OAAOZ,MAAM,CAACC,YAAY,KAAKK,SAAS;EAC5C,CAAC;EACDO,WAAWA,CAAA,EAAG;IACV,OAAOnB,YAAY,CAAC,CAAC,KAAKY,SAAS;EACvC,CAAC;EACDQ,MAAMA,CAACC,OAAO,EAAE;IACZ,MAAMP,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACU,MAAM,EAAE;MACT;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMQ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC;IAC9ET,MAAM,CAACM,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EAC5B,CAAC;EACDE,YAAYA,CAACH,OAAO,EAAE;IAClB,MAAMP,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACU,MAAM,EAAE;MACT;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMW,IAAI,GAAG,IAAI,CAACN,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,CAACF,WAAW,CAAC,CAAC;IAC3ET,MAAM,CAACU,YAAY,CAAC;MAAEC;IAAK,CAAC,CAAC;EACjC,CAAC;EACDC,SAASA,CAAA,EAAG;IACR;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMJ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGlB,WAAW,CAAC0B,KAAK,GAAG,OAAO;IAC9D,IAAI,CAACP,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EAC1B,CAAC;EACDM,cAAcA,CAAA,EAAG;IACb,MAAMd,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACU,MAAM,EAAE;MACT;IACJ;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACpBL,MAAM,CAACc,cAAc,CAAC,CAAC;IAC3B,CAAC,MACI;MACDd,MAAM,CAACe,qBAAqB,CAAC,CAAC;IAClC;EACJ,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACf,MAAMhB,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACU,MAAM,EAAE;MACT;IACJ;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACpBL,MAAM,CAACgB,gBAAgB,CAAC,CAAC;IAC7B,CAAC,MACI;MACDhB,MAAM,CAACiB,uBAAuB,CAAC,CAAC;IACpC;EACJ,CAAC;EACDC,YAAYA,CAAA,EAAG;IACX,MAAMlB,MAAM,GAAG,IAAI,CAACV,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACU,MAAM,EAAE;MACT;IACJ;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACpBL,MAAM,CAACkB,YAAY,CAAC,CAAC;IACzB,CAAC,MACI;MACDlB,MAAM,CAACmB,mBAAmB,CAAC,CAAC;IAChC;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,OAAO/B,YAAY,CAACU,SAAS,CAAC,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMsB,eAAe,GAAGA,CAAA,KAAM;EAC1BD,eAAe,CAAC,CAAC,IAAI/B,YAAY,CAACuB,SAAS,CAAC,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA,MAAMU,oBAAoB,GAAGA,CAAA,KAAM;EAC/BF,eAAe,CAAC,CAAC,IAAI/B,YAAY,CAACyB,cAAc,CAAC,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA,MAAMS,sBAAsB,GAAGA,CAAA,KAAM;EACjCH,eAAe,CAAC,CAAC,IAAI/B,YAAY,CAAC2B,gBAAgB,CAAC,CAAC;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;EAC7BJ,eAAe,CAAC,CAAC,IAAI/B,YAAY,CAAC6B,YAAY,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMO,YAAY,GAAIlB,OAAO,IAAK;EAC9Ba,eAAe,CAAC,CAAC,IAAI/B,YAAY,CAACiB,MAAM,CAACC,OAAO,CAAC;AACrD,CAAC;AAED,SAASpB,WAAW,IAAIuC,CAAC,EAAEJ,oBAAoB,IAAIK,CAAC,EAAEJ,sBAAsB,IAAIK,CAAC,EAAEP,eAAe,IAAIQ,CAAC,EAAEJ,YAAY,IAAIK,CAAC,EAAEN,kBAAkB,IAAIO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}