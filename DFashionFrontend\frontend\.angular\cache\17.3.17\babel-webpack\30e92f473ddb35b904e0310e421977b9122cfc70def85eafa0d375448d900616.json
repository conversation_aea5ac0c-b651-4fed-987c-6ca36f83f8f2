{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AdminProductService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://********:5000/api/admin'; // Direct IP for testing\n    this.productsSubject = new BehaviorSubject([]);\n    this.products$ = this.productsSubject.asObservable();\n  }\n  // Get all products with filters (Admin API)\n  getProducts(filters = {}) {\n    let params = new HttpParams();\n    Object.keys(filters).forEach(key => {\n      const value = filters[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n    return this.http.get(`${this.apiUrl}/products`, {\n      params\n    });\n  }\n  // Get product by ID (Admin API)\n  getProductById(id) {\n    return this.http.get(`${this.apiUrl}/products/${id}`);\n  }\n  // Update product status (Admin API)\n  updateProductStatus(id, isActive) {\n    return this.http.put(`${this.apiUrl}/products/${id}/status`, {\n      isActive\n    });\n  }\n  // Toggle featured status (Admin API)\n  updateFeaturedStatus(id, isFeatured) {\n    return this.http.put(`${this.apiUrl}/products/${id}/featured`, {\n      isFeatured\n    });\n  }\n  // Delete product (Admin API)\n  deleteProduct(id) {\n    return this.http.delete(`${this.apiUrl}/products/${id}`);\n  }\n  // Approve product (for vendor products)\n  approveProduct(id) {\n    return this.http.patch(`${this.apiUrl}/${id}/approve`, {});\n  }\n  // Get product categories\n  getCategories() {\n    return this.http.get(`${this.apiUrl}/categories`);\n  }\n  // Get subcategories by category\n  getSubcategories(category) {\n    return this.http.get(`${this.apiUrl}/categories/${category}/subcategories`);\n  }\n  // Get product brands\n  getBrands() {\n    return this.http.get(`${this.apiUrl}/brands`);\n  }\n  // Upload product images\n  uploadProductImages(productId, files) {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('images', file);\n    });\n    return this.http.post(`${this.apiUrl}/${productId}/images`, formData);\n  }\n  // Delete product image\n  deleteProductImage(productId, imageId) {\n    return this.http.delete(`${this.apiUrl}/${productId}/images/${imageId}`);\n  }\n  // Update product inventory\n  updateInventory(productId, inventory) {\n    return this.http.patch(`${this.apiUrl}/${productId}/inventory`, inventory);\n  }\n  // Get product analytics\n  getProductAnalytics(productId, period = '30d') {\n    return this.http.get(`${this.apiUrl}/${productId}/analytics?period=${period}`);\n  }\n  // Bulk operations\n  bulkUpdateProducts(productIds, updates) {\n    return this.http.patch(`${this.apiUrl}/bulk-update`, {\n      productIds,\n      updates\n    });\n  }\n  bulkDeleteProducts(productIds) {\n    return this.http.delete(`${this.apiUrl}/bulk-delete`, {\n      body: {\n        productIds\n      }\n    });\n  }\n  // Search products\n  searchProducts(query, filters = {}) {\n    const searchFilters = {\n      ...filters,\n      search: query\n    };\n    return this.getProducts(searchFilters);\n  }\n  // Get featured products\n  getFeaturedProducts(limit = 10) {\n    return this.http.get(`${this.apiUrl}/featured?limit=${limit}`);\n  }\n  // Get products by vendor\n  getProductsByVendor(vendorId, filters = {}) {\n    const vendorFilters = {\n      ...filters,\n      vendor: vendorId\n    };\n    return this.getProducts(vendorFilters);\n  }\n  // Update products subject\n  updateProductsSubject(products) {\n    this.productsSubject.next(products);\n  }\n  // Get current products\n  getCurrentProducts() {\n    return this.productsSubject.value;\n  }\n  static {\n    this.ɵfac = function AdminProductService_Factory(t) {\n      return new (t || AdminProductService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminProductService,\n      factory: AdminProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "AdminProductService", "constructor", "http", "apiUrl", "productsSubject", "products$", "asObservable", "getProducts", "filters", "params", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "set", "toString", "get", "getProductById", "id", "updateProductStatus", "isActive", "put", "updateFeaturedStatus", "isFeatured", "deleteProduct", "delete", "approveProduct", "patch", "getCategories", "getSubcategories", "category", "getBrands", "uploadProductImages", "productId", "files", "formData", "FormData", "file", "append", "post", "deleteProductImage", "imageId", "updateInventory", "inventory", "getProductAnalytics", "period", "bulkUpdateProducts", "productIds", "updates", "bulkDeleteProducts", "body", "searchProducts", "query", "searchFilters", "search", "getFeaturedProducts", "limit", "getProductsByVendor", "vendorId", "vendorFilters", "vendor", "updateProductsSubject", "products", "next", "getCurrentProducts", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\admin\\services\\product.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\n\nexport interface Product {\n  _id?: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice?: number;\n  discount: number;\n  category: string;\n  subcategory: string;\n  brand: string;\n  images: ProductImage[];\n  sizes: ProductSize[];\n  colors: ProductColor[];\n  vendor?: any;\n  isActive: boolean;\n  isFeatured: boolean;\n  isApproved?: boolean;\n  rating?: {\n    average: number;\n    count: number;\n  };\n  analytics?: {\n    views: number;\n    likes: number;\n    shares: number;\n    purchases: number;\n  };\n  tags?: string[];\n  createdAt?: string;\n  updatedAt?: string;\n}\n\nexport interface ProductImage {\n  url: string;\n  alt: string;\n  isPrimary: boolean;\n}\n\nexport interface ProductSize {\n  size: string;\n  stock: number;\n}\n\nexport interface ProductColor {\n  name: string;\n  code: string;\n  images: string[];\n}\n\nexport interface ProductFilters {\n  search?: string;\n  category?: string;\n  subcategory?: string;\n  brand?: string;\n  minPrice?: number;\n  maxPrice?: number;\n  isActive?: boolean;\n  isFeatured?: boolean;\n  vendor?: string;\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface ProductResponse {\n  products: Product[];\n  total: number;\n  page: number;\n  totalPages: number;\n}\n\nexport interface AdminProductResponse {\n  success: boolean;\n  data: {\n    products: Product[];\n    pagination: {\n      currentPage: number;\n      totalPages: number;\n      totalProducts: number;\n      hasNextPage: boolean;\n      hasPrevPage: boolean;\n    };\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminProductService {\n  private apiUrl = 'http://********:5000/api/admin'; // Direct IP for testing\n  private productsSubject = new BehaviorSubject<Product[]>([]);\n  public products$ = this.productsSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  // Get all products with filters (Admin API)\n  getProducts(filters: ProductFilters = {}): Observable<AdminProductResponse> {\n    let params = new HttpParams();\n\n    Object.keys(filters).forEach(key => {\n      const value = (filters as any)[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n\n    return this.http.get<AdminProductResponse>(`${this.apiUrl}/products`, { params });\n  }\n\n  // Get product by ID (Admin API)\n  getProductById(id: string): Observable<{success: boolean; data: Product}> {\n    return this.http.get<{success: boolean; data: Product}>(`${this.apiUrl}/products/${id}`);\n  }\n\n  // Update product status (Admin API)\n  updateProductStatus(id: string, isActive: boolean): Observable<{success: boolean; message: string; data: Product}> {\n    return this.http.put<{success: boolean; message: string; data: Product}>(`${this.apiUrl}/products/${id}/status`, { isActive });\n  }\n\n  // Toggle featured status (Admin API)\n  updateFeaturedStatus(id: string, isFeatured: boolean): Observable<{success: boolean; message: string; data: Product}> {\n    return this.http.put<{success: boolean; message: string; data: Product}>(`${this.apiUrl}/products/${id}/featured`, { isFeatured });\n  }\n\n  // Delete product (Admin API)\n  deleteProduct(id: string): Observable<{success: boolean; message: string}> {\n    return this.http.delete<{success: boolean; message: string}>(`${this.apiUrl}/products/${id}`);\n  }\n\n  // Approve product (for vendor products)\n  approveProduct(id: string): Observable<Product> {\n    return this.http.patch<Product>(`${this.apiUrl}/${id}/approve`, {});\n  }\n\n  // Get product categories\n  getCategories(): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/categories`);\n  }\n\n  // Get subcategories by category\n  getSubcategories(category: string): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/categories/${category}/subcategories`);\n  }\n\n  // Get product brands\n  getBrands(): Observable<string[]> {\n    return this.http.get<string[]>(`${this.apiUrl}/brands`);\n  }\n\n  // Upload product images\n  uploadProductImages(productId: string, files: File[]): Observable<ProductImage[]> {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('images', file);\n    });\n    \n    return this.http.post<ProductImage[]>(`${this.apiUrl}/${productId}/images`, formData);\n  }\n\n  // Delete product image\n  deleteProductImage(productId: string, imageId: string): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/${productId}/images/${imageId}`);\n  }\n\n  // Update product inventory\n  updateInventory(productId: string, inventory: any): Observable<Product> {\n    return this.http.patch<Product>(`${this.apiUrl}/${productId}/inventory`, inventory);\n  }\n\n  // Get product analytics\n  getProductAnalytics(productId: string, period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/${productId}/analytics?period=${period}`);\n  }\n\n  // Bulk operations\n  bulkUpdateProducts(productIds: string[], updates: Partial<Product>): Observable<any> {\n    return this.http.patch<any>(`${this.apiUrl}/bulk-update`, {\n      productIds,\n      updates\n    });\n  }\n\n  bulkDeleteProducts(productIds: string[]): Observable<any> {\n    return this.http.delete<any>(`${this.apiUrl}/bulk-delete`, {\n      body: { productIds }\n    });\n  }\n\n  // Search products\n  searchProducts(query: string, filters: ProductFilters = {}): Observable<AdminProductResponse> {\n    const searchFilters = { ...filters, search: query };\n    return this.getProducts(searchFilters);\n  }\n\n  // Get featured products\n  getFeaturedProducts(limit: number = 10): Observable<Product[]> {\n    return this.http.get<Product[]>(`${this.apiUrl}/featured?limit=${limit}`);\n  }\n\n  // Get products by vendor\n  getProductsByVendor(vendorId: string, filters: ProductFilters = {}): Observable<AdminProductResponse> {\n    const vendorFilters = { ...filters, vendor: vendorId };\n    return this.getProducts(vendorFilters);\n  }\n\n  // Update products subject\n  updateProductsSubject(products: Product[]): void {\n    this.productsSubject.next(products);\n  }\n\n  // Get current products\n  getCurrentProducts(): Product[] {\n    return this.productsSubject.value;\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAAqBC,eAAe,QAAQ,MAAM;;;AA4FlD,OAAM,MAAOC,mBAAmB;EAK9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,MAAM,GAAG,gCAAgC,CAAC,CAAC;IAC3C,KAAAC,eAAe,GAAG,IAAIL,eAAe,CAAY,EAAE,CAAC;IACrD,KAAAM,SAAS,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEf;EAEvC;EACAC,WAAWA,CAACC,OAAA,GAA0B,EAAE;IACtC,IAAIC,MAAM,GAAG,IAAIX,UAAU,EAAE;IAE7BY,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;MACjC,MAAMC,KAAK,GAAIN,OAAe,CAACK,GAAG,CAAC;MACnC,IAAIC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,EAAE,CAAC;;IAE9C,CAAC,CAAC;IAEF,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAuB,GAAG,IAAI,CAACf,MAAM,WAAW,EAAE;MAAEM;IAAM,CAAE,CAAC;EACnF;EAEA;EACAU,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAAClB,IAAI,CAACgB,GAAG,CAAoC,GAAG,IAAI,CAACf,MAAM,aAAaiB,EAAE,EAAE,CAAC;EAC1F;EAEA;EACAC,mBAAmBA,CAACD,EAAU,EAAEE,QAAiB;IAC/C,OAAO,IAAI,CAACpB,IAAI,CAACqB,GAAG,CAAqD,GAAG,IAAI,CAACpB,MAAM,aAAaiB,EAAE,SAAS,EAAE;MAAEE;IAAQ,CAAE,CAAC;EAChI;EAEA;EACAE,oBAAoBA,CAACJ,EAAU,EAAEK,UAAmB;IAClD,OAAO,IAAI,CAACvB,IAAI,CAACqB,GAAG,CAAqD,GAAG,IAAI,CAACpB,MAAM,aAAaiB,EAAE,WAAW,EAAE;MAAEK;IAAU,CAAE,CAAC;EACpI;EAEA;EACAC,aAAaA,CAACN,EAAU;IACtB,OAAO,IAAI,CAAClB,IAAI,CAACyB,MAAM,CAAsC,GAAG,IAAI,CAACxB,MAAM,aAAaiB,EAAE,EAAE,CAAC;EAC/F;EAEA;EACAQ,cAAcA,CAACR,EAAU;IACvB,OAAO,IAAI,CAAClB,IAAI,CAAC2B,KAAK,CAAU,GAAG,IAAI,CAAC1B,MAAM,IAAIiB,EAAE,UAAU,EAAE,EAAE,CAAC;EACrE;EAEA;EACAU,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC5B,IAAI,CAACgB,GAAG,CAAQ,GAAG,IAAI,CAACf,MAAM,aAAa,CAAC;EAC1D;EAEA;EACA4B,gBAAgBA,CAACC,QAAgB;IAC/B,OAAO,IAAI,CAAC9B,IAAI,CAACgB,GAAG,CAAQ,GAAG,IAAI,CAACf,MAAM,eAAe6B,QAAQ,gBAAgB,CAAC;EACpF;EAEA;EACAC,SAASA,CAAA;IACP,OAAO,IAAI,CAAC/B,IAAI,CAACgB,GAAG,CAAW,GAAG,IAAI,CAACf,MAAM,SAAS,CAAC;EACzD;EAEA;EACA+B,mBAAmBA,CAACC,SAAiB,EAAEC,KAAa;IAClD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BF,KAAK,CAACxB,OAAO,CAAC2B,IAAI,IAAG;MACnBF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,OAAO,IAAI,CAACrC,IAAI,CAACuC,IAAI,CAAiB,GAAG,IAAI,CAACtC,MAAM,IAAIgC,SAAS,SAAS,EAAEE,QAAQ,CAAC;EACvF;EAEA;EACAK,kBAAkBA,CAACP,SAAiB,EAAEQ,OAAe;IACnD,OAAO,IAAI,CAACzC,IAAI,CAACyB,MAAM,CAAO,GAAG,IAAI,CAACxB,MAAM,IAAIgC,SAAS,WAAWQ,OAAO,EAAE,CAAC;EAChF;EAEA;EACAC,eAAeA,CAACT,SAAiB,EAAEU,SAAc;IAC/C,OAAO,IAAI,CAAC3C,IAAI,CAAC2B,KAAK,CAAU,GAAG,IAAI,CAAC1B,MAAM,IAAIgC,SAAS,YAAY,EAAEU,SAAS,CAAC;EACrF;EAEA;EACAC,mBAAmBA,CAACX,SAAiB,EAAEY,MAAA,GAAiB,KAAK;IAC3D,OAAO,IAAI,CAAC7C,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACf,MAAM,IAAIgC,SAAS,qBAAqBY,MAAM,EAAE,CAAC;EACrF;EAEA;EACAC,kBAAkBA,CAACC,UAAoB,EAAEC,OAAyB;IAChE,OAAO,IAAI,CAAChD,IAAI,CAAC2B,KAAK,CAAM,GAAG,IAAI,CAAC1B,MAAM,cAAc,EAAE;MACxD8C,UAAU;MACVC;KACD,CAAC;EACJ;EAEAC,kBAAkBA,CAACF,UAAoB;IACrC,OAAO,IAAI,CAAC/C,IAAI,CAACyB,MAAM,CAAM,GAAG,IAAI,CAACxB,MAAM,cAAc,EAAE;MACzDiD,IAAI,EAAE;QAAEH;MAAU;KACnB,CAAC;EACJ;EAEA;EACAI,cAAcA,CAACC,KAAa,EAAE9C,OAAA,GAA0B,EAAE;IACxD,MAAM+C,aAAa,GAAG;MAAE,GAAG/C,OAAO;MAAEgD,MAAM,EAAEF;IAAK,CAAE;IACnD,OAAO,IAAI,CAAC/C,WAAW,CAACgD,aAAa,CAAC;EACxC;EAEA;EACAE,mBAAmBA,CAACC,KAAA,GAAgB,EAAE;IACpC,OAAO,IAAI,CAACxD,IAAI,CAACgB,GAAG,CAAY,GAAG,IAAI,CAACf,MAAM,mBAAmBuD,KAAK,EAAE,CAAC;EAC3E;EAEA;EACAC,mBAAmBA,CAACC,QAAgB,EAAEpD,OAAA,GAA0B,EAAE;IAChE,MAAMqD,aAAa,GAAG;MAAE,GAAGrD,OAAO;MAAEsD,MAAM,EAAEF;IAAQ,CAAE;IACtD,OAAO,IAAI,CAACrD,WAAW,CAACsD,aAAa,CAAC;EACxC;EAEA;EACAE,qBAAqBA,CAACC,QAAmB;IACvC,IAAI,CAAC5D,eAAe,CAAC6D,IAAI,CAACD,QAAQ,CAAC;EACrC;EAEA;EACAE,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC9D,eAAe,CAACU,KAAK;EACnC;;;uBA7HWd,mBAAmB,EAAAmE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBtE,mBAAmB;MAAAuE,OAAA,EAAnBvE,mBAAmB,CAAAwE,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}