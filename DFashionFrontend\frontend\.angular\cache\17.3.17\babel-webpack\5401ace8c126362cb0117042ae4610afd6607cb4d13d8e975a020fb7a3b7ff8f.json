{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ionic/angular\";\nfunction SettingsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"ion-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading settings...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SettingsComponent_div_2_li_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const permission_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", permission_r3, \" \");\n  }\n}\nfunction SettingsComponent_div_2_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtext(3, \"Email Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14);\n    i0.ɵɵtext(5, \"Receive notifications via email\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15)(7, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_div_57_Template_ion_toggle_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.emailNotifications, $event) || (ctx_r1.emailNotifications = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_div_57_Template_ion_toggle_ionChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.emailNotifications);\n  }\n}\nfunction SettingsComponent_div_2_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtext(3, \"Push Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14);\n    i0.ɵɵtext(5, \"Receive push notifications on your device\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15)(7, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_div_58_Template_ion_toggle_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.pushNotifications, $event) || (ctx_r1.pushNotifications = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_div_58_Template_ion_toggle_ionChange_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.pushNotifications);\n  }\n}\nfunction SettingsComponent_div_2_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h3\", 10);\n    i0.ɵɵtext(2, \"Payment & Shipping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Payment Methods\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8, \"Manage your saved payment methods\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_67_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.managePaymentMethods());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 31);\n    i0.ɵɵtext(12, \" Manage \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13);\n    i0.ɵɵtext(16, \"Shipping Addresses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 14);\n    i0.ɵɵtext(18, \"Manage your delivery addresses\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 15)(20, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_67_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.manageAddresses());\n    });\n    i0.ɵɵelement(21, \"ion-icon\", 32);\n    i0.ɵɵtext(22, \" Manage \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SettingsComponent_div_2_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h3\", 10);\n    i0.ɵɵtext(2, \"Vendor Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Vendor Dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8, \"Access vendor-specific settings and preferences\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_68_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToVendorSettings());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 33);\n    i0.ɵɵtext(12, \" Open \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SettingsComponent_div_2_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h3\", 10);\n    i0.ɵɵtext(2, \"Administrator Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Admin Panel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8, \"Access system administration settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_69_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToAdminSettings());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 34);\n    i0.ɵɵtext(12, \" Open \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SettingsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"h1\");\n    i0.ɵɵtext(3, \"Account Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Manage your account preferences and permissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"h3\", 10);\n    i0.ɵɵtext(9, \"Account Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 11)(11, \"div\", 12)(12, \"div\", 13);\n    i0.ɵɵtext(13, \"Account Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 14);\n    i0.ɵɵtext(15, \"Your current role and permissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 15)(17, \"span\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 11)(20, \"div\", 12)(21, \"div\", 13);\n    i0.ɵɵtext(22, \"Profile Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 14);\n    i0.ɵɵtext(24, \"Update your personal information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 15)(26, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editProfile());\n    });\n    i0.ɵɵelement(27, \"ion-icon\", 18);\n    i0.ɵɵtext(28, \" Edit \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 11)(30, \"div\", 12)(31, \"div\", 13);\n    i0.ɵɵtext(32, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 14);\n    i0.ɵɵtext(34, \"Change your account password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 15)(36, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changePassword());\n    });\n    i0.ɵɵelement(37, \"ion-icon\", 19);\n    i0.ɵɵtext(38, \" Change \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"h3\", 10);\n    i0.ɵɵtext(41, \"Your Permissions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"ul\", 20);\n    i0.ɵɵtemplate(45, SettingsComponent_div_2_li_45_Template, 2, 1, \"li\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"h3\", 10);\n    i0.ɵɵtext(48, \"Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 11)(50, \"div\", 12)(51, \"div\", 13);\n    i0.ɵɵtext(52, \"Enable Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 14);\n    i0.ɵɵtext(54, \"Receive notifications about your account activity\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 15)(56, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_Template_ion_toggle_ngModelChange_56_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.notificationsEnabled, $event) || (ctx_r1.notificationsEnabled = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_Template_ion_toggle_ionChange_56_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(57, SettingsComponent_div_2_div_57_Template, 8, 1, \"div\", 23)(58, SettingsComponent_div_2_div_58_Template, 8, 1, \"div\", 23);\n    i0.ɵɵelementStart(59, \"div\", 11)(60, \"div\", 12)(61, \"div\", 13);\n    i0.ɵɵtext(62, \"Marketing Emails\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 14);\n    i0.ɵɵtext(64, \"Receive promotional emails and offers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 15)(66, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_Template_ion_toggle_ngModelChange_66_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.marketingEmails, $event) || (ctx_r1.marketingEmails = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_Template_ion_toggle_ionChange_66_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(67, SettingsComponent_div_2_div_67_Template, 23, 0, \"div\", 24)(68, SettingsComponent_div_2_div_68_Template, 13, 0, \"div\", 24)(69, SettingsComponent_div_2_div_69_Template, 13, 0, \"div\", 24);\n    i0.ɵɵelementStart(70, \"div\", 9)(71, \"h3\", 10);\n    i0.ɵɵtext(72, \"Privacy & Security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 11)(74, \"div\", 12)(75, \"div\", 13);\n    i0.ɵɵtext(76, \"Privacy Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 14);\n    i0.ɵɵtext(78, \"Control who can see your information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(79, \"div\", 15)(80, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_80_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewPrivacySettings());\n    });\n    i0.ɵɵelement(81, \"ion-icon\", 25);\n    i0.ɵɵtext(82, \" Manage \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(83, \"div\", 11)(84, \"div\", 12)(85, \"div\", 13);\n    i0.ɵɵtext(86, \"Security Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(87, \"div\", 14);\n    i0.ɵɵtext(88, \"Two-factor authentication and security options\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 15)(90, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_90_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewSecuritySettings());\n    });\n    i0.ɵɵelement(91, \"ion-icon\", 26);\n    i0.ɵɵtext(92, \" Manage \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(93, \"div\", 9)(94, \"h3\", 10);\n    i0.ɵɵtext(95, \"Account Management\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 11)(97, \"div\", 12)(98, \"div\", 13);\n    i0.ɵɵtext(99, \"Deactivate Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"div\", 14);\n    i0.ɵɵtext(101, \"Temporarily disable your account\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"div\", 15)(103, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_103_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deactivateAccount());\n    });\n    i0.ɵɵelement(104, \"ion-icon\", 28);\n    i0.ɵɵtext(105, \" Deactivate \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(106, \"div\", 11)(107, \"div\", 12)(108, \"div\", 13);\n    i0.ɵɵtext(109, \"Delete Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"div\", 14);\n    i0.ɵɵtext(111, \"Permanently delete your account and all data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(112, \"div\", 15)(113, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_113_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteAccount());\n    });\n    i0.ɵɵelement(114, \"ion-icon\", 29);\n    i0.ɵɵtext(115, \" Delete \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getRoleBadgeClass());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getRoleDisplayName(), \" \");\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate1(\"Based on your \", ctx_r1.getRoleDisplayName(), \" role, you have access to:\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getRolePermissions());\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.notificationsEnabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.notificationsEnabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.notificationsEnabled);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.marketingEmails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canManageAccount());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canAccessVendorSettings());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canAccessAdminSettings());\n  }\n}\nfunction SettingsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"ion-icon\", 36);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Access Denied\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Please log in to access settings.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToLogin());\n    });\n    i0.ɵɵtext(7, \" Login \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SettingsComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n    this.isLoading = true;\n    // Settings state\n    this.notificationsEnabled = true;\n    this.emailNotifications = true;\n    this.pushNotifications = true;\n    this.marketingEmails = false;\n  }\n  ngOnInit() {\n    this.loadUserSettings();\n  }\n  loadUserSettings() {\n    this.currentUser = this.authService.currentUserValue;\n    this.loadNotificationSettings();\n    this.isLoading = false;\n  }\n  loadNotificationSettings() {\n    // Load from localStorage or API\n    const settings = localStorage.getItem('userSettings');\n    if (settings) {\n      const parsed = JSON.parse(settings);\n      this.notificationsEnabled = parsed.notificationsEnabled ?? true;\n      this.emailNotifications = parsed.emailNotifications ?? true;\n      this.pushNotifications = parsed.pushNotifications ?? true;\n      this.marketingEmails = parsed.marketingEmails ?? false;\n    }\n  }\n  saveNotificationSettings() {\n    const settings = {\n      notificationsEnabled: this.notificationsEnabled,\n      emailNotifications: this.emailNotifications,\n      pushNotifications: this.pushNotifications,\n      marketingEmails: this.marketingEmails\n    };\n    localStorage.setItem('userSettings', JSON.stringify(settings));\n    this.showSuccessMessage('Notification settings saved successfully!');\n  }\n  // Role-based access methods\n  canAccessVendorSettings() {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n  canAccessAdminSettings() {\n    return this.authService.isAdmin();\n  }\n  canManageAccount() {\n    return this.authService.isAuthenticated;\n  }\n  // Navigation methods\n  editProfile() {\n    this.router.navigate(['/account/edit-profile']);\n  }\n  changePassword() {\n    this.router.navigate(['/account/change-password']);\n  }\n  managePaymentMethods() {\n    this.router.navigate(['/account/payment-methods']);\n  }\n  manageAddresses() {\n    this.router.navigate(['/account/addresses']);\n  }\n  viewPrivacySettings() {\n    this.router.navigate(['/account/privacy']);\n  }\n  viewSecuritySettings() {\n    this.router.navigate(['/account/security']);\n  }\n  navigateToVendorSettings() {\n    if (this.canAccessVendorSettings()) {\n      this.router.navigate(['/vendor/settings']);\n    }\n  }\n  navigateToAdminSettings() {\n    if (this.canAccessAdminSettings()) {\n      this.router.navigate(['/admin/settings']);\n    }\n  }\n  // Account management\n  deactivateAccount() {\n    if (confirm('Are you sure you want to deactivate your account? This action can be reversed by contacting support.')) {\n      // Implement account deactivation\n      console.log('Account deactivation requested');\n      this.showSuccessMessage('Account deactivation request submitted. You will receive an email confirmation.');\n    }\n  }\n  deleteAccount() {\n    if (confirm('Are you sure you want to permanently delete your account? This action cannot be undone.')) {\n      if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {\n        // Implement account deletion\n        console.log('Account deletion requested');\n        this.showSuccessMessage('Account deletion request submitted. You will receive an email with further instructions.');\n      }\n    }\n  }\n  getRoleDisplayName() {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'Customer';\n      case 'vendor':\n        return 'Vendor';\n      case 'admin':\n        return 'Administrator';\n      default:\n        return 'User';\n    }\n  }\n  getRoleBadgeClass() {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'customer-badge';\n      case 'vendor':\n        return 'vendor-badge';\n      case 'admin':\n        return 'admin-badge';\n      default:\n        return 'role-badge';\n    }\n  }\n  getRolePermissions() {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return ['Browse and purchase products', 'Create and manage wishlist', 'View order history', 'Leave product reviews', 'Follow other users'];\n      case 'vendor':\n        return ['All customer permissions', 'Add and manage products', 'View sales analytics', 'Manage inventory', 'Process orders', 'Create promotional content'];\n      case 'admin':\n        return ['All vendor permissions', 'Manage all users', 'Access system analytics', 'Moderate content', 'Configure system settings', 'Manage vendor approvals'];\n      default:\n        return [];\n    }\n  }\n  showSuccessMessage(message) {\n    // You can replace this with a proper toast/notification service\n    alert(message);\n  }\n  navigateToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  static {\n    this.ɵfac = function SettingsComponent_Factory(t) {\n      return new (t || SettingsComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingsComponent,\n      selectors: [[\"app-settings\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"settings-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"settings-content\", 4, \"ngIf\"], [\"class\", \"no-user-state\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"settings-content\"], [1, \"settings-header\"], [1, \"settings-sections\"], [1, \"settings-section\"], [1, \"section-title\"], [1, \"setting-item\"], [1, \"setting-info\"], [1, \"setting-title\"], [1, \"setting-description\"], [1, \"setting-control\"], [1, \"role-badge\", 3, \"ngClass\"], [1, \"btn-primary\", 3, \"click\"], [\"name\", \"create-outline\"], [\"name\", \"key-outline\"], [1, \"permission-list\"], [\"class\", \"permission-item\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngModelChange\", \"ionChange\", \"ngModel\"], [\"class\", \"setting-item\", 4, \"ngIf\"], [\"class\", \"settings-section\", 4, \"ngIf\"], [\"name\", \"eye-outline\"], [\"name\", \"shield-checkmark-outline\"], [1, \"btn-danger\", 3, \"click\"], [\"name\", \"pause-outline\"], [\"name\", \"trash-outline\"], [1, \"permission-item\"], [\"name\", \"card-outline\"], [\"name\", \"location-outline\"], [\"name\", \"storefront-outline\"], [\"name\", \"shield-outline\"], [1, \"no-user-state\"], [\"name\", \"settings-outline\", 1, \"large-icon\"]],\n      template: function SettingsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, SettingsComponent_div_1_Template, 4, 0, \"div\", 1)(2, SettingsComponent_div_2_Template, 116, 11, \"div\", 2)(3, SettingsComponent_div_3_Template, 8, 0, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.currentUser);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.NgControlStatus, i4.NgModel, IonicModule, i5.IonIcon, i5.IonSpinner, i5.IonToggle, i5.BooleanValueAccessor],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.settings-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.settings-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.settings-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n\\n.settings-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n  color: #333;\\n}\\n\\n.setting-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.setting-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.setting-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.setting-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n\\n.setting-description[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.setting-control[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n}\\n\\n.btn-danger[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n}\\n\\n.role-badge[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n}\\n\\n.admin-badge[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n}\\n\\n.vendor-badge[_ngcontent-%COMP%] {\\n  background: #28a745;\\n}\\n\\n.customer-badge[_ngcontent-%COMP%] {\\n  background: #17a2b8;\\n}\\n\\n.permission-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 10px 0;\\n}\\n\\n.permission-item[_ngcontent-%COMP%] {\\n  padding: 5px 0;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.permission-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u2713\\\";\\n  color: #28a745;\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "permission_r3", "ɵɵtwoWayListener", "SettingsComponent_div_2_div_57_Template_ion_toggle_ngModelChange_7_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "emailNotifications", "ɵɵresetView", "ɵɵlistener", "SettingsComponent_div_2_div_57_Template_ion_toggle_ionChange_7_listener", "saveNotificationSettings", "ɵɵtwoWayProperty", "SettingsComponent_div_2_div_58_Template_ion_toggle_ngModelChange_7_listener", "_r5", "pushNotifications", "SettingsComponent_div_2_div_58_Template_ion_toggle_ionChange_7_listener", "SettingsComponent_div_2_div_67_Template_button_click_10_listener", "_r6", "managePaymentMethods", "SettingsComponent_div_2_div_67_Template_button_click_20_listener", "manageAddresses", "SettingsComponent_div_2_div_68_Template_button_click_10_listener", "_r7", "navigateToVendorSettings", "SettingsComponent_div_2_div_69_Template_button_click_10_listener", "_r8", "navigateToAdminSettings", "SettingsComponent_div_2_Template_button_click_26_listener", "_r1", "editProfile", "SettingsComponent_div_2_Template_button_click_36_listener", "changePassword", "ɵɵtemplate", "SettingsComponent_div_2_li_45_Template", "SettingsComponent_div_2_Template_ion_toggle_ngModelChange_56_listener", "notificationsEnabled", "SettingsComponent_div_2_Template_ion_toggle_ionChange_56_listener", "SettingsComponent_div_2_div_57_Template", "SettingsComponent_div_2_div_58_Template", "SettingsComponent_div_2_Template_ion_toggle_ngModelChange_66_listener", "marketingEmails", "SettingsComponent_div_2_Template_ion_toggle_ionChange_66_listener", "SettingsComponent_div_2_div_67_Template", "SettingsComponent_div_2_div_68_Template", "SettingsComponent_div_2_div_69_Template", "SettingsComponent_div_2_Template_button_click_80_listener", "viewPrivacySettings", "SettingsComponent_div_2_Template_button_click_90_listener", "viewSecuritySettings", "SettingsComponent_div_2_Template_button_click_103_listener", "deactivateAccount", "SettingsComponent_div_2_Template_button_click_113_listener", "deleteAccount", "ɵɵproperty", "getRoleBadgeClass", "getRoleDisplayName", "getRolePermissions", "canManageAccount", "canAccessVendorSettings", "canAccessAdminSettings", "SettingsComponent_div_3_Template_button_click_6_listener", "_r9", "navigateToLogin", "SettingsComponent", "constructor", "authService", "router", "currentUser", "isLoading", "ngOnInit", "loadUserSettings", "currentUserValue", "loadNotificationSettings", "settings", "localStorage", "getItem", "parsed", "JSON", "parse", "setItem", "stringify", "showSuccessMessage", "isVendor", "isAdmin", "isAuthenticated", "navigate", "confirm", "console", "log", "role", "message", "alert", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingsComponent_Template", "rf", "ctx", "SettingsComponent_div_1_Template", "SettingsComponent_div_2_Template", "SettingsComponent_div_3_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "NgControlStatus", "NgModel", "i5", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "IonToggle", "BooleanValueAccessor", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\profile\\pages\\settings\\settings.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\profile\\pages\\settings\\settings.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-settings',\n  standalone: true,\n  imports: [CommonModule, FormsModule, IonicModule],\n  templateUrl: './settings.component.html',\n  styles: [`\n    .settings-container {\n      padding: 20px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .settings-header {\n      margin-bottom: 30px;\n    }\n\n    .settings-sections {\n      display: grid;\n      gap: 20px;\n    }\n\n    .settings-section {\n      background: white;\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n    }\n\n    .section-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 15px;\n      color: #333;\n    }\n\n    .setting-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 15px 0;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .setting-item:last-child {\n      border-bottom: none;\n    }\n\n    .setting-info {\n      flex: 1;\n    }\n\n    .setting-title {\n      font-weight: 500;\n      margin-bottom: 4px;\n    }\n\n    .setting-description {\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    .setting-control {\n      margin-left: 20px;\n    }\n\n    .btn-primary {\n      background: #667eea;\n      color: white;\n      border: none;\n      padding: 8px 16px;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .btn-danger {\n      background: #dc3545;\n      color: white;\n      border: none;\n      padding: 8px 16px;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .role-badge {\n      background: #667eea;\n      color: white;\n      padding: 4px 12px;\n      border-radius: 20px;\n      font-size: 0.8rem;\n    }\n\n    .admin-badge {\n      background: #dc3545;\n    }\n\n    .vendor-badge {\n      background: #28a745;\n    }\n\n    .customer-badge {\n      background: #17a2b8;\n    }\n\n    .permission-list {\n      list-style: none;\n      padding: 0;\n      margin: 10px 0;\n    }\n\n    .permission-item {\n      padding: 5px 0;\n      font-size: 0.9rem;\n      color: #666;\n    }\n\n    .permission-item::before {\n      content: \"✓\";\n      color: #28a745;\n      margin-right: 8px;\n    }\n  `]\n})\nexport class SettingsComponent implements OnInit {\n  currentUser: User | null = null;\n  isLoading = true;\n\n  // Settings state\n  notificationsEnabled = true;\n  emailNotifications = true;\n  pushNotifications = true;\n  marketingEmails = false;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadUserSettings();\n  }\n\n  loadUserSettings() {\n    this.currentUser = this.authService.currentUserValue;\n    this.loadNotificationSettings();\n    this.isLoading = false;\n  }\n\n  loadNotificationSettings() {\n    // Load from localStorage or API\n    const settings = localStorage.getItem('userSettings');\n    if (settings) {\n      const parsed = JSON.parse(settings);\n      this.notificationsEnabled = parsed.notificationsEnabled ?? true;\n      this.emailNotifications = parsed.emailNotifications ?? true;\n      this.pushNotifications = parsed.pushNotifications ?? true;\n      this.marketingEmails = parsed.marketingEmails ?? false;\n    }\n  }\n\n  saveNotificationSettings() {\n    const settings = {\n      notificationsEnabled: this.notificationsEnabled,\n      emailNotifications: this.emailNotifications,\n      pushNotifications: this.pushNotifications,\n      marketingEmails: this.marketingEmails\n    };\n    localStorage.setItem('userSettings', JSON.stringify(settings));\n    this.showSuccessMessage('Notification settings saved successfully!');\n  }\n\n  // Role-based access methods\n  canAccessVendorSettings(): boolean {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n\n  canAccessAdminSettings(): boolean {\n    return this.authService.isAdmin();\n  }\n\n  canManageAccount(): boolean {\n    return this.authService.isAuthenticated;\n  }\n\n  // Navigation methods\n  editProfile() {\n    this.router.navigate(['/account/edit-profile']);\n  }\n\n  changePassword() {\n    this.router.navigate(['/account/change-password']);\n  }\n\n  managePaymentMethods() {\n    this.router.navigate(['/account/payment-methods']);\n  }\n\n  manageAddresses() {\n    this.router.navigate(['/account/addresses']);\n  }\n\n  viewPrivacySettings() {\n    this.router.navigate(['/account/privacy']);\n  }\n\n  viewSecuritySettings() {\n    this.router.navigate(['/account/security']);\n  }\n\n  navigateToVendorSettings() {\n    if (this.canAccessVendorSettings()) {\n      this.router.navigate(['/vendor/settings']);\n    }\n  }\n\n  navigateToAdminSettings() {\n    if (this.canAccessAdminSettings()) {\n      this.router.navigate(['/admin/settings']);\n    }\n  }\n\n  // Account management\n  deactivateAccount() {\n    if (confirm('Are you sure you want to deactivate your account? This action can be reversed by contacting support.')) {\n      // Implement account deactivation\n      console.log('Account deactivation requested');\n      this.showSuccessMessage('Account deactivation request submitted. You will receive an email confirmation.');\n    }\n  }\n\n  deleteAccount() {\n    if (confirm('Are you sure you want to permanently delete your account? This action cannot be undone.')) {\n      if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {\n        // Implement account deletion\n        console.log('Account deletion requested');\n        this.showSuccessMessage('Account deletion request submitted. You will receive an email with further instructions.');\n      }\n    }\n  }\n\n  getRoleDisplayName(): string {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'Customer';\n      case 'vendor':\n        return 'Vendor';\n      case 'admin':\n        return 'Administrator';\n      default:\n        return 'User';\n    }\n  }\n\n  getRoleBadgeClass(): string {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'customer-badge';\n      case 'vendor':\n        return 'vendor-badge';\n      case 'admin':\n        return 'admin-badge';\n      default:\n        return 'role-badge';\n    }\n  }\n\n  getRolePermissions(): string[] {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return [\n          'Browse and purchase products',\n          'Create and manage wishlist',\n          'View order history',\n          'Leave product reviews',\n          'Follow other users'\n        ];\n      case 'vendor':\n        return [\n          'All customer permissions',\n          'Add and manage products',\n          'View sales analytics',\n          'Manage inventory',\n          'Process orders',\n          'Create promotional content'\n        ];\n      case 'admin':\n        return [\n          'All vendor permissions',\n          'Manage all users',\n          'Access system analytics',\n          'Moderate content',\n          'Configure system settings',\n          'Manage vendor approvals'\n        ];\n      default:\n        return [];\n    }\n  }\n\n  private showSuccessMessage(message: string) {\n    // You can replace this with a proper toast/notification service\n    alert(message);\n  }\n\n  navigateToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n}\n", "<div class=\"settings-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <ion-spinner name=\"crescent\"></ion-spinner>\n    <p>Loading settings...</p>\n  </div>\n\n  <!-- Settings Content -->\n  <div *ngIf=\"!isLoading && currentUser\" class=\"settings-content\">\n    <!-- Settings Header -->\n    <div class=\"settings-header\">\n      <h1>Account Settings</h1>\n      <p>Manage your account preferences and permissions</p>\n    </div>\n\n    <!-- Settings Sections -->\n    <div class=\"settings-sections\">\n      <!-- Account Information -->\n      <div class=\"settings-section\">\n        <h3 class=\"section-title\">Account Information</h3>\n        \n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Account Type</div>\n            <div class=\"setting-description\">Your current role and permissions</div>\n          </div>\n          <div class=\"setting-control\">\n            <span class=\"role-badge\" [ngClass]=\"getRoleBadgeClass()\">\n              {{ getRoleDisplayName() }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Profile Information</div>\n            <div class=\"setting-description\">Update your personal information</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"editProfile()\">\n              <ion-icon name=\"create-outline\"></ion-icon>\n              Edit\n            </button>\n          </div>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Password</div>\n            <div class=\"setting-description\">Change your account password</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"changePassword()\">\n              <ion-icon name=\"key-outline\"></ion-icon>\n              Change\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Role Permissions -->\n      <div class=\"settings-section\">\n        <h3 class=\"section-title\">Your Permissions</h3>\n        <p>Based on your {{ getRoleDisplayName() }} role, you have access to:</p>\n        <ul class=\"permission-list\">\n          <li *ngFor=\"let permission of getRolePermissions()\" class=\"permission-item\">\n            {{ permission }}\n          </li>\n        </ul>\n      </div>\n\n      <!-- Notification Settings -->\n      <div class=\"settings-section\">\n        <h3 class=\"section-title\">Notifications</h3>\n        \n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Enable Notifications</div>\n            <div class=\"setting-description\">Receive notifications about your account activity</div>\n          </div>\n          <div class=\"setting-control\">\n            <ion-toggle [(ngModel)]=\"notificationsEnabled\" (ionChange)=\"saveNotificationSettings()\"></ion-toggle>\n          </div>\n        </div>\n\n        <div class=\"setting-item\" *ngIf=\"notificationsEnabled\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Email Notifications</div>\n            <div class=\"setting-description\">Receive notifications via email</div>\n          </div>\n          <div class=\"setting-control\">\n            <ion-toggle [(ngModel)]=\"emailNotifications\" (ionChange)=\"saveNotificationSettings()\"></ion-toggle>\n          </div>\n        </div>\n\n        <div class=\"setting-item\" *ngIf=\"notificationsEnabled\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Push Notifications</div>\n            <div class=\"setting-description\">Receive push notifications on your device</div>\n          </div>\n          <div class=\"setting-control\">\n            <ion-toggle [(ngModel)]=\"pushNotifications\" (ionChange)=\"saveNotificationSettings()\"></ion-toggle>\n          </div>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Marketing Emails</div>\n            <div class=\"setting-description\">Receive promotional emails and offers</div>\n          </div>\n          <div class=\"setting-control\">\n            <ion-toggle [(ngModel)]=\"marketingEmails\" (ionChange)=\"saveNotificationSettings()\"></ion-toggle>\n          </div>\n        </div>\n      </div>\n\n      <!-- Payment & Shipping (Customer/Vendor) -->\n      <div class=\"settings-section\" *ngIf=\"canManageAccount()\">\n        <h3 class=\"section-title\">Payment & Shipping</h3>\n        \n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Payment Methods</div>\n            <div class=\"setting-description\">Manage your saved payment methods</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"managePaymentMethods()\">\n              <ion-icon name=\"card-outline\"></ion-icon>\n              Manage\n            </button>\n          </div>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Shipping Addresses</div>\n            <div class=\"setting-description\">Manage your delivery addresses</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"manageAddresses()\">\n              <ion-icon name=\"location-outline\"></ion-icon>\n              Manage\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Vendor Settings -->\n      <div class=\"settings-section\" *ngIf=\"canAccessVendorSettings()\">\n        <h3 class=\"section-title\">Vendor Settings</h3>\n        \n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Vendor Dashboard</div>\n            <div class=\"setting-description\">Access vendor-specific settings and preferences</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"navigateToVendorSettings()\">\n              <ion-icon name=\"storefront-outline\"></ion-icon>\n              Open\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Admin Settings -->\n      <div class=\"settings-section\" *ngIf=\"canAccessAdminSettings()\">\n        <h3 class=\"section-title\">Administrator Settings</h3>\n        \n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Admin Panel</div>\n            <div class=\"setting-description\">Access system administration settings</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"navigateToAdminSettings()\">\n              <ion-icon name=\"shield-outline\"></ion-icon>\n              Open\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Privacy & Security -->\n      <div class=\"settings-section\">\n        <h3 class=\"section-title\">Privacy & Security</h3>\n        \n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Privacy Settings</div>\n            <div class=\"setting-description\">Control who can see your information</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"viewPrivacySettings()\">\n              <ion-icon name=\"eye-outline\"></ion-icon>\n              Manage\n            </button>\n          </div>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Security Settings</div>\n            <div class=\"setting-description\">Two-factor authentication and security options</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-primary\" (click)=\"viewSecuritySettings()\">\n              <ion-icon name=\"shield-checkmark-outline\"></ion-icon>\n              Manage\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Account Management -->\n      <div class=\"settings-section\">\n        <h3 class=\"section-title\">Account Management</h3>\n        \n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Deactivate Account</div>\n            <div class=\"setting-description\">Temporarily disable your account</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-danger\" (click)=\"deactivateAccount()\">\n              <ion-icon name=\"pause-outline\"></ion-icon>\n              Deactivate\n            </button>\n          </div>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-info\">\n            <div class=\"setting-title\">Delete Account</div>\n            <div class=\"setting-description\">Permanently delete your account and all data</div>\n          </div>\n          <div class=\"setting-control\">\n            <button class=\"btn-danger\" (click)=\"deleteAccount()\">\n              <ion-icon name=\"trash-outline\"></ion-icon>\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- No User State -->\n  <div *ngIf=\"!isLoading && !currentUser\" class=\"no-user-state\">\n    <ion-icon name=\"settings-outline\" class=\"large-icon\"></ion-icon>\n    <h3>Access Denied</h3>\n    <p>Please log in to access settings.</p>\n    <button class=\"btn-primary\" (click)=\"navigateToLogin()\">\n      Login\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;ICF1CC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,qBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IACxBH,EADwB,CAAAI,YAAA,EAAI,EACtB;;;;;IA4DEJ,EAAA,CAAAC,cAAA,aAA4E;IAC1ED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IADHJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,aAAA,MACF;;;;;;IAoBEP,EAFJ,CAAAC,cAAA,cAAuD,cAC3B,cACG;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpDJ,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAG,MAAA,sCAA+B;IAClEH,EADkE,CAAAI,YAAA,EAAM,EAClE;IAEJJ,EADF,CAAAC,cAAA,cAA6B,qBAC2D;IAA1ED,EAAA,CAAAQ,gBAAA,2BAAAC,4EAAAC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAF,MAAA,CAAAG,kBAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,kBAAA,GAAAN,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAAgC;IAACV,EAAA,CAAAkB,UAAA,uBAAAC,wEAAA;MAAAnB,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAaJ,MAAA,CAAAO,wBAAA,EAA0B;IAAA,EAAC;IAEzFpB,EAF0F,CAAAI,YAAA,EAAa,EAC/F,EACF;;;;IAFUJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAqB,gBAAA,YAAAR,MAAA,CAAAG,kBAAA,CAAgC;;;;;;IAM5ChB,EAFJ,CAAAC,cAAA,cAAuD,cAC3B,cACG;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACnDJ,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAG,MAAA,gDAAyC;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;IAEJJ,EADF,CAAAC,cAAA,cAA6B,qBAC0D;IAAzED,EAAA,CAAAQ,gBAAA,2BAAAc,4EAAAZ,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAF,MAAA,CAAAW,iBAAA,EAAAd,MAAA,MAAAG,MAAA,CAAAW,iBAAA,GAAAd,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAACV,EAAA,CAAAkB,UAAA,uBAAAO,wEAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAaJ,MAAA,CAAAO,wBAAA,EAA0B;IAAA,EAAC;IAExFpB,EAFyF,CAAAI,YAAA,EAAa,EAC9F,EACF;;;;IAFUJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAqB,gBAAA,YAAAR,MAAA,CAAAW,iBAAA,CAA+B;;;;;;IAiB/CxB,EADF,CAAAC,cAAA,aAAyD,aAC7B;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI7CJ,EAFJ,CAAAC,cAAA,cAA0B,cACE,cACG;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAChDJ,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAG,MAAA,wCAAiC;IACpEH,EADoE,CAAAI,YAAA,EAAM,EACpE;IAEJJ,EADF,CAAAC,cAAA,cAA6B,kBACkC;IAAjCD,EAAA,CAAAkB,UAAA,mBAAAQ,iEAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAe,oBAAA,EAAsB;IAAA,EAAC;IAC1D5B,EAAA,CAAAE,SAAA,oBAAyC;IACzCF,EAAA,CAAAG,MAAA,gBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAIFJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACnDJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,sCAA8B;IACjEH,EADiE,CAAAI,YAAA,EAAM,EACjE;IAEJJ,EADF,CAAAC,cAAA,eAA6B,kBAC6B;IAA5BD,EAAA,CAAAkB,UAAA,mBAAAW,iEAAA;MAAA7B,EAAA,CAAAW,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAiB,eAAA,EAAiB;IAAA,EAAC;IACrD9B,EAAA,CAAAE,SAAA,oBAA6C;IAC7CF,EAAA,CAAAG,MAAA,gBACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;;IAIJJ,EADF,CAAAC,cAAA,aAAgE,aACpC;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI1CJ,EAFJ,CAAAC,cAAA,cAA0B,cACE,cACG;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACjDJ,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAG,MAAA,sDAA+C;IAClFH,EADkF,CAAAI,YAAA,EAAM,EAClF;IAEJJ,EADF,CAAAC,cAAA,cAA6B,kBACsC;IAArCD,EAAA,CAAAkB,UAAA,mBAAAa,iEAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAqB,GAAA;MAAA,MAAAnB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAoB,wBAAA,EAA0B;IAAA,EAAC;IAC9DjC,EAAA,CAAAE,SAAA,oBAA+C;IAC/CF,EAAA,CAAAG,MAAA,cACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;;IAIJJ,EADF,CAAAC,cAAA,aAA+D,aACnC;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAIjDJ,EAFJ,CAAAC,cAAA,cAA0B,cACE,cACG;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC5CJ,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAG,MAAA,4CAAqC;IACxEH,EADwE,CAAAI,YAAA,EAAM,EACxE;IAEJJ,EADF,CAAAC,cAAA,cAA6B,kBACqC;IAApCD,EAAA,CAAAkB,UAAA,mBAAAgB,iEAAA;MAAAlC,EAAA,CAAAW,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAuB,uBAAA,EAAyB;IAAA,EAAC;IAC7DpC,EAAA,CAAAE,SAAA,oBAA2C;IAC3CF,EAAA,CAAAG,MAAA,cACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;;IA1KNJ,EAHJ,CAAAC,cAAA,aAAgE,aAEjC,SACvB;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sDAA+C;IACpDH,EADoD,CAAAI,YAAA,EAAI,EAClD;IAMFJ,EAHJ,CAAAC,cAAA,aAA+B,aAEC,aACF;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI9CJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC7CJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,yCAAiC;IACpEH,EADoE,CAAAI,YAAA,EAAM,EACpE;IAEJJ,EADF,CAAAC,cAAA,eAA6B,gBAC8B;IACvDD,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACH,EACF;IAIFJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpDJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,wCAAgC;IACnEH,EADmE,CAAAI,YAAA,EAAM,EACnE;IAEJJ,EADF,CAAAC,cAAA,eAA6B,kBACyB;IAAxBD,EAAA,CAAAkB,UAAA,mBAAAmB,0DAAA;MAAArC,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA0B,WAAA,EAAa;IAAA,EAAC;IACjDvC,EAAA,CAAAE,SAAA,oBAA2C;IAC3CF,EAAA,CAAAG,MAAA,cACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAIFJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACzCJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,oCAA4B;IAC/DH,EAD+D,CAAAI,YAAA,EAAM,EAC/D;IAEJJ,EADF,CAAAC,cAAA,eAA6B,kBAC4B;IAA3BD,EAAA,CAAAkB,UAAA,mBAAAsB,0DAAA;MAAAxC,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA4B,cAAA,EAAgB;IAAA,EAAC;IACpDzC,EAAA,CAAAE,SAAA,oBAAwC;IACxCF,EAAA,CAAAG,MAAA,gBACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;IAIJJ,EADF,CAAAC,cAAA,cAA8B,cACF;IAAAD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/CJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAkE;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzEJ,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAA0C,UAAA,KAAAC,sCAAA,iBAA4E;IAIhF3C,EADE,CAAAI,YAAA,EAAK,EACD;IAIJJ,EADF,CAAAC,cAAA,cAA8B,cACF;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAIxCJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACrDJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,yDAAiD;IACpFH,EADoF,CAAAI,YAAA,EAAM,EACpF;IAEJJ,EADF,CAAAC,cAAA,eAA6B,sBAC6D;IAA5ED,EAAA,CAAAQ,gBAAA,2BAAAoC,sEAAAlC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAF,MAAA,CAAAgC,oBAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAAgC,oBAAA,GAAAnC,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAAkC;IAACV,EAAA,CAAAkB,UAAA,uBAAA4B,kEAAA;MAAA9C,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAaJ,MAAA,CAAAO,wBAAA,EAA0B;IAAA,EAAC;IAE3FpB,EAF4F,CAAAI,YAAA,EAAa,EACjG,EACF;IAYNJ,EAVA,CAAA0C,UAAA,KAAAK,uCAAA,kBAAuD,KAAAC,uCAAA,kBAUA;IAYnDhD,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACjDJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,6CAAqC;IACxEH,EADwE,CAAAI,YAAA,EAAM,EACxE;IAEJJ,EADF,CAAAC,cAAA,eAA6B,sBACwD;IAAvED,EAAA,CAAAQ,gBAAA,2BAAAyC,sEAAAvC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAe,kBAAA,CAAAF,MAAA,CAAAqC,eAAA,EAAAxC,MAAA,MAAAG,MAAA,CAAAqC,eAAA,GAAAxC,MAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAAP,MAAA;IAAA,EAA6B;IAACV,EAAA,CAAAkB,UAAA,uBAAAiC,kEAAA;MAAAnD,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAaJ,MAAA,CAAAO,wBAAA,EAA0B;IAAA,EAAC;IAGxFpB,EAHyF,CAAAI,YAAA,EAAa,EAC5F,EACF,EACF;IAoDNJ,EAjDA,CAAA0C,UAAA,KAAAU,uCAAA,mBAAyD,KAAAC,uCAAA,mBA+BO,KAAAC,uCAAA,mBAkBD;IAmB7DtD,EADF,CAAAC,cAAA,cAA8B,cACF;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI7CJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACjDJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,4CAAoC;IACvEH,EADuE,CAAAI,YAAA,EAAM,EACvE;IAEJJ,EADF,CAAAC,cAAA,eAA6B,kBACiC;IAAhCD,EAAA,CAAAkB,UAAA,mBAAAqC,0DAAA;MAAAvD,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA2C,mBAAA,EAAqB;IAAA,EAAC;IACzDxD,EAAA,CAAAE,SAAA,oBAAwC;IACxCF,EAAA,CAAAG,MAAA,gBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAIFJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClDJ,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAG,MAAA,sDAA8C;IACjFH,EADiF,CAAAI,YAAA,EAAM,EACjF;IAEJJ,EADF,CAAAC,cAAA,eAA6B,kBACkC;IAAjCD,EAAA,CAAAkB,UAAA,mBAAAuC,0DAAA;MAAAzD,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA6C,oBAAA,EAAsB;IAAA,EAAC;IAC1D1D,EAAA,CAAAE,SAAA,oBAAqD;IACrDF,EAAA,CAAAG,MAAA,gBACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;IAIJJ,EADF,CAAAC,cAAA,cAA8B,cACF;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAI7CJ,EAFJ,CAAAC,cAAA,eAA0B,eACE,eACG;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACnDJ,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAG,MAAA,yCAAgC;IACnEH,EADmE,CAAAI,YAAA,EAAM,EACnE;IAEJJ,EADF,CAAAC,cAAA,gBAA6B,mBAC8B;IAA9BD,EAAA,CAAAkB,UAAA,mBAAAyC,2DAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA+C,iBAAA,EAAmB;IAAA,EAAC;IACtD5D,EAAA,CAAAE,SAAA,qBAA0C;IAC1CF,EAAA,CAAAG,MAAA,qBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAIFJ,EAFJ,CAAAC,cAAA,gBAA0B,gBACE,gBACG;IAAAD,EAAA,CAAAG,MAAA,uBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC/CJ,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAG,MAAA,qDAA4C;IAC/EH,EAD+E,CAAAI,YAAA,EAAM,EAC/E;IAEJJ,EADF,CAAAC,cAAA,gBAA6B,mBAC0B;IAA1BD,EAAA,CAAAkB,UAAA,mBAAA2C,2DAAA;MAAA7D,EAAA,CAAAW,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAiD,aAAA,EAAe;IAAA,EAAC;IAClD9D,EAAA,CAAAE,SAAA,qBAA0C;IAC1CF,EAAA,CAAAG,MAAA,iBACF;IAKVH,EALU,CAAAI,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;;;;IA1N6BJ,EAAA,CAAAK,SAAA,IAA+B;IAA/BL,EAAA,CAAA+D,UAAA,YAAAlD,MAAA,CAAAmD,iBAAA,GAA+B;IACtDhE,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAO,MAAA,CAAAoD,kBAAA,QACF;IAkCDjE,EAAA,CAAAK,SAAA,IAAkE;IAAlEL,EAAA,CAAAM,kBAAA,mBAAAO,MAAA,CAAAoD,kBAAA,iCAAkE;IAExCjE,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA+D,UAAA,YAAAlD,MAAA,CAAAqD,kBAAA,GAAuB;IAgBpClE,EAAA,CAAAK,SAAA,IAAkC;IAAlCL,EAAA,CAAAqB,gBAAA,YAAAR,MAAA,CAAAgC,oBAAA,CAAkC;IAIvB7C,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAA+D,UAAA,SAAAlD,MAAA,CAAAgC,oBAAA,CAA0B;IAU1B7C,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAA+D,UAAA,SAAAlD,MAAA,CAAAgC,oBAAA,CAA0B;IAgBrC7C,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAqB,gBAAA,YAAAR,MAAA,CAAAqC,eAAA,CAA6B;IAMhBlD,EAAA,CAAAK,SAAA,EAAwB;IAAxBL,EAAA,CAAA+D,UAAA,SAAAlD,MAAA,CAAAsD,gBAAA,GAAwB;IA+BxBnE,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAA+D,UAAA,SAAAlD,MAAA,CAAAuD,uBAAA,GAA+B;IAkB/BpE,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAA+D,UAAA,SAAAlD,MAAA,CAAAwD,sBAAA,GAA8B;;;;;;IAkFjErE,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,SAAA,mBAAgE;IAChEF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,wCAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACxCJ,EAAA,CAAAC,cAAA,iBAAwD;IAA5BD,EAAA,CAAAkB,UAAA,mBAAAoD,yDAAA;MAAAtE,EAAA,CAAAW,aAAA,CAAA4D,GAAA;MAAA,MAAA1D,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA2D,eAAA,EAAiB;IAAA,EAAC;IACrDxE,EAAA,CAAAG,MAAA,cACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;AD1HR,OAAM,MAAOqE,iBAAiB;EAU5BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAXhB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,SAAS,GAAG,IAAI;IAEhB;IACA,KAAAjC,oBAAoB,GAAG,IAAI;IAC3B,KAAA7B,kBAAkB,GAAG,IAAI;IACzB,KAAAQ,iBAAiB,GAAG,IAAI;IACxB,KAAA0B,eAAe,GAAG,KAAK;EAKpB;EAEH6B,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACH,WAAW,GAAG,IAAI,CAACF,WAAW,CAACM,gBAAgB;IACpD,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,SAAS,GAAG,KAAK;EACxB;EAEAI,wBAAwBA,CAAA;IACtB;IACA,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACrD,IAAIF,QAAQ,EAAE;MACZ,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MACnC,IAAI,CAACtC,oBAAoB,GAAGyC,MAAM,CAACzC,oBAAoB,IAAI,IAAI;MAC/D,IAAI,CAAC7B,kBAAkB,GAAGsE,MAAM,CAACtE,kBAAkB,IAAI,IAAI;MAC3D,IAAI,CAACQ,iBAAiB,GAAG8D,MAAM,CAAC9D,iBAAiB,IAAI,IAAI;MACzD,IAAI,CAAC0B,eAAe,GAAGoC,MAAM,CAACpC,eAAe,IAAI,KAAK;;EAE1D;EAEA9B,wBAAwBA,CAAA;IACtB,MAAM+D,QAAQ,GAAG;MACftC,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/C7B,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CQ,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzC0B,eAAe,EAAE,IAAI,CAACA;KACvB;IACDkC,YAAY,CAACK,OAAO,CAAC,cAAc,EAAEF,IAAI,CAACG,SAAS,CAACP,QAAQ,CAAC,CAAC;IAC9D,IAAI,CAACQ,kBAAkB,CAAC,2CAA2C,CAAC;EACtE;EAEA;EACAvB,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACO,WAAW,CAACiB,QAAQ,EAAE,IAAI,IAAI,CAACjB,WAAW,CAACkB,OAAO,EAAE;EAClE;EAEAxB,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACM,WAAW,CAACkB,OAAO,EAAE;EACnC;EAEA1B,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACQ,WAAW,CAACmB,eAAe;EACzC;EAEA;EACAvD,WAAWA,CAAA;IACT,IAAI,CAACqC,MAAM,CAACmB,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAEAtD,cAAcA,CAAA;IACZ,IAAI,CAACmC,MAAM,CAACmB,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;EACpD;EAEAnE,oBAAoBA,CAAA;IAClB,IAAI,CAACgD,MAAM,CAACmB,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;EACpD;EAEAjE,eAAeA,CAAA;IACb,IAAI,CAAC8C,MAAM,CAACmB,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAvC,mBAAmBA,CAAA;IACjB,IAAI,CAACoB,MAAM,CAACmB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEArC,oBAAoBA,CAAA;IAClB,IAAI,CAACkB,MAAM,CAACmB,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;EAC7C;EAEA9D,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACmC,uBAAuB,EAAE,EAAE;MAClC,IAAI,CAACQ,MAAM,CAACmB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;EAE9C;EAEA3D,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACiC,sBAAsB,EAAE,EAAE;MACjC,IAAI,CAACO,MAAM,CAACmB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;;EAE7C;EAEA;EACAnC,iBAAiBA,CAAA;IACf,IAAIoC,OAAO,CAAC,sGAAsG,CAAC,EAAE;MACnH;MACAC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,IAAI,CAACP,kBAAkB,CAAC,iFAAiF,CAAC;;EAE9G;EAEA7B,aAAaA,CAAA;IACX,IAAIkC,OAAO,CAAC,yFAAyF,CAAC,EAAE;MACtG,IAAIA,OAAO,CAAC,sEAAsE,CAAC,EAAE;QACnF;QACAC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACP,kBAAkB,CAAC,0FAA0F,CAAC;;;EAGzH;EAEA1B,kBAAkBA,CAAA;IAChB,QAAQ,IAAI,CAACY,WAAW,EAAEsB,IAAI;MAC5B,KAAK,UAAU;QACb,OAAO,UAAU;MACnB,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,OAAO;QACV,OAAO,eAAe;MACxB;QACE,OAAO,MAAM;;EAEnB;EAEAnC,iBAAiBA,CAAA;IACf,QAAQ,IAAI,CAACa,WAAW,EAAEsB,IAAI;MAC5B,KAAK,UAAU;QACb,OAAO,gBAAgB;MACzB,KAAK,QAAQ;QACX,OAAO,cAAc;MACvB,KAAK,OAAO;QACV,OAAO,aAAa;MACtB;QACE,OAAO,YAAY;;EAEzB;EAEAjC,kBAAkBA,CAAA;IAChB,QAAQ,IAAI,CAACW,WAAW,EAAEsB,IAAI;MAC5B,KAAK,UAAU;QACb,OAAO,CACL,8BAA8B,EAC9B,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,EACvB,oBAAoB,CACrB;MACH,KAAK,QAAQ;QACX,OAAO,CACL,0BAA0B,EAC1B,yBAAyB,EACzB,sBAAsB,EACtB,kBAAkB,EAClB,gBAAgB,EAChB,4BAA4B,CAC7B;MACH,KAAK,OAAO;QACV,OAAO,CACL,wBAAwB,EACxB,kBAAkB,EAClB,yBAAyB,EACzB,kBAAkB,EAClB,2BAA2B,EAC3B,yBAAyB,CAC1B;MACH;QACE,OAAO,EAAE;;EAEf;EAEQR,kBAAkBA,CAACS,OAAe;IACxC;IACAC,KAAK,CAACD,OAAO,CAAC;EAChB;EAEA5B,eAAeA,CAAA;IACb,IAAI,CAACI,MAAM,CAACmB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;;uBAvLWtB,iBAAiB,EAAAzE,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjBjC,iBAAiB;MAAAkC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7G,EAAA,CAAA8G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrI9BpH,EAAA,CAAAC,cAAA,aAAgC;UAwP9BD,EAtPA,CAAA0C,UAAA,IAAA4E,gCAAA,iBAAiD,IAAAC,gCAAA,oBAMe,IAAAC,gCAAA,iBAgPF;UAQhExH,EAAA,CAAAI,YAAA,EAAM;;;UA9PEJ,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAA+D,UAAA,SAAAsD,GAAA,CAAAvC,SAAA,CAAe;UAMf9E,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAA+D,UAAA,UAAAsD,GAAA,CAAAvC,SAAA,IAAAuC,GAAA,CAAAxC,WAAA,CAA+B;UAgP/B7E,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAA+D,UAAA,UAAAsD,GAAA,CAAAvC,SAAA,KAAAuC,GAAA,CAAAxC,WAAA,CAAgC;;;qBD5O5BhF,YAAY,EAAA4H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAE9H,WAAW,EAAA+H,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAEhI,WAAW,EAAAiI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA,EAAAF,EAAA,CAAAG,SAAA,EAAAH,EAAA,CAAAI,oBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}