{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n  flush(action) {\n    this._active = true;\n    const flushId = this._scheduled;\n    this._scheduled = undefined;\n    const {\n      actions\n    } = this;\n    let error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["AsyncScheduler", "AsapScheduler", "flush", "action", "_active", "flushId", "_scheduled", "undefined", "actions", "error", "shift", "execute", "state", "delay", "id", "unsubscribe"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/rxjs/dist/esm/internal/scheduler/AsapScheduler.js"], "sourcesContent": ["import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n    flush(action) {\n        this._active = true;\n        const flushId = this._scheduled;\n        this._scheduled = undefined;\n        const { actions } = this;\n        let error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,aAAa,SAASD,cAAc,CAAC;EAC9CE,KAAKA,CAACC,MAAM,EAAE;IACV,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAI,CAACA,UAAU,GAAGC,SAAS;IAC3B,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAIC,KAAK;IACTN,MAAM,GAAGA,MAAM,IAAIK,OAAO,CAACE,KAAK,CAAC,CAAC;IAClC,GAAG;MACC,IAAKD,KAAK,GAAGN,MAAM,CAACQ,OAAO,CAACR,MAAM,CAACS,KAAK,EAAET,MAAM,CAACU,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ,CAAC,QAAQ,CAACV,MAAM,GAAGK,OAAO,CAAC,CAAC,CAAC,KAAKL,MAAM,CAACW,EAAE,KAAKT,OAAO,IAAIG,OAAO,CAACE,KAAK,CAAC,CAAC;IAC1E,IAAI,CAACN,OAAO,GAAG,KAAK;IACpB,IAAIK,KAAK,EAAE;MACP,OAAO,CAACN,MAAM,GAAGK,OAAO,CAAC,CAAC,CAAC,KAAKL,MAAM,CAACW,EAAE,KAAKT,OAAO,IAAIG,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE;QACtEP,MAAM,CAACY,WAAW,CAAC,CAAC;MACxB;MACA,MAAMN,KAAK;IACf;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}