{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { AdvancedSearchComponent } from '../../../../shared/components/advanced-search/advanced-search.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/search.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => [1, 2, 3, 4, 5];\nfunction SearchComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Searching...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchComponent_div_7_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r3);\n  }\n}\nfunction SearchComponent_div_7_div_44_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_7_div_44_span_10_Template_span_click_0_listener() {\n      const tag_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.searchFor(tag_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tag_r5, \" \");\n  }\n}\nfunction SearchComponent_div_7_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No products found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Try searching with different keywords or browse our categories.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"h4\");\n    i0.ɵɵtext(8, \"Popular searches:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 34);\n    i0.ɵɵtemplate(10, SearchComponent_div_7_div_44_span_10_Template, 2, 1, \"span\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.popularSearches);\n  }\n}\nfunction SearchComponent_div_7_div_45_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getDiscountPercentage(product_r7), \"% OFF\");\n  }\n}\nfunction SearchComponent_div_7_div_45_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r7.price, \"1.0-0\"), \"\");\n  }\n}\nfunction SearchComponent_div_7_div_45_div_1_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 61);\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const product_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", i_r8 < product_r7.rating.average);\n  }\n}\nfunction SearchComponent_div_7_div_45_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵtemplate(2, SearchComponent_div_7_div_45_div_1_div_17_i_2_Template, 1, 2, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r7.rating.count, \")\");\n  }\n}\nfunction SearchComponent_div_7_div_45_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_7_div_45_div_1_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r7._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 40);\n    i0.ɵɵelement(2, \"img\", 41);\n    i0.ɵɵtemplate(3, SearchComponent_div_7_div_45_div_1_div_3_Template, 3, 1, \"div\", 42);\n    i0.ɵɵelementStart(4, \"div\", 43)(5, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_7_div_45_div_1_Template_button_click_5_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.toggleWishlist(product_r7._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(6, \"i\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 45)(8, \"h3\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 46);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 47)(13, \"span\", 48);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SearchComponent_div_7_div_45_div_1_span_16_Template, 3, 4, \"span\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, SearchComponent_div_7_div_45_div_1_div_17_Template, 5, 3, \"div\", 50);\n    i0.ɵɵelementStart(18, \"div\", 51)(19, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_7_div_45_div_1_Template_button_click_19_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.addToCart(product_r7._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 53);\n    i0.ɵɵtext(21, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_7_div_45_div_1_Template_button_click_22_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.buyNow(product_r7._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(23, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r7.images == null ? null : product_r7.images[0]) || \"/assets/images/placeholder-product.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.discountPrice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r1.isInWishlist(product_r7._id) ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 10, product_r7.discountPrice || product_r7.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r7.discountPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.rating);\n  }\n}\nfunction SearchComponent_div_7_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, SearchComponent_div_7_div_45_div_1_Template, 24, 13, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.searchResults);\n  }\n}\nfunction SearchComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"h2\");\n    i0.ɵɵtext(3, \"Search Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 13)(7, \"div\", 14)(8, \"label\");\n    i0.ɵɵtext(9, \"Category:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"select\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_7_Template_select_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedCategory, $event) || (ctx_r1.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_7_Template_select_change_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilters());\n    });\n    i0.ɵɵelementStart(11, \"option\", 16);\n    i0.ɵɵtext(12, \"All Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SearchComponent_div_7_option_13_Template, 2, 2, \"option\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 14)(15, \"label\");\n    i0.ɵɵtext(16, \"Price Range:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"select\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_7_Template_select_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedPriceRange, $event) || (ctx_r1.selectedPriceRange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_7_Template_select_change_17_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilters());\n    });\n    i0.ɵɵelementStart(18, \"option\", 16);\n    i0.ɵɵtext(19, \"All Prices\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 18);\n    i0.ɵɵtext(21, \"Under \\u20B91,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 19);\n    i0.ɵɵtext(23, \"\\u20B91,000 - \\u20B92,500\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 20);\n    i0.ɵɵtext(25, \"\\u20B92,500 - \\u20B95,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 21);\n    i0.ɵɵtext(27, \"\\u20B95,000 - \\u20B910,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"option\", 22);\n    i0.ɵɵtext(29, \"Above \\u20B910,000\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 14)(31, \"label\");\n    i0.ɵɵtext(32, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"select\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_7_Template_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.sortBy, $event) || (ctx_r1.sortBy = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_7_Template_select_change_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilters());\n    });\n    i0.ɵɵelementStart(34, \"option\", 23);\n    i0.ɵɵtext(35, \"Relevance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"option\", 24);\n    i0.ɵɵtext(37, \"Price: Low to High\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"option\", 25);\n    i0.ɵɵtext(39, \"Price: High to Low\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"option\", 26);\n    i0.ɵɵtext(41, \"Newest First\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"option\", 27);\n    i0.ɵɵtext(43, \"Highest Rated\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(44, SearchComponent_div_7_div_44_Template, 11, 1, \"div\", 28)(45, SearchComponent_div_7_div_45_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.searchResults.length, \" products found\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedPriceRange);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.sortBy);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchResults.length > 0);\n  }\n}\nfunction SearchComponent_div_8_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 70);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_8_div_1_span_4_Template_span_click_0_listener() {\n      const search_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.searchFor(search_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"i\", 72);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_8_div_1_span_4_Template_i_click_3_listener($event) {\n      const search_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.removeRecentSearch(search_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const search_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r10, \" \");\n  }\n}\nfunction SearchComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h3\");\n    i0.ɵɵtext(2, \"Recent Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65);\n    i0.ɵɵtemplate(4, SearchComponent_div_8_div_1_span_4_Template, 4, 1, \"span\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentSearches);\n  }\n}\nfunction SearchComponent_div_8_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_8_span_6_Template_span_click_0_listener() {\n      const search_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.searchFor(search_r12));\n    });\n    i0.ɵɵelement(1, \"i\", 74);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const search_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r12, \" \");\n  }\n}\nfunction SearchComponent_div_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_8_div_11_Template_div_click_0_listener() {\n      const category_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.browseCategory(category_r14.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 76);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(category_r14.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r14.count, \" items\");\n  }\n}\nfunction SearchComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, SearchComponent_div_8_div_1_Template, 5, 1, \"div\", 63);\n    i0.ɵɵelementStart(2, \"div\", 64)(3, \"h3\");\n    i0.ɵɵtext(4, \"Popular Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 65);\n    i0.ɵɵtemplate(6, SearchComponent_div_8_span_6_Template, 3, 1, \"span\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 64)(8, \"h3\");\n    i0.ɵɵtext(9, \"Browse Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 67);\n    i0.ɵɵtemplate(11, SearchComponent_div_8_div_11_Template, 7, 4, \"div\", 68);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.recentSearches.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.popularSearches);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categoryList);\n  }\n}\nexport let SearchComponent = /*#__PURE__*/(() => {\n  class SearchComponent {\n    constructor(route, router, productService, searchService) {\n      this.route = route;\n      this.router = router;\n      this.productService = productService;\n      this.searchService = searchService;\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.recentSearches = [];\n      this.isLoading = false;\n      this.hasSearched = false;\n      this.currentFilters = {};\n      this.searchStartTime = 0;\n      this.destroy$ = new Subject();\n      // Filters\n      this.selectedCategory = '';\n      this.selectedPriceRange = '';\n      this.sortBy = 'relevance';\n      this.categories = ['Men', 'Women', 'Kids', 'Accessories', 'Footwear', 'Electronics'];\n      // Data\n      this.popularSearches = ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Jackets', 'Accessories', 'Formal Wear', 'Casual Wear'];\n      this.categoryList = [{\n        name: 'Men',\n        icon: 'fas fa-male',\n        count: 1250\n      }, {\n        name: 'Women',\n        icon: 'fas fa-female',\n        count: 1890\n      }, {\n        name: 'Kids',\n        icon: 'fas fa-child',\n        count: 650\n      }, {\n        name: 'Accessories',\n        icon: 'fas fa-gem',\n        count: 890\n      }, {\n        name: 'Footwear',\n        icon: 'fas fa-shoe-prints',\n        count: 750\n      }, {\n        name: 'Electronics',\n        icon: 'fas fa-mobile-alt',\n        count: 450\n      }];\n      this.wishlistItems = [];\n      this.searchSubject = new Subject();\n    }\n    ngOnInit() {\n      this.loadRecentSearches();\n      this.loadWishlistItems();\n      // Check for query parameter\n      this.route.queryParams.subscribe(params => {\n        if (params['q']) {\n          this.searchQuery = params['q'];\n          this.performSearch();\n        }\n      });\n      // Setup search with debounce\n      this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), switchMap(query => {\n        if (query.trim().length > 0) {\n          this.isLoading = true;\n          return this.productService.searchProducts(query, {\n            category: this.selectedCategory,\n            minPrice: this.getPriceRange().min,\n            maxPrice: this.getPriceRange().max,\n            sortBy: this.getSortField(),\n            sortOrder: this.getSortOrder()\n          });\n        } else {\n          this.searchResults = [];\n          this.hasSearched = false;\n          this.isLoading = false;\n          return [];\n        }\n      })).subscribe({\n        next: response => {\n          console.log('Search response:', response);\n          this.searchResults = response.products || response.data?.products || [];\n          this.hasSearched = true;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Search error:', error);\n          this.isLoading = false;\n          this.hasSearched = true;\n          this.searchResults = [];\n          // Show user-friendly error message\n          this.showErrorMessage('Search failed. Please try again.');\n        }\n      });\n    }\n    onSearchInput(event) {\n      this.searchQuery = event.target.value;\n      if (this.searchQuery.trim().length > 0) {\n        this.searchSubject.next(this.searchQuery);\n      } else {\n        this.searchResults = [];\n        this.hasSearched = false;\n      }\n    }\n    onSearchSubmit() {\n      if (this.searchQuery.trim()) {\n        this.saveRecentSearch(this.searchQuery.trim());\n        this.performSearch();\n      }\n    }\n    performSearch() {\n      if (this.searchQuery.trim()) {\n        this.searchSubject.next(this.searchQuery);\n      }\n    }\n    clearSearch() {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.hasSearched = false;\n    }\n    searchFor(term) {\n      this.searchQuery = term;\n      this.saveRecentSearch(term);\n      this.performSearch();\n    }\n    applyFilters() {\n      if (this.searchQuery) {\n        this.performSearch();\n      }\n    }\n    browseCategory(category) {\n      this.selectedCategory = category;\n      this.searchQuery = category;\n      this.performSearch();\n    }\n    viewProduct(productId) {\n      this.router.navigate(['/shop/product', productId]);\n    }\n    toggleWishlist(productId) {\n      if (this.isInWishlist(productId)) {\n        this.wishlistItems = this.wishlistItems.filter(id => id !== productId);\n      } else {\n        this.wishlistItems.push(productId);\n      }\n      localStorage.setItem('wishlist', JSON.stringify(this.wishlistItems));\n    }\n    isInWishlist(productId) {\n      return this.wishlistItems.includes(productId);\n    }\n    addToCart(productId) {\n      // Add to cart logic\n      console.log('Added to cart:', productId);\n      this.showNotification('Added to cart!', 'success');\n    }\n    buyNow(productId) {\n      // Buy now logic\n      console.log('Buy now:', productId);\n      this.showNotification('Redirecting to checkout...', 'info');\n    }\n    getDiscountPercentage(product) {\n      if (product.discountPrice) {\n        return Math.round((product.price - product.discountPrice) / product.price * 100);\n      }\n      return 0;\n    }\n    removeRecentSearch(term) {\n      this.recentSearches = this.recentSearches.filter(search => search !== term);\n      localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n    }\n    loadRecentSearches() {\n      const saved = localStorage.getItem('recentSearches');\n      this.recentSearches = saved ? JSON.parse(saved) : [];\n    }\n    loadWishlistItems() {\n      const saved = localStorage.getItem('wishlist');\n      this.wishlistItems = saved ? JSON.parse(saved) : [];\n    }\n    saveRecentSearch(term) {\n      this.recentSearches = this.recentSearches.filter(search => search !== term);\n      this.recentSearches.unshift(term);\n      this.recentSearches = this.recentSearches.slice(0, 10);\n      localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n    }\n    showErrorMessage(message) {\n      // Create error notification\n      const notification = document.createElement('div');\n      notification.className = 'search-error-notification';\n      notification.textContent = message;\n      notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #f44336;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      animation: slideIn 0.3s ease;\n    `;\n      document.body.appendChild(notification);\n      setTimeout(() => {\n        notification.remove();\n      }, 3000);\n    }\n    getPriceRange() {\n      if (!this.selectedPriceRange) return {\n        min: undefined,\n        max: undefined\n      };\n      const ranges = {\n        '0-1000': {\n          min: 0,\n          max: 1000\n        },\n        '1000-2500': {\n          min: 1000,\n          max: 2500\n        },\n        '2500-5000': {\n          min: 2500,\n          max: 5000\n        },\n        '5000-10000': {\n          min: 5000,\n          max: 10000\n        },\n        '10000+': {\n          min: 10000\n        }\n      };\n      return ranges[this.selectedPriceRange] || {\n        min: undefined,\n        max: undefined\n      };\n    }\n    getSortField() {\n      const sortMap = {\n        'relevance': 'createdAt',\n        'price-low': 'price',\n        'price-high': 'price',\n        'newest': 'createdAt',\n        'rating': 'rating'\n      };\n      return sortMap[this.sortBy] || 'createdAt';\n    }\n    getSortOrder() {\n      return this.sortBy === 'price-high' ? 'desc' : 'asc';\n    }\n    showNotification(message, type) {\n      const notification = document.createElement('div');\n      notification.className = `notification notification-${type}`;\n      notification.textContent = message;\n      notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      right: 20px;\n      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};\n      color: white;\n      padding: 1rem 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 0.9rem;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n      document.body.appendChild(notification);\n      setTimeout(() => notification.remove(), 3000);\n    }\n    // Advanced search event handlers\n    onSearchPerformed(event) {\n      this.searchQuery = event.query;\n      this.currentFilters = event.filters;\n      this.searchStartTime = Date.now();\n      this.hasSearched = true;\n      // Update URL with search parameters\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          q: event.query,\n          ...event.filters\n        },\n        queryParamsHandling: 'merge'\n      });\n      // Save to recent searches\n      this.saveRecentSearch(event.query);\n    }\n    onSuggestionSelected(suggestion) {\n      // Track suggestion selection\n      console.log('Suggestion selected:', suggestion);\n      // Track the interaction\n      if (this.searchQuery) {\n        this.searchService.trackSearchInteraction(this.searchQuery, suggestion.metadata?.productId || '', 'suggestion_click').subscribe();\n      }\n    }\n    onFiltersChanged(filters) {\n      this.currentFilters = filters;\n      // Update URL with new filters\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          ...filters\n        },\n        queryParamsHandling: 'merge'\n      });\n      // Track filter change\n      if (this.searchQuery) {\n        this.searchService.trackFilterChange(this.searchQuery).subscribe();\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    // Track product clicks from search results\n    onProductClick(product, position) {\n      if (this.searchQuery) {\n        this.searchService.trackProductClick(this.searchQuery, product._id, position).subscribe();\n      }\n    }\n    static {\n      this.ɵfac = function SearchComponent_Factory(t) {\n        return new (t || SearchComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.SearchService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SearchComponent,\n        selectors: [[\"app-search\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 9,\n        vars: 7,\n        consts: [[1, \"search-page\"], [1, \"search-header\"], [1, \"container\"], [3, \"searchPerformed\", \"suggestionSelected\", \"filtersChanged\", \"placeholder\", \"showFilters\", \"enableVoiceSearch\", \"autoFocus\"], [1, \"search-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"default-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"search-results\"], [1, \"results-header\"], [1, \"results-count\"], [1, \"filters-section\"], [1, \"filter-group\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"0-1000\"], [\"value\", \"1000-2500\"], [\"value\", \"2500-5000\"], [\"value\", \"5000-10000\"], [\"value\", \"10000+\"], [\"value\", \"relevance\"], [\"value\", \"price-low\"], [\"value\", \"price-high\"], [\"value\", \"newest\"], [\"value\", \"rating\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"results-grid\", 4, \"ngIf\"], [3, \"value\"], [1, \"no-results\"], [1, \"fas\", \"fa-search\"], [1, \"suggested-searches\"], [1, \"search-tags\"], [\"class\", \"search-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-tag\", 3, \"click\"], [1, \"results-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [\"class\", \"product-badge\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price-container\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"rating\", 4, \"ngIf\"], [1, \"product-buttons\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy\", 3, \"click\"], [1, \"product-badge\"], [1, \"original-price\"], [1, \"rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"fas\", \"fa-star\"], [1, \"default-content\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"section\"], [1, \"search-chips\"], [\"class\", \"search-chip popular\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"search-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-chip\", 3, \"click\"], [1, \"fas\", \"fa-clock\"], [1, \"fas\", \"fa-times\", 3, \"click\"], [1, \"search-chip\", \"popular\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [1, \"category-card\", 3, \"click\"], [1, \"category-icon\"]],\n        template: function SearchComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"app-advanced-search\", 3);\n            i0.ɵɵlistener(\"searchPerformed\", function SearchComponent_Template_app_advanced_search_searchPerformed_3_listener($event) {\n              return ctx.onSearchPerformed($event);\n            })(\"suggestionSelected\", function SearchComponent_Template_app_advanced_search_suggestionSelected_3_listener($event) {\n              return ctx.onSuggestionSelected($event);\n            })(\"filtersChanged\", function SearchComponent_Template_app_advanced_search_filtersChanged_3_listener($event) {\n              return ctx.onFiltersChanged($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 2);\n            i0.ɵɵtemplate(6, SearchComponent_div_6_Template, 4, 0, \"div\", 5)(7, SearchComponent_div_7_Template, 46, 7, \"div\", 6)(8, SearchComponent_div_8_Template, 12, 3, \"div\", 7);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"placeholder\", \"Search for fashion, brands, and more...\")(\"showFilters\", true)(\"enableVoiceSearch\", true)(\"autoFocus\", true);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.hasSearched && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.hasSearched && !ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, FormsModule, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, IonicModule, AdvancedSearchComponent],\n        styles: [\".search-page[_ngcontent-%COMP%]{min-height:calc(100vh - 60px);background:#f8f9fa}.search-header[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #e9ecef;padding:2rem 0;position:sticky;top:60px;z-index:100}.search-bar-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.search-input-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center}.search-icon[_ngcontent-%COMP%]{position:absolute;left:1rem;color:#6c757d;z-index:1}.search-input[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;border:2px solid #e9ecef;border-radius:50px;font-size:1rem;outline:none;transition:all .3s ease}.search-input[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.clear-btn[_ngcontent-%COMP%]{position:absolute;right:1rem;background:none;border:none;color:#6c757d;cursor:pointer;padding:.5rem}.search-content[_ngcontent-%COMP%]{padding:2rem 0}.container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 1rem}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:4rem 0}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.results-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem}.results-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:1.5rem;font-weight:600}.results-count[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem}.filters-section[_ngcontent-%COMP%]{display:flex;gap:2rem;margin-bottom:2rem;padding:1rem;background:#fff;border-radius:8px;box-shadow:0 2px 4px #0000001a}.filter-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;font-size:.9rem;color:#495057}.filter-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:.5rem;border:1px solid #ced4da;border-radius:4px;font-size:.9rem}.no-results[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem;background:#fff;border-radius:8px}.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#dee2e6;margin-bottom:1rem}.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.5rem;color:#495057}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:2rem}.suggested-searches[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 1rem;font-size:1rem;color:#495057}.search-tags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;justify-content:center}.search-tag[_ngcontent-%COMP%]{background:#e9ecef;color:#495057;padding:.5rem 1rem;border-radius:20px;cursor:pointer;font-size:.9rem;transition:all .2s ease}.search-tag[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.results-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1.5rem}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000001a;transition:all .3s ease;cursor:pointer}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.product-image[_ngcontent-%COMP%]{position:relative;width:100%;height:200px;overflow:hidden}.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.05)}.product-badge[_ngcontent-%COMP%]{position:absolute;top:.5rem;left:.5rem;background:#dc3545;color:#fff;padding:.25rem .5rem;border-radius:4px;font-size:.75rem;font-weight:600}.product-actions[_ngcontent-%COMP%]{position:absolute;top:.5rem;right:.5rem}.action-btn[_ngcontent-%COMP%]{background:#ffffffe6;border:none;width:36px;height:36px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease;color:#6c757d}.action-btn[_ngcontent-%COMP%]:hover{background:#fff;color:#dc3545}.product-info[_ngcontent-%COMP%]{padding:1rem}.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1rem;font-weight:600;color:#212529;line-height:1.3}.brand[_ngcontent-%COMP%]{color:#6c757d;font-size:.85rem;margin:0 0 .5rem;text-transform:uppercase;letter-spacing:.5px}.price-container[_ngcontent-%COMP%]{margin-bottom:.5rem}.current-price[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:700;color:#28a745;margin-right:.5rem}.original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#6c757d;text-decoration:line-through}.rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:1rem}.stars[_ngcontent-%COMP%]{display:flex;gap:.125rem}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;color:#dee2e6}.stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%]{color:#ffc107}.rating-count[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d}.product-buttons[_ngcontent-%COMP%]{display:flex;gap:.5rem}.btn-cart[_ngcontent-%COMP%], .btn-buy[_ngcontent-%COMP%]{flex:1;padding:.5rem;border:none;border-radius:6px;font-size:.85rem;font-weight:600;cursor:pointer;transition:all .2s ease}.btn-cart[_ngcontent-%COMP%]{background:#f8f9fa;color:#495057;border:1px solid #dee2e6}.btn-cart[_ngcontent-%COMP%]:hover{background:#e9ecef}.btn-buy[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-buy[_ngcontent-%COMP%]:hover{background:#0056b3}.default-content[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.section[_ngcontent-%COMP%]{margin-bottom:3rem}.section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 1rem;font-size:1.25rem;font-weight:600;color:#212529}.search-chips[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.search-chip[_ngcontent-%COMP%]{background:#fff;border:1px solid #dee2e6;color:#495057;padding:.5rem 1rem;border-radius:20px;cursor:pointer;font-size:.9rem;transition:all .2s ease;display:flex;align-items:center;gap:.5rem}.search-chip[_ngcontent-%COMP%]:hover{border-color:#007bff;color:#007bff}.search-chip.popular[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff6b6b,#feca57);color:#fff;border:none}.categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem}.category-card[_ngcontent-%COMP%]{background:#fff;padding:2rem 1rem;border-radius:12px;text-align:center;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #0000001a}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.category-icon[_ngcontent-%COMP%]{width:60px;height:60px;background:linear-gradient(45deg,#007bff,#0056b3);border-radius:50%;display:flex;align-items:center;justify-content:center;margin:0 auto 1rem}.category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;color:#fff}.category-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.125rem;font-weight:600;color:#212529}.category-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#6c757d;font-size:.9rem}@media (max-width: 768px){.filters-section[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.results-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}.categories-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(150px,1fr))}.search-header[_ngcontent-%COMP%]{padding:1rem 0}}\"]\n      });\n    }\n  }\n  return SearchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}