{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction NewArrivalsComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"div\", 15);\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵelement(3, \"div\", 17)(4, \"div\", 18)(5, \"div\", 19);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_11_div_2_Template, 6, 0, \"div\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction NewArrivalsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"ion-icon\", 21);\n    i0.ɵɵelementStart(2, \"p\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_12_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 24);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction NewArrivalsComponent_div_13_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r5), \"% OFF \");\n  }\n}\nfunction NewArrivalsComponent_div_13_div_7_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.originalPrice));\n  }\n}\nfunction NewArrivalsComponent_div_13_div_7_ion_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 42);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r5.rating.average);\n    i0.ɵɵproperty(\"name\", star_r6 <= product_r5.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_7_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵelement(2, \"img\", 35);\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵelement(4, \"ion-icon\", 37);\n    i0.ɵɵtext(5, \" New \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_13_div_7_div_8_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementStart(9, \"div\", 40)(10, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_7_Template_button_click_10_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r5, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_7_Template_button_click_12_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r5, $event));\n    });\n    i0.ɵɵelement(13, \"ion-icon\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 45)(15, \"div\", 46);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h3\", 47);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 48)(20, \"span\", 49);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NewArrivalsComponent_div_13_div_7_span_22_Template, 2, 1, \"span\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 51)(24, \"div\", 52);\n    i0.ɵɵtemplate(25, NewArrivalsComponent_div_13_div_7_ion_icon_25_Template, 1, 3, \"ion-icon\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 54);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 55)(29, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_7_Template_button_click_29_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r5, $event));\n    });\n    i0.ɵɵelement(30, \"ion-icon\", 57);\n    i0.ɵɵtext(31, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_7_Template_button_click_32_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r5, $event));\n    });\n    i0.ɵɵelement(33, \"ion-icon\", 59);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.cardWidth, \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r5.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r5.images[0].alt || product_r5.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDaysAgo(product_r5.createdAt), \" days ago \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r5) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r5._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r5._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.originalPrice && product_r5.originalPrice > product_r5.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(17, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n  }\n}\nfunction NewArrivalsComponent_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"ion-icon\", 63);\n    i0.ɵɵelementStart(2, \"h3\", 64);\n    i0.ɵɵtext(3, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 65);\n    i0.ɵɵtext(5, \"Check back soon for fresh new styles\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"mouseenter\", function NewArrivalsComponent_div_13_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function NewArrivalsComponent_div_13_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(1, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevSlide());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextSlide());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"div\", 30);\n    i0.ɵɵtemplate(7, NewArrivalsComponent_div_13_div_7_Template, 34, 18, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_13_div_8_Template, 6, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoPrev);\n    i0.ɵɵattribute(\"aria-label\", \"Previous products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoNext);\n    i0.ɵɵattribute(\"aria-label\", \"Next products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(-\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && !ctx_r1.error && ctx_r1.newArrivals.length === 0);\n  }\n}\nexport class NewArrivalsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.newArrivals = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 280;\n    this.visibleCards = 4;\n    this.maxSlide = 0;\n    this.autoSlideDelay = 3500; // 3.5 seconds for new arrivals\n  }\n  ngOnInit() {\n    this.loadNewArrivals();\n    this.subscribeNewArrivals();\n    this.subscribeLikedProducts();\n    this.initializeSlider();\n    this.startAutoSlide();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  subscribeNewArrivals() {\n    this.subscription.add(this.trendingService.newArrivals$.subscribe(products => {\n      this.newArrivals = products;\n      this.isLoading = false;\n      this.calculateMaxSlide();\n      this.currentSlide = 0;\n      this.updateSlidePosition();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadNewArrivals() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadNewArrivals(1, 6);\n      } catch (error) {\n        console.error('Error loading new arrivals:', error);\n        _this.error = 'Failed to load new arrivals';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  getDaysAgo(createdAt) {\n    const now = new Date();\n    const created = new Date(createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  }\n  onRetry() {\n    this.loadNewArrivals();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'new-arrivals'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Slider methods\n  initializeSlider() {\n    this.updateResponsiveSettings();\n    this.calculateMaxSlide();\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\n  }\n  updateResponsiveSettings() {\n    const containerWidth = window.innerWidth;\n    if (containerWidth >= 1200) {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    } else if (containerWidth >= 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 260;\n    } else if (containerWidth >= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 240;\n    } else {\n      this.visibleCards = 1;\n      this.cardWidth = 220;\n    }\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n  calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\n  }\n  updateSlidePosition() {\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\n  }\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n  prevSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n  startAutoSlide() {\n    this.autoSlideInterval = setInterval(() => {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlidePosition();\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.startAutoSlide();\n  }\n  get canGoPrev() {\n    return this.currentSlide > 0;\n  }\n  get canGoNext() {\n    return this.currentSlide < this.maxSlide;\n  }\n  static {\n    this.ɵfac = function NewArrivalsComponent_Factory(t) {\n      return new (t || NewArrivalsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewArrivalsComponent,\n      selectors: [[\"app-new-arrivals\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 3,\n      consts: [[1, \"new-arrivals-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"sparkles\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\"], [1, \"nav-btn\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"nav-btn\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"products-slider-wrapper\"], [1, \"products-slider\"], [\"class\", \"product-card\", 3, \"width\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [\"name\", \"sparkles\"], [1, \"days-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"sparkles-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function NewArrivalsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" New Arrivals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Fresh styles just landed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function NewArrivalsComponent_Template_button_click_8_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(9, \" View All \");\n          i0.ɵɵelement(10, \"ion-icon\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, NewArrivalsComponent_div_11_Template, 3, 2, \"div\", 8)(12, NewArrivalsComponent_div_12_Template, 7, 1, \"div\", 9)(13, NewArrivalsComponent_div_13_Template, 9, 9, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.newArrivals.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule],\n      styles: [\".new-arrivals-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.products-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 10px;\\n}\\n\\n.products-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .days-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50px;\\n  left: 12px;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.15);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 768px) {\\n  .new-arrivals-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 250px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .products-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 5px;\\n  }\\n  .products-slider[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 220px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "NewArrivalsComponent_div_11_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "NewArrivalsComponent_div_12_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r5", "formatPrice", "originalPrice", "ɵɵclassProp", "star_r6", "rating", "average", "NewArrivalsComponent_div_13_div_7_Template_div_click_0_listener", "_r4", "$implicit", "onProductClick", "NewArrivalsComponent_div_13_div_7_div_8_Template", "NewArrivalsComponent_div_13_div_7_Template_button_click_10_listener", "$event", "onLikeProduct", "NewArrivalsComponent_div_13_div_7_Template_button_click_12_listener", "onShareProduct", "NewArrivalsComponent_div_13_div_7_span_22_Template", "NewArrivalsComponent_div_13_div_7_ion_icon_25_Template", "NewArrivalsComponent_div_13_div_7_Template_button_click_29_listener", "onAddToCart", "NewArrivalsComponent_div_13_div_7_Template_button_click_32_listener", "onAddToWishlist", "ɵɵstyleProp", "<PERSON><PERSON><PERSON><PERSON>", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "getDaysAgo", "createdAt", "isProductLiked", "_id", "brand", "price", "_c1", "count", "NewArrivalsComponent_div_13_Template_div_mouseenter_0_listener", "_r3", "pauseAutoSlide", "NewArrivalsComponent_div_13_Template_div_mouseleave_0_listener", "resumeAutoSlide", "NewArrivalsComponent_div_13_Template_button_click_1_listener", "prevSlide", "NewArrivalsComponent_div_13_Template_button_click_3_listener", "nextSlide", "NewArrivalsComponent_div_13_div_7_Template", "NewArrivalsComponent_div_13_div_8_Template", "canGoPrev", "canGoNext", "slideOffset", "newArrivals", "trackByProductId", "isLoading", "length", "NewArrivalsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "likedProducts", "Set", "subscription", "currentSlide", "visibleCards", "maxSlide", "autoSlideDelay", "ngOnInit", "loadNewArrivals", "subscribeNewArrivals", "subscribeLikedProducts", "initializeSlider", "startAutoSlide", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "add", "newArrivals$", "subscribe", "products", "calculateMaxSlide", "updateSlidePosition", "likedProducts$", "_this", "_asyncToGenerator", "console", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "now", "Date", "created", "diffTime", "abs", "getTime", "diffDays", "ceil", "onViewAll", "queryParams", "filter", "productId", "has", "index", "updateResponsiveSettings", "addEventListener", "containerWidth", "innerWidth", "max", "autoSlideInterval", "setInterval", "clearInterval", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NewArrivalsComponent_Template", "rf", "ctx", "NewArrivalsComponent_Template_button_click_8_listener", "NewArrivalsComponent_div_11_Template", "NewArrivalsComponent_div_12_Template", "NewArrivalsComponent_div_13_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-new-arrivals',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './new-arrivals.component.html',\n  styleUrls: ['./new-arrivals.component.scss']\n})\nexport class NewArrivalsComponent implements OnInit, OnDestroy {\n  newArrivals: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 280;\n  visibleCards = 4;\n  maxSlide = 0;\n  autoSlideInterval: any;\n  autoSlideDelay = 3500; // 3.5 seconds for new arrivals\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadNewArrivals();\n    this.subscribeNewArrivals();\n    this.subscribeLikedProducts();\n    this.initializeSlider();\n    this.startAutoSlide();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private subscribeNewArrivals() {\n    this.subscription.add(\n      this.trendingService.newArrivals$.subscribe(products => {\n        this.newArrivals = products;\n        this.isLoading = false;\n        this.calculateMaxSlide();\n        this.currentSlide = 0;\n        this.updateSlidePosition();\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadNewArrivals() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadNewArrivals(1, 6);\n    } catch (error) {\n      console.error('Error loading new arrivals:', error);\n      this.error = 'Failed to load new arrivals';\n      this.isLoading = false;\n    }\n  }\n\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  async onAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.cartService.addToCart(product._id, 1);\n      console.log('Product added to cart!');\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    }\n  }\n\n  async onAddToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.wishlistService.addToWishlist(product._id);\n      console.log('Product added to wishlist!');\n    } catch (error) {\n      console.error('Error adding to wishlist:', error);\n    }\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  getDaysAgo(createdAt: Date): number {\n    const now = new Date();\n    const created = new Date(createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  }\n\n  onRetry() {\n    this.loadNewArrivals();\n  }\n\n  onViewAll() {\n    this.router.navigate(['/products'], { \n      queryParams: { filter: 'new-arrivals' } \n    });\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  // Slider methods\n  private initializeSlider() {\n    this.updateResponsiveSettings();\n    this.calculateMaxSlide();\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\n  }\n\n  private updateResponsiveSettings() {\n    const containerWidth = window.innerWidth;\n\n    if (containerWidth >= 1200) {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    } else if (containerWidth >= 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 260;\n    } else if (containerWidth >= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 240;\n    } else {\n      this.visibleCards = 1;\n      this.cardWidth = 220;\n    }\n\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n\n  private calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\n  }\n\n  private updateSlidePosition() {\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\n  }\n\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n\n  prevSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n\n  private startAutoSlide() {\n    this.autoSlideInterval = setInterval(() => {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlidePosition();\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.startAutoSlide();\n  }\n\n  get canGoPrev(): boolean {\n    return this.currentSlide > 0;\n  }\n\n  get canGoNext(): boolean {\n    return this.currentSlide < this.maxSlide;\n  }\n}\n", "<div class=\"new-arrivals-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"sparkles\" class=\"title-icon\"></ion-icon>\n        New Arrivals\n      </h2>\n      <p class=\"section-subtitle\">Fresh styles just landed</p>\n    </div>\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\n      View All\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"loading-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Products Slider -->\n  <div *ngIf=\"!isLoading && !error && newArrivals.length > 0\" class=\"products-slider-container\"\n       (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n\n    <!-- Navigation Buttons -->\n    <button class=\"nav-btn prev-btn\"\n            [disabled]=\"!canGoPrev\"\n            (click)=\"prevSlide()\"\n            [attr.aria-label]=\"'Previous products'\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n\n    <button class=\"nav-btn next-btn\"\n            [disabled]=\"!canGoNext\"\n            (click)=\"nextSlide()\"\n            [attr.aria-label]=\"'Next products'\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n\n    <div class=\"products-slider-wrapper\">\n      <div class=\"products-slider\"\n           [style.transform]=\"'translateX(-' + slideOffset + 'px)'\">\n        <div\n          *ngFor=\"let product of newArrivals; trackBy: trackByProductId\"\n          class=\"product-card\"\n          [style.width.px]=\"cardWidth\"\n          (click)=\"onProductClick(product)\"\n        >\n      <!-- Product Image -->\n      <div class=\"product-image-container\">\n        <img \n          [src]=\"product.images[0].url\"\n          [alt]=\"product.images[0].alt || product.name\"\n          class=\"product-image\"\n          loading=\"lazy\"\n        />\n        \n        <!-- New Badge -->\n        <div class=\"new-badge\">\n          <ion-icon name=\"sparkles\"></ion-icon>\n          New\n        </div>\n\n        <!-- Days Badge -->\n        <div class=\"days-badge\">\n          {{ getDaysAgo(product.createdAt) }} days ago\n        </div>\n\n        <!-- Discount Badge -->\n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n          {{ getDiscountPercentage(product) }}% OFF\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isProductLiked(product._id)\"\n            (click)=\"onLikeProduct(product, $event)\"\n            [attr.aria-label]=\"'Like ' + product.name\"\n          >\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n          </button>\n          <button \n            class=\"action-btn share-btn\" \n            (click)=\"onShareProduct(product, $event)\"\n            [attr.aria-label]=\"'Share ' + product.name\"\n          >\n            <ion-icon name=\"share-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n\n      <!-- Product Info -->\n      <div class=\"product-info\">\n        <div class=\"product-brand\">{{ product.brand }}</div>\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n        \n        <!-- Price Section -->\n        <div class=\"price-section\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n        </div>\n\n        <!-- Rating -->\n        <div class=\"rating-section\">\n          <div class=\"stars\">\n            <ion-icon \n              *ngFor=\"let star of [1,2,3,4,5]\" \n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n              [class.filled]=\"star <= product.rating.average\"\n            ></ion-icon>\n          </div>\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"product-actions\">\n          <button \n            class=\"cart-btn\" \n            (click)=\"onAddToCart(product, $event)\"\n          >\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\n            Add to Cart\n          </button>\n          <button \n            class=\"wishlist-btn\" \n            (click)=\"onAddToWishlist(product, $event)\"\n          >\n            <ion-icon name=\"heart-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && newArrivals.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"sparkles-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No New Arrivals</h3>\n    <p class=\"empty-message\">Check back soon for fresh new styles</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;;ICS7CC,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,0CAAA,kBAA6D;IASjEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAToBH,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAgB;;;;;;IAY1CT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAW,UAAA,mBAAAC,6DAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,SAAA,mBAAoC;IACpCF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAwDhCpB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAN,MAAA,CAAAO,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BEvB,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAU,MAAA,GAAwC;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnEzB,EAAA,CAAAE,SAAA,mBAIY;;;;;IADVF,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IApEvE7B,EAAA,CAAAC,cAAA,cAKC;IADCD,EAAA,CAAAW,UAAA,mBAAAmB,gEAAA;MAAA,MAAAP,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkB,cAAA,CAAAV,UAAA,CAAuB;IAAA,EAAC;IAGrCvB,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAKE;IAGFF,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,SAAA,mBAAqC;IACrCF,EAAA,CAAAU,MAAA,YACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,IAAA8B,gDAAA,kBAAuE;IAMrElC,EADF,CAAAC,cAAA,cAA4B,kBAMzB;IAFCD,EAAA,CAAAW,UAAA,mBAAAwB,oEAAAC,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsB,aAAA,CAAAd,UAAA,EAAAa,MAAA,CAA8B;IAAA,EAAC;IAGxCpC,EAAA,CAAAE,SAAA,oBAAsF;IACxFF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAW,UAAA,mBAAA2B,oEAAAF,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAwB,cAAA,CAAAhB,UAAA,EAAAa,MAAA,CAA+B;IAAA,EAAC;IAGzCpC,EAAA,CAAAE,SAAA,oBAA0C;IAGhDF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAI9CH,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAoC,kDAAA,mBAC6B;IAC/BxC,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAAI,UAAA,KAAAqC,sDAAA,uBAIC;IACHzC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAA4B;IACxDV,EADwD,CAAAG,YAAA,EAAO,EACzD;IAIJH,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAW,UAAA,mBAAA+B,oEAAAN,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA4B,WAAA,CAAApB,UAAA,EAAAa,MAAA,CAA4B;IAAA,EAAC;IAEtCpC,EAAA,CAAAE,SAAA,oBAA4C;IAC5CF,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAW,UAAA,mBAAAiC,oEAAAR,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA8B,eAAA,CAAAtB,UAAA,EAAAa,MAAA,CAAgC;IAAA,EAAC;IAE1CpC,EAAA,CAAAE,SAAA,oBAA0C;IAIlDF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;IAzFAH,EAAA,CAAA8C,WAAA,UAAA/B,MAAA,CAAAgC,SAAA,OAA4B;IAM5B/C,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAgB,UAAA,CAAAyB,MAAA,IAAAC,GAAA,EAAAjD,EAAA,CAAAkD,aAAA,CAA6B,QAAA3B,UAAA,CAAAyB,MAAA,IAAAG,GAAA,IAAA5B,UAAA,CAAA6B,IAAA,CACgB;IAa7CpD,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAN,MAAA,CAAAsC,UAAA,CAAA9B,UAAA,CAAA+B,SAAA,gBACF;IAGMtD,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAO,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CvB,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAX,MAAA,CAAAwC,cAAA,CAAAhC,UAAA,CAAAiC,GAAA,EAA2C;;IAIjCxD,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAwC,cAAA,CAAAhC,UAAA,CAAAiC,GAAA,8BAAgE;IAK1ExD,EAAA,CAAAM,SAAA,EAA2C;;IASpBN,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAAkC,KAAA,CAAmB;IACrBzD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA6B,IAAA,CAAkB;IAIbpD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAAmC,KAAA,EAAgC;IACrD1D,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAAmC,KAAA,CAAoE;IAQtD1D,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAAmD,GAAA,EAAc;IAKT3D,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAqB,kBAAA,MAAAE,UAAA,CAAAK,MAAA,CAAAgC,KAAA,MAA4B;;;;;IAwB9D5D,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,mBAAgE;IAChEF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,sBAAe;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,2CAAoC;IAC/DV,EAD+D,CAAAG,YAAA,EAAI,EAC7D;;;;;;IAzHNH,EAAA,CAAAC,cAAA,cACsE;IAAjCD,EAAhC,CAAAW,UAAA,wBAAAkD,+DAAA;MAAA7D,EAAA,CAAAa,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAcF,MAAA,CAAAgD,cAAA,EAAgB;IAAA,EAAC,wBAAAC,+DAAA;MAAAhE,EAAA,CAAAa,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeF,MAAA,CAAAkD,eAAA,EAAiB;IAAA,EAAC;IAGnEjE,EAAA,CAAAC,cAAA,iBAGgD;IADxCD,EAAA,CAAAW,UAAA,mBAAAuD,6DAAA;MAAAlE,EAAA,CAAAa,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoD,SAAA,EAAW;IAAA,EAAC;IAE3BnE,EAAA,CAAAE,SAAA,mBAAyC;IAC3CF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,iBAG4C;IADpCD,EAAA,CAAAW,UAAA,mBAAAyD,6DAAA;MAAApE,EAAA,CAAAa,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsD,SAAA,EAAW;IAAA,EAAC;IAE3BrE,EAAA,CAAAE,SAAA,kBAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,cAAqC,cAE2B;IAC5DD,EAAA,CAAAI,UAAA,IAAAkE,0CAAA,oBAKC;IAwFPtE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,IAAAmE,0CAAA,kBAAsF;IAKxFvE,EAAA,CAAAG,YAAA,EAAM,EAzHkE;;;;IAI5DH,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,cAAAQ,MAAA,CAAAyD,SAAA,CAAuB;;IAOvBxE,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAO,UAAA,cAAAQ,MAAA,CAAA0D,SAAA,CAAuB;;IAQxBzE,EAAA,CAAAM,SAAA,GAAwD;IAAxDN,EAAA,CAAA8C,WAAA,+BAAA/B,MAAA,CAAA2D,WAAA,SAAwD;IAErC1E,EAAA,CAAAM,SAAA,EAAgB;IAAAN,EAAhB,CAAAO,UAAA,YAAAQ,MAAA,CAAA4D,WAAA,CAAgB,iBAAA5D,MAAA,CAAA6D,gBAAA,CAAyB;IA+F/D5E,EAAA,CAAAM,SAAA,EAAsD;IAAtDN,EAAA,CAAAO,UAAA,UAAAQ,MAAA,CAAA8D,SAAA,KAAA9D,MAAA,CAAAK,KAAA,IAAAL,MAAA,CAAA4D,WAAA,CAAAG,MAAA,OAAsD;;;AD3I9D,OAAM,MAAOC,oBAAoB;EAgB/BC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IApBhB,KAAAV,WAAW,GAAc,EAAE;IAC3B,KAAAE,SAAS,GAAG,IAAI;IAChB,KAAAzD,KAAK,GAAkB,IAAI;IAC3B,KAAAkE,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI3F,YAAY,EAAE;IAEvD;IACA,KAAA4F,YAAY,GAAG,CAAC;IAChB,KAAAf,WAAW,GAAG,CAAC;IACf,KAAA3B,SAAS,GAAG,GAAG;IACf,KAAA2C,YAAY,GAAG,CAAC;IAChB,KAAAC,QAAQ,GAAG,CAAC;IAEZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;EAQpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,YAAY,CAACY,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQN,oBAAoBA,CAAA;IAC1B,IAAI,CAACP,YAAY,CAACc,GAAG,CACnB,IAAI,CAACrB,eAAe,CAACsB,YAAY,CAACC,SAAS,CAACC,QAAQ,IAAG;MACrD,IAAI,CAAC9B,WAAW,GAAG8B,QAAQ;MAC3B,IAAI,CAAC5B,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC6B,iBAAiB,EAAE;MACxB,IAAI,CAACjB,YAAY,GAAG,CAAC;MACrB,IAAI,CAACkB,mBAAmB,EAAE;IAC5B,CAAC,CAAC,CACH;EACH;EAEQX,sBAAsBA,CAAA;IAC5B,IAAI,CAACR,YAAY,CAACc,GAAG,CACnB,IAAI,CAACpB,aAAa,CAAC0B,cAAc,CAACJ,SAAS,CAAClB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcQ,eAAeA,CAAA;IAAA,IAAAe,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAI;QACFD,KAAI,CAAChC,SAAS,GAAG,IAAI;QACrBgC,KAAI,CAACzF,KAAK,GAAG,IAAI;QACjB,MAAMyF,KAAI,CAAC5B,eAAe,CAACa,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC;OACjD,CAAC,OAAO1E,KAAK,EAAE;QACd2F,OAAO,CAAC3F,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDyF,KAAI,CAACzF,KAAK,GAAG,6BAA6B;QAC1CyF,KAAI,CAAChC,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA5C,cAAcA,CAAC+E,OAAgB;IAC7B,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACxD,GAAG,CAAC,CAAC;EACjD;EAEMnB,aAAaA,CAAC2E,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAChDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAACjC,aAAa,CAACoC,WAAW,CAACN,OAAO,CAACxD,GAAG,CAAC;QAChE,IAAI6D,MAAM,CAACE,OAAO,EAAE;UAClBR,OAAO,CAACS,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLV,OAAO,CAAC3F,KAAK,CAAC,yBAAyB,EAAEiG,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOrG,KAAK,EAAE;QACd2F,OAAO,CAAC3F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMmB,cAAcA,CAACyE,OAAgB,EAAEE,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAZ,iBAAA;MACjDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMO,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYd,OAAO,CAACxD,GAAG,EAAE;QACrE,MAAMuE,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAACxC,aAAa,CAACgD,YAAY,CAAClB,OAAO,CAACxD,GAAG,EAAE;UACjD2E,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,iCAAiCT,OAAO,CAAC5D,IAAI,SAAS4D,OAAO,CAACvD,KAAK;SAC7E,CAAC;QAEFsD,OAAO,CAACS,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOpG,KAAK,EAAE;QACd2F,OAAO,CAAC3F,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMuB,WAAWA,CAACqE,OAAgB,EAAEE,KAAY;IAAA,IAAAkB,MAAA;IAAA,OAAAtB,iBAAA;MAC9CI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMgB,MAAI,CAACjD,WAAW,CAACkD,SAAS,CAACrB,OAAO,CAACxD,GAAG,EAAE,CAAC,CAAC;QAChDuD,OAAO,CAACS,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAOpG,KAAK,EAAE;QACd2F,OAAO,CAAC3F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMyB,eAAeA,CAACmE,OAAgB,EAAEE,KAAY;IAAA,IAAAoB,MAAA;IAAA,OAAAxB,iBAAA;MAClDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMkB,MAAI,CAAClD,eAAe,CAACmD,aAAa,CAACvB,OAAO,CAACxD,GAAG,CAAC;QACrDuD,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAOpG,KAAK,EAAE;QACd2F,OAAO,CAAC3F,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAAC0F,OAAgB;IACpC,IAAIA,OAAO,CAACvF,aAAa,IAAIuF,OAAO,CAACvF,aAAa,GAAGuF,OAAO,CAACtD,KAAK,EAAE;MAClE,OAAO8E,IAAI,CAACC,KAAK,CAAE,CAACzB,OAAO,CAACvF,aAAa,GAAGuF,OAAO,CAACtD,KAAK,IAAIsD,OAAO,CAACvF,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAACkC,KAAa;IACvB,OAAO,IAAIgF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACrF,KAAK,CAAC;EAClB;EAEAL,UAAUA,CAACC,SAAe;IACxB,MAAM0F,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAAC3F,SAAS,CAAC;IACnC,MAAM6F,QAAQ,GAAGX,IAAI,CAACY,GAAG,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,OAAO,CAACG,OAAO,EAAE,CAAC;IAC5D,MAAMC,QAAQ,GAAGd,IAAI,CAACe,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB;EAEApI,OAAOA,CAAA;IACL,IAAI,CAAC4E,eAAe,EAAE;EACxB;EAEA0D,SAASA,CAAA;IACP,IAAI,CAACnE,MAAM,CAAC4B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCwC,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAc;KACtC,CAAC;EACJ;EAEAnG,cAAcA,CAACoG,SAAiB;IAC9B,OAAO,IAAI,CAACrE,aAAa,CAACsE,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEA/E,gBAAgBA,CAACiF,KAAa,EAAE7C,OAAgB;IAC9C,OAAOA,OAAO,CAACxD,GAAG;EACpB;EAEA;EACQyC,gBAAgBA,CAAA;IACtB,IAAI,CAAC6D,wBAAwB,EAAE;IAC/B,IAAI,CAACpD,iBAAiB,EAAE;IACxBkB,MAAM,CAACmC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,IAAI,CAACD,wBAAwB,EAAE,CAAC;EAC1E;EAEQA,wBAAwBA,CAAA;IAC9B,MAAME,cAAc,GAAGpC,MAAM,CAACqC,UAAU;IAExC,IAAID,cAAc,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACtE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAIiH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAACtE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAIiH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAACtE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM;MACL,IAAI,CAAC2C,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;;IAGtB,IAAI,CAAC2D,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQD,iBAAiBA,CAAA;IACvB,IAAI,CAACf,QAAQ,GAAG6C,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,IAAI,CAACvF,WAAW,CAACG,MAAM,GAAG,IAAI,CAACY,YAAY,CAAC;EAC1E;EAEQiB,mBAAmBA,CAAA;IACzB,IAAI,CAACjC,WAAW,GAAG,IAAI,CAACe,YAAY,IAAI,IAAI,CAAC1C,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC;EAChE;EAEAsB,SAASA,CAAA;IACP,IAAI,IAAI,CAACoB,YAAY,GAAG,IAAI,CAACE,QAAQ,EAAE;MACrC,IAAI,CAACF,YAAY,EAAE;MACnB,IAAI,CAACkB,mBAAmB,EAAE;;EAE9B;EAEAxC,SAASA,CAAA;IACP,IAAI,IAAI,CAACsB,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACkB,mBAAmB,EAAE;;EAE9B;EAEQT,cAAcA,CAAA;IACpB,IAAI,CAACiE,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAAC3E,YAAY,IAAI,IAAI,CAACE,QAAQ,EAAE;QACtC,IAAI,CAACF,YAAY,GAAG,CAAC;OACtB,MAAM;QACL,IAAI,CAACA,YAAY,EAAE;;MAErB,IAAI,CAACkB,mBAAmB,EAAE;IAC5B,CAAC,EAAE,IAAI,CAACf,cAAc,CAAC;EACzB;EAEQS,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC8D,iBAAiB,EAAE;MAC1BE,aAAa,CAAC,IAAI,CAACF,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEApG,cAAcA,CAAA;IACZ,IAAI,CAACsC,aAAa,EAAE;EACtB;EAEApC,eAAeA,CAAA;IACb,IAAI,CAACiC,cAAc,EAAE;EACvB;EAEA,IAAI1B,SAASA,CAAA;IACX,OAAO,IAAI,CAACiB,YAAY,GAAG,CAAC;EAC9B;EAEA,IAAIhB,SAASA,CAAA;IACX,OAAO,IAAI,CAACgB,YAAY,GAAG,IAAI,CAACE,QAAQ;EAC1C;;;uBAvPWZ,oBAAoB,EAAA/E,EAAA,CAAAsK,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAxK,EAAA,CAAAsK,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA1K,EAAA,CAAAsK,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5K,EAAA,CAAAsK,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA9K,EAAA,CAAAsK,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBjG,oBAAoB;MAAAkG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnL,EAAA,CAAAoL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf3B1L,EAJN,CAAAC,cAAA,aAAoC,aAEN,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,kBAAwD;UACxDF,EAAA,CAAAU,MAAA,qBACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,+BAAwB;UACtDV,EADsD,CAAAG,YAAA,EAAI,EACpD;UACNH,EAAA,CAAAC,cAAA,gBAAmD;UAAtBD,EAAA,CAAAW,UAAA,mBAAAiL,sDAAA;YAAA,OAASD,GAAA,CAAAnC,SAAA,EAAW;UAAA,EAAC;UAChDxJ,EAAA,CAAAU,MAAA,iBACA;UAAAV,EAAA,CAAAE,SAAA,mBAA4C;UAEhDF,EADE,CAAAG,YAAA,EAAS,EACL;UA2BNH,EAxBA,CAAAI,UAAA,KAAAyL,oCAAA,iBAAiD,KAAAC,oCAAA,iBAcQ,KAAAC,oCAAA,kBAWa;UA1CxE/L,EAAA,CAAAG,YAAA,EAAoC;;;UAiB5BH,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAoL,GAAA,CAAA9G,SAAA,CAAe;UAcf7E,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAoL,GAAA,CAAAvK,KAAA,KAAAuK,GAAA,CAAA9G,SAAA,CAAyB;UAUzB7E,EAAA,CAAAM,SAAA,EAAoD;UAApDN,EAAA,CAAAO,UAAA,UAAAoL,GAAA,CAAA9G,SAAA,KAAA8G,GAAA,CAAAvK,KAAA,IAAAuK,GAAA,CAAAhH,WAAA,CAAAG,MAAA,KAAoD;;;qBD1BhDlF,YAAY,EAAAoM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpM,WAAW,EAAAqM,EAAA,CAAAC,OAAA,EAAErM,cAAc;MAAAsM,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}