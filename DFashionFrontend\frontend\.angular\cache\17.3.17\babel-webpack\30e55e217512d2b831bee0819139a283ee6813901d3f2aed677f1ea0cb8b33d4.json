{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { isDevMode, Injectable, InjectionToken, PLATFORM_ID, Inject, Optional, Directive, Input, Component, HostListener, EventEmitter, ChangeDetectionStrategy, ContentChildren, Output, Attribute, HostBinding, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport { Subject, merge, of, fromEvent, from } from 'rxjs';\nimport { tap, filter, switchMap, first, take, skip, map, toArray, delay } from 'rxjs/operators';\nimport * as i1 from '@angular/router';\nimport { NavigationEnd } from '@angular/router';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\n/**\n * Defaults value of options\n */\nconst _c0 = (a0, a1, a2, a3, a4) => ({\n  \"width\": a0,\n  \"transform\": a1,\n  \"transition\": a2,\n  \"padding-left\": a3,\n  \"padding-right\": a4\n});\nconst _c1 = (a0, a1, a2, a3) => ({\n  \"width\": a0,\n  \"margin-left\": a1,\n  \"margin-right\": a2,\n  \"left\": a3\n});\nconst _c2 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction StageComponent_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction StageComponent_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, StageComponent_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const slide_r2 = ctx_r3.$implicit;\n    const i_r5 = ctx_r3.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", slide_r2.tplRef)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r2.preparePublicSlide(slide_r2), i_r5));\n  }\n}\nfunction StageComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵlistener(\"animationend\", function StageComponent_ng_container_2_Template_div_animationend_1_listener() {\n      const slide_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clear(slide_r2.id));\n    });\n    i0.ɵɵtemplate(2, StageComponent_ng_container_2_2_Template, 1, 5, null, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const slide_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", slide_r2.classes)(\"ngStyle\", i0.ɵɵpureFunction4(4, _c1, slide_r2.width + \"px\", slide_r2.marginL ? slide_r2.marginL + \"px\" : \"\", slide_r2.marginR ? slide_r2.marginR + \"px\" : \"\", slide_r2.left))(\"@autoHeight\", slide_r2.heightState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", slide_r2.load);\n  }\n}\nconst _c3 = (a0, a1, a2, a3, a4) => ({\n  \"owl-rtl\": a0,\n  \"owl-loaded\": a1,\n  \"owl-responsive\": a2,\n  \"owl-drag\": a3,\n  \"owl-grab\": a4\n});\nconst _c4 = (a0, a1) => ({\n  \"isMouseDragable\": a0,\n  \"isTouchDragable\": a1\n});\nconst _c5 = a0 => ({\n  \"disabled\": a0\n});\nconst _c6 = (a0, a1) => ({\n  \"active\": a0,\n  \"owl-dot-text\": a1\n});\nfunction CarouselComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"owl-stage\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"owlDraggable\", i0.ɵɵpureFunction2(3, _c4, ctx_r1.owlDOMData == null ? null : ctx_r1.owlDOMData.isMouseDragable, ctx_r1.owlDOMData == null ? null : ctx_r1.owlDOMData.isTouchDragable))(\"stageData\", ctx_r1.stageData)(\"slidesData\", ctx_r1.slidesData);\n  }\n}\nfunction CarouselComponent_ng_container_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_ng_container_3_div_5_Template_div_click_0_listener() {\n      const dot_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveByDot(dot_r5.id));\n    });\n    i0.ɵɵelement(1, \"span\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dot_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c6, dot_r5.active, dot_r5.showInnerContent));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dot_r5.innerContent, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction CarouselComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_ng_container_3_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prev());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_ng_container_3_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.next());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 9);\n    i0.ɵɵtemplate(5, CarouselComponent_ng_container_3_div_5_Template, 2, 5, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c5, ctx_r1.navData == null ? null : ctx_r1.navData.disabled));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c5, ctx_r1.navData == null ? null : ctx_r1.navData.prev == null ? null : ctx_r1.navData.prev.disabled))(\"innerHTML\", ctx_r1.navData == null ? null : ctx_r1.navData.prev == null ? null : ctx_r1.navData.prev.htmlText, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c5, ctx_r1.navData == null ? null : ctx_r1.navData.next == null ? null : ctx_r1.navData.next.disabled))(\"innerHTML\", ctx_r1.navData == null ? null : ctx_r1.navData.next == null ? null : ctx_r1.navData.next.htmlText, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c5, ctx_r1.dotsData == null ? null : ctx_r1.dotsData.disabled));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dotsData == null ? null : ctx_r1.dotsData.dots);\n  }\n}\nclass OwlCarouselOConfig {\n  items = 3;\n  skip_validateItems = false;\n  loop = false;\n  center = false;\n  rewind = false;\n  mouseDrag = true;\n  touchDrag = true;\n  pullDrag = true;\n  freeDrag = false;\n  margin = 0;\n  stagePadding = 0;\n  merge = false;\n  mergeFit = true;\n  autoWidth = false;\n  startPosition = 0;\n  rtl = false;\n  smartSpeed = 250;\n  fluidSpeed = false;\n  dragEndSpeed = false;\n  responsive = {};\n  responsiveRefreshRate = 200;\n  // defaults to Navigation\n  nav = false;\n  navText = ['prev', 'next'];\n  navSpeed = false;\n  slideBy = 1; // stage moves on 1 width of slide; if slideBy = 2, stage moves on 2 widths of slide\n  dots = true;\n  dotsEach = false;\n  dotsData = false;\n  dotsSpeed = false;\n  // defaults to Autoplay\n  autoplay = false;\n  autoplayTimeout = 5000;\n  autoplayHoverPause = false;\n  autoplaySpeed = false;\n  autoplayMouseleaveTimeout = 1;\n  // defaults to LazyLoading\n  lazyLoad = false;\n  lazyLoadEager = 0;\n  // defaults to Animate\n  slideTransition = '';\n  animateOut = false;\n  animateIn = false;\n  // defaults to AutoHeight\n  autoHeight = false;\n  // defaults to Hash\n  URLhashListener = false;\n  constructor() {}\n}\n/**\n * we can't read types from OwlOptions in javascript because of props have undefined value and types of those props are used for validating inputs\n * class below is copy of OwlOptions but its all props have string value showing certain type;\n * this is class is being used just in method _validateOptions() of CarouselService;\n */\nclass OwlOptionsMockedTypes {\n  items = 'number';\n  skip_validateItems = 'boolean';\n  loop = 'boolean';\n  center = 'boolean';\n  rewind = 'boolean';\n  mouseDrag = 'boolean';\n  touchDrag = 'boolean';\n  pullDrag = 'boolean';\n  freeDrag = 'boolean';\n  margin = 'number';\n  stagePadding = 'number';\n  merge = 'boolean';\n  mergeFit = 'boolean';\n  autoWidth = 'boolean';\n  startPosition = 'number|string';\n  rtl = 'boolean';\n  smartSpeed = 'number';\n  fluidSpeed = 'boolean';\n  dragEndSpeed = 'number|boolean';\n  responsive = {};\n  responsiveRefreshRate = 'number';\n  // defaults to Navigation\n  nav = 'boolean';\n  navText = 'string[]';\n  navSpeed = 'number|boolean';\n  slideBy = 'number|string'; // stage moves on 1 width of slide; if slideBy = 2, stage moves on 2 widths of slide\n  dots = 'boolean';\n  dotsEach = 'number|boolean';\n  dotsData = 'boolean';\n  dotsSpeed = 'number|boolean';\n  // defaults to Autoplay\n  autoplay = 'boolean';\n  autoplayTimeout = 'number';\n  autoplayHoverPause = 'boolean';\n  autoplaySpeed = 'number|boolean';\n  autoplayMouseleaveTimeout = 'number';\n  // defaults to LazyLoading\n  lazyLoad = 'boolean';\n  lazyLoadEager = 'number';\n  // defaults to Animate\n  slideTransition = 'string';\n  animateOut = 'string|boolean';\n  animateIn = 'string|boolean';\n  // defaults to AutoHeight\n  autoHeight = 'boolean';\n  // defaults to Hash\n  URLhashListener = \"boolean\";\n  constructor() {}\n}\nlet OwlLogger = /*#__PURE__*/(() => {\n  class OwlLogger {\n    errorHandler;\n    constructor(errorHandler) {\n      this.errorHandler = errorHandler;\n    }\n    log(value, ...rest) {\n      if (isDevMode()) {\n        console.log(value, ...rest);\n      }\n    }\n    error(error) {\n      this.errorHandler.handleError(error);\n    }\n    warn(value, ...rest) {\n      console.warn(value, ...rest);\n    }\n    static ɵfac = function OwlLogger_Factory(t) {\n      return new (t || OwlLogger)(i0.ɵɵinject(i0.ErrorHandler));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OwlLogger,\n      factory: OwlLogger.ɵfac\n    });\n  }\n  return OwlLogger;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Current state information and their tags.\n */\nclass States {\n  current;\n  tags;\n}\n/**\n * Enumeration for types.\n * @enum {String}\n */\nvar Type = /*#__PURE__*/function (Type) {\n  Type[\"Event\"] = \"event\";\n  Type[\"State\"] = \"state\";\n  return Type;\n}(Type || {});\n;\n/**\n * Enumeration for width.\n * @enum {String}\n */\nvar Width = /*#__PURE__*/function (Width) {\n  Width[\"Default\"] = \"default\";\n  Width[\"Inner\"] = \"inner\";\n  Width[\"Outer\"] = \"outer\";\n  return Width;\n}(Width || {});\n;\n/**\n * Model for coords of .owl-stage\n */\nclass Coords {\n  x;\n  y;\n}\n/**\n * Model for all current data of carousel\n */\nclass CarouselCurrentData {\n  owlDOMData;\n  stageData;\n  slidesData;\n  navData;\n  dotsData;\n}\nlet CarouselService = /*#__PURE__*/(() => {\n  class CarouselService {\n    logger;\n    /**\n     * Subject for passing data needed for managing View\n     */\n    _viewSettingsShipper$ = new Subject();\n    /**\n     * Subject for notification when the carousel got initializes\n     */\n    _initializedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel's settings start changinf\n     */\n    _changeSettingsCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel's settings have changed\n     */\n    _changedSettingsCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel starts translating or moving\n     */\n    _translateCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel stopped translating or moving\n     */\n    _translatedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the carousel's rebuilding caused by 'resize' event starts\n     */\n    _resizeCarousel$ = new Subject();\n    /**\n     * Subject for notification  when the carousel's rebuilding caused by 'resize' event is ended\n     */\n    _resizedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the refresh of carousel starts\n     */\n    _refreshCarousel$ = new Subject();\n    /**\n     * Subject for notification when the refresh of carousel is ended\n     */\n    _refreshedCarousel$ = new Subject();\n    /**\n     * Subject for notification when the dragging of carousel starts\n     */\n    _dragCarousel$ = new Subject();\n    /**\n     * Subject for notification when the dragging of carousel is ended\n     */\n    _draggedCarousel$ = new Subject();\n    /**\n     * Current settings for the carousel.\n     */\n    settings = {\n      items: 0\n    };\n    /**\n     * Initial data for setting classes to element .owl-carousel\n     */\n    owlDOMData = {\n      rtl: false,\n      isResponsive: false,\n      isRefreshed: false,\n      isLoaded: false,\n      isLoading: false,\n      isMouseDragable: false,\n      isGrab: false,\n      isTouchDragable: false\n    };\n    /**\n     * Initial data of .owl-stage\n     */\n    stageData = {\n      transform: 'translate3d(0px,0px,0px)',\n      transition: '0s',\n      width: 0,\n      paddingL: 0,\n      paddingR: 0\n    };\n    /**\n     *  Data of every slide\n     */\n    slidesData;\n    /**\n     * Data of navigation block\n     */\n    navData;\n    /**\n     * Data of dots block\n     */\n    dotsData;\n    /**\n     * Carousel width\n     */\n    _width;\n    /**\n     * All real items.\n     */\n    _items = []; // is equal to this.slides\n    /**\n     * Array with width of every slide.\n     */\n    _widths = [];\n    /**\n     * Currently suppressed events to prevent them from beeing retriggered.\n     */\n    _supress = {};\n    /**\n     * References to the running plugins of this carousel.\n     */\n    _plugins = {};\n    /**\n     * Absolute current position.\n     */\n    _current = null;\n    /**\n     * All cloned items.\n     */\n    _clones = [];\n    /**\n     * Merge values of all items.\n     * @todo Maybe this could be part of a plugin.\n     */\n    _mergers = [];\n    /**\n     * Animation speed in milliseconds.\n     */\n    _speed = null;\n    /**\n     * Coordinates of all items in pixel.\n     * @todo The name of this member is missleading.\n     */\n    _coordinates = [];\n    /**\n     * Current breakpoint.\n     * @todo Real media queries would be nice.\n     */\n    _breakpoint = null;\n    /**\n     * Prefix for id of cloned slides\n     */\n    clonedIdPrefix = 'cloned-';\n    /**\n     * Current options set by the caller including defaults.\n     */\n    _options = {};\n    /**\n     * Invalidated parts within the update process.\n     */\n    _invalidated = {};\n    // Is needed for tests\n    get invalidated() {\n      return this._invalidated;\n    }\n    /**\n     * Current state information and their tags.\n     */\n    _states = {\n      current: {},\n      tags: {\n        initializing: ['busy'],\n        animating: ['busy'],\n        dragging: ['interacting']\n      }\n    };\n    // is needed for tests\n    get states() {\n      return this._states;\n    }\n    /**\n         * Ordered list of workers for the update process.\n     */\n    _pipe = [\n    // {\n    //   filter: ['width', 'settings'],\n    //   run: () => {\n    //     this._width = this.carouselWindowWidth;\n    //   }\n    // },\n    {\n      filter: ['width', 'items', 'settings'],\n      run: cache => {\n        cache.current = this._items && this._items[this.relative(this._current)]?.id;\n      }\n    },\n    // {\n    //   filter: ['items', 'settings'],\n    //   run: function() {\n    //     // this.$stage.children('.cloned').remove();\n    //   }\n    // },\n    {\n      filter: ['width', 'items', 'settings'],\n      run: cache => {\n        const margin = this.settings.margin || '',\n          grid = !this.settings.autoWidth,\n          rtl = this.settings.rtl,\n          css = {\n            'margin-left': rtl ? margin : '',\n            'margin-right': rtl ? '' : margin\n          };\n        if (!grid) {\n          this.slidesData.forEach(slide => {\n            slide.marginL = css['margin-left'];\n            slide.marginR = css['margin-right'];\n          });\n        }\n        cache.css = css;\n      }\n    }, {\n      filter: ['width', 'items', 'settings'],\n      run: cache => {\n        const width = +(this.width() / this.settings.items).toFixed(3) - this.settings.margin,\n          grid = !this.settings.autoWidth,\n          widths = [];\n        let merge = null,\n          iterator = this._items.length;\n        cache.items = {\n          merge: false,\n          width: width\n        };\n        while (iterator-- > 0) {\n          merge = this._mergers[iterator];\n          merge = this.settings.mergeFit && Math.min(merge, this.settings.items) || merge;\n          cache.items.merge = merge > 1 || cache.items.merge;\n          widths[iterator] = !grid ? this._items[iterator].width ? this._items[iterator].width : width : width * merge;\n        }\n        this._widths = widths;\n        this.slidesData.forEach((slide, i) => {\n          slide.width = this._widths[i];\n          slide.marginR = cache.css['margin-right'];\n          slide.marginL = cache.css['margin-left'];\n        });\n      }\n    }, {\n      filter: ['items', 'settings'],\n      run: () => {\n        const clones = [],\n          items = this._items,\n          settings = this.settings,\n          // TODO: Should be computed from number of min width items in stage\n          view = Math.max(settings.items * 2, 4),\n          size = Math.ceil(items.length / 2) * 2;\n        let append = [],\n          prepend = [],\n          repeat = settings.loop && items.length ? settings.rewind ? view : Math.max(view, size) : 0;\n        repeat /= 2;\n        while (repeat-- > 0) {\n          // Switch to only using appended clones\n          clones.push(this.normalize(clones.length / 2, true));\n          append.push({\n            ...this.slidesData[clones[clones.length - 1]]\n          });\n          clones.push(this.normalize(items.length - 1 - (clones.length - 1) / 2, true));\n          prepend.unshift({\n            ...this.slidesData[clones[clones.length - 1]]\n          });\n        }\n        this._clones = clones;\n        append = append.map(slide => {\n          slide.id = `${this.clonedIdPrefix}${slide.id}`;\n          slide.isActive = false;\n          slide.isCloned = true;\n          return slide;\n        });\n        prepend = prepend.map(slide => {\n          slide.id = `${this.clonedIdPrefix}${slide.id}`;\n          slide.isActive = false;\n          slide.isCloned = true;\n          return slide;\n        });\n        this.slidesData = prepend.concat(this.slidesData).concat(append);\n      }\n    }, {\n      filter: ['width', 'items', 'settings'],\n      run: () => {\n        const rtl = this.settings.rtl ? 1 : -1,\n          size = this._clones.length + this._items.length,\n          coordinates = [];\n        let iterator = -1,\n          previous = 0,\n          current = 0;\n        while (++iterator < size) {\n          previous = coordinates[iterator - 1] || 0;\n          current = this._widths[this.relative(iterator)] + this.settings.margin;\n          coordinates.push(previous + current * rtl);\n        }\n        this._coordinates = coordinates;\n      }\n    }, {\n      filter: ['width', 'items', 'settings'],\n      run: () => {\n        const padding = this.settings.stagePadding,\n          coordinates = this._coordinates,\n          css = {\n            'width': Math.ceil(Math.abs(coordinates[coordinates.length - 1])) + padding * 2,\n            'padding-left': padding || '',\n            'padding-right': padding || ''\n          };\n        this.stageData.width = css.width; // use this property in *ngIf directive for .owl-stage element\n        this.stageData.paddingL = css['padding-left'];\n        this.stageData.paddingR = css['padding-right'];\n      }\n    }, {\n      //   filter: [ 'width', 'items', 'settings' ],\n      //   run: cache => {\n      // \t\t// this method sets the width for every slide, but I set it in different way earlier\n      // \t\tconst grid = !this.settings.autoWidth,\n      // \t\titems = this.$stage.children(); // use this.slidesData\n      //     let iterator = this._coordinates.length;\n      //     if (grid && cache.items.merge) {\n      //       while (iterator--) {\n      //         cache.css.width = this._widths[this.relative(iterator)];\n      //         items.eq(iterator).css(cache.css);\n      //       }\n      //     } else if (grid) {\n      //       cache.css.width = cache.items.width;\n      //       items.css(cache.css);\n      //     }\n      //   }\n      // }, {\n      //   filter: [ 'items' ],\n      //   run: function() {\n      //     this._coordinates.length < 1 && this.$stage.removeAttr('style');\n      //   }\n      // }, {\n      filter: ['width', 'items', 'settings'],\n      run: cache => {\n        let current = cache.current ? this.slidesData.findIndex(slide => slide.id === cache.current) : 0;\n        current = Math.max(this.minimum(), Math.min(this.maximum(), current));\n        this.reset(current);\n      }\n    }, {\n      filter: ['position'],\n      run: () => {\n        this.animate(this.coordinates(this._current));\n      }\n    }, {\n      filter: ['width', 'position', 'items', 'settings'],\n      run: () => {\n        const rtl = this.settings.rtl ? 1 : -1,\n          padding = this.settings.stagePadding * 2,\n          matches = [];\n        let begin, end, inner, outer, i, n;\n        begin = this.coordinates(this.current());\n        if (typeof begin === 'number') {\n          begin += padding;\n        } else {\n          begin = 0;\n        }\n        end = begin + this.width() * rtl;\n        if (rtl === -1 && this.settings.center) {\n          const result = this._coordinates.filter(element => {\n            return this.settings.items % 2 === 1 ? element >= begin : element > begin;\n          });\n          begin = result.length ? result[result.length - 1] : begin;\n        }\n        for (i = 0, n = this._coordinates.length; i < n; i++) {\n          inner = Math.ceil(this._coordinates[i - 1] || 0);\n          outer = Math.ceil(Math.abs(this._coordinates[i]) + padding * rtl);\n          if (this._op(inner, '<=', begin) && this._op(inner, '>', end) || this._op(outer, '<', begin) && this._op(outer, '>', end)) {\n            matches.push(i);\n          }\n        }\n        this.slidesData.forEach(slide => {\n          slide.isActive = false;\n          return slide;\n        });\n        matches.forEach(item => {\n          this.slidesData[item].isActive = true;\n        });\n        if (this.settings.center) {\n          this.slidesData.forEach(slide => {\n            slide.isCentered = false;\n            return slide;\n          });\n          this.slidesData[this.current()].isCentered = true;\n        }\n      }\n    }];\n    constructor(logger) {\n      this.logger = logger;\n    }\n    /**\n     * Makes _viewSettingsShipper$ Subject become Observable\n     * @returns Observable of _viewSettingsShipper$ Subject\n     */\n    getViewCurSettings() {\n      return this._viewSettingsShipper$.asObservable();\n    }\n    /**\n     * Makes _initializedCarousel$ Subject become Observable\n     * @returns Observable of _initializedCarousel$ Subject\n     */\n    getInitializedState() {\n      return this._initializedCarousel$.asObservable();\n    }\n    /**\n     * Makes _changeSettingsCarousel$ Subject become Observable\n     * @returns Observable of _changeSettingsCarousel$ Subject\n     */\n    getChangeState() {\n      return this._changeSettingsCarousel$.asObservable();\n    }\n    /**\n     * Makes _changedSettingsCarousel$ Subject become Observable\n     * @returns Observable of _changedSettingsCarousel$ Subject\n     */\n    getChangedState() {\n      return this._changedSettingsCarousel$.asObservable();\n    }\n    /**\n     * Makes _translateCarousel$ Subject become Observable\n     * @returns Observable of _translateCarousel$ Subject\n     */\n    getTranslateState() {\n      return this._translateCarousel$.asObservable();\n    }\n    /**\n     * Makes _translatedCarousel$ Subject become Observable\n     * @returns Observable of _translatedCarousel$ Subject\n     */\n    getTranslatedState() {\n      return this._translatedCarousel$.asObservable();\n    }\n    /**\n     * Makes _resizeCarousel$ Subject become Observable\n     * @returns Observable of _resizeCarousel$ Subject\n     */\n    getResizeState() {\n      return this._resizeCarousel$.asObservable();\n    }\n    /**\n     * Makes _resizedCarousel$ Subject become Observable\n     * @returns Observable of _resizedCarousel$ Subject\n     */\n    getResizedState() {\n      return this._resizedCarousel$.asObservable();\n    }\n    /**\n     * Makes _refreshCarousel$ Subject become Observable\n     * @returns Observable of _refreshCarousel$ Subject\n     */\n    getRefreshState() {\n      return this._refreshCarousel$.asObservable();\n    }\n    /**\n     * Makes _refreshedCarousel$ Subject become Observable\n     * @returns Observable of _refreshedCarousel$ Subject\n     */\n    getRefreshedState() {\n      return this._refreshedCarousel$.asObservable();\n    }\n    /**\n     * Makes _dragCarousel$ Subject become Observable\n     * @returns Observable of _dragCarousel$ Subject\n     */\n    getDragState() {\n      return this._dragCarousel$.asObservable();\n    }\n    /**\n     * Makes _draggedCarousel$ Subject become Observable\n     * @returns Observable of _draggedCarousel$ Subject\n     */\n    getDraggedState() {\n      return this._draggedCarousel$.asObservable();\n    }\n    /**\n     * Setups custom options expanding default options\n     * @param options custom options\n     */\n    setOptions(options) {\n      const configOptions = new OwlCarouselOConfig();\n      const checkedOptions = this._validateOptions(options, configOptions);\n      this._options = {\n        ...configOptions,\n        ...checkedOptions\n      };\n    }\n    /**\n     * Checks whether user's option are set properly. Cheking is based on typings;\n     * @param options options set by user\n     * @param configOptions default options\n     * @returns checked and modified (if it's needed) user's options\n     *\n     * Notes:\n     * \t- if user set option with wrong type, it'll be written in console\n     */\n    _validateOptions(options, configOptions) {\n      const checkedOptions = {\n        ...options\n      };\n      const mockedTypes = new OwlOptionsMockedTypes();\n      const setRightOption = (type, key) => {\n        this.logger.log(`options.${key} must be type of ${type}; ${key}=${options[key]} skipped to defaults: ${key}=${configOptions[key]}`);\n        return configOptions[key];\n      };\n      for (const key in checkedOptions) {\n        if (checkedOptions.hasOwnProperty(key)) {\n          // condition could be shortened but it gets harder for understanding\n          if (mockedTypes[key] === 'number') {\n            if (this._isNumeric(checkedOptions[key])) {\n              checkedOptions[key] = +checkedOptions[key];\n              checkedOptions[key] = key === 'items' ? this._validateItems(checkedOptions[key], checkedOptions.skip_validateItems) : checkedOptions[key];\n            } else {\n              checkedOptions[key] = setRightOption(mockedTypes[key], key);\n            }\n          } else if (mockedTypes[key] === 'boolean' && typeof checkedOptions[key] !== 'boolean') {\n            checkedOptions[key] = setRightOption(mockedTypes[key], key);\n          } else if (mockedTypes[key] === 'number|boolean' && !this._isNumberOrBoolean(checkedOptions[key])) {\n            checkedOptions[key] = setRightOption(mockedTypes[key], key);\n          } else if (mockedTypes[key] === 'number|string' && !this._isNumberOrString(checkedOptions[key])) {\n            checkedOptions[key] = setRightOption(mockedTypes[key], key);\n          } else if (mockedTypes[key] === 'string|boolean' && !this._isStringOrBoolean(checkedOptions[key])) {\n            checkedOptions[key] = setRightOption(mockedTypes[key], key);\n          } else if (mockedTypes[key] === 'string[]') {\n            if (Array.isArray(checkedOptions[key])) {\n              let isString = false;\n              checkedOptions[key].forEach(element => {\n                isString = typeof element === 'string' ? true : false;\n              });\n              if (!isString) {\n                checkedOptions[key] = setRightOption(mockedTypes[key], key);\n              }\n              ;\n            } else {\n              checkedOptions[key] = setRightOption(mockedTypes[key], key);\n            }\n          }\n        }\n      }\n      return checkedOptions;\n    }\n    /**\n     * Checks the option `items` set by user and if it bigger than number of slides, the function returns number of slides\n     * @param items option items set by user\n     * @param skip_validateItems option `skip_validateItems` set by user\n     * @returns right number of items\n     */\n    _validateItems(items, skip_validateItems) {\n      let result = items;\n      if (items > this._items.length) {\n        if (skip_validateItems) {\n          this.logger.log('The option \\'items\\' in your options is bigger than the number of slides. The navigation got disabled');\n        } else {\n          result = this._items.length;\n          this.logger.log('The option \\'items\\' in your options is bigger than the number of slides. This option is updated to the current number of slides and the navigation got disabled');\n        }\n      } else {\n        if (items === this._items.length && (this.settings.dots || this.settings.nav)) {\n          this.logger.log('Option \\'items\\' in your options is equal to the number of slides. So the navigation got disabled');\n        }\n      }\n      return result;\n    }\n    /**\n     * Set current width of carousel\n     * @param width width of carousel Window\n     */\n    setCarouselWidth(width) {\n      this._width = width;\n    }\n    /**\n     * Setups the current settings.\n     * @todo Remove responsive classes. Why should adaptive designs be brought into IE8?\n     * @todo Support for media queries by using `matchMedia` would be nice.\n     * @param carouselWidth width of carousel\n     * @param slides array of slides\n     * @param options options set by user\n     */\n    setup(carouselWidth, slides, options) {\n      this.setCarouselWidth(carouselWidth);\n      this.setItems(slides);\n      this._defineSlidesData();\n      this.setOptions(options);\n      this.settings = {\n        ...this._options\n      };\n      this.setOptionsForViewport();\n      this._trigger('change', {\n        property: {\n          name: 'settings',\n          value: this.settings\n        }\n      });\n      this.invalidate('settings'); // must be call of this function;\n      this._trigger('changed', {\n        property: {\n          name: 'settings',\n          value: this.settings\n        }\n      });\n    }\n    /**\n     * Set options for current viewport\n     */\n    setOptionsForViewport() {\n      const viewport = this._width,\n        overwrites = this._options.responsive;\n      let match = -1;\n      if (!Object.keys(overwrites).length) {\n        return;\n      }\n      if (!viewport) {\n        this.settings.items = 1;\n        return;\n      }\n      for (const key in overwrites) {\n        if (overwrites.hasOwnProperty(key)) {\n          if (+key <= viewport && +key > match) {\n            match = Number(key);\n          }\n        }\n      }\n      this.settings = {\n        ...this._options,\n        ...overwrites[match],\n        items: overwrites[match] && overwrites[match].items ? this._validateItems(overwrites[match].items, this._options.skip_validateItems) : this._options.items\n      };\n      // if (typeof this.settings.stagePadding === 'function') {\n      // \tthis.settings.stagePadding = this.settings.stagePadding();\n      // }\n      delete this.settings.responsive;\n      this.owlDOMData.isResponsive = true;\n      this.owlDOMData.isMouseDragable = this.settings.mouseDrag;\n      this.owlDOMData.isTouchDragable = this.settings.touchDrag;\n      const mergers = [];\n      this._items.forEach(item => {\n        const mergeN = this.settings.merge ? item.dataMerge : 1;\n        mergers.push(mergeN);\n      });\n      this._mergers = mergers;\n      this._breakpoint = match;\n      this.invalidate('settings');\n    }\n    /**\n     * Initializes the carousel.\n     * @param slides array of CarouselSlideDirective\n     */\n    initialize(slides) {\n      this.enter('initializing');\n      // this.trigger('initialize');\n      this.owlDOMData.rtl = this.settings.rtl;\n      if (this._mergers.length) {\n        this._mergers = [];\n      }\n      slides.forEach(item => {\n        const mergeN = this.settings.merge ? item.dataMerge : 1;\n        this._mergers.push(mergeN);\n      });\n      this._clones = [];\n      this.reset(this._isNumeric(this.settings.startPosition) ? +this.settings.startPosition : 0);\n      this.invalidate('items');\n      this.refresh();\n      this.owlDOMData.isLoaded = true;\n      this.owlDOMData.isMouseDragable = this.settings.mouseDrag;\n      this.owlDOMData.isTouchDragable = this.settings.touchDrag;\n      this.sendChanges();\n      this.leave('initializing');\n      this._trigger('initialized');\n    }\n    /**\n     * Sends all data needed for View\n     */\n    sendChanges() {\n      this._viewSettingsShipper$.next({\n        owlDOMData: this.owlDOMData,\n        stageData: this.stageData,\n        slidesData: this.slidesData,\n        navData: this.navData,\n        dotsData: this.dotsData\n      });\n    }\n    /**\n     * Updates option logic if necessery\n     */\n    _optionsLogic() {\n      if (this.settings.autoWidth) {\n        this.settings.stagePadding = 0;\n        this.settings.merge = false;\n      }\n    }\n    /**\n     * Updates the view\n     */\n    update() {\n      let i = 0;\n      const n = this._pipe.length,\n        filter = item => this._invalidated[item],\n        cache = {};\n      while (i < n) {\n        const filteredPipe = this._pipe[i].filter.filter(filter);\n        if (this._invalidated.all || filteredPipe.length > 0) {\n          this._pipe[i].run(cache);\n        }\n        i++;\n      }\n      this.slidesData.forEach(slide => slide.classes = this.setCurSlideClasses(slide));\n      this.sendChanges();\n      this._invalidated = {};\n      if (!this.is('valid')) {\n        this.enter('valid');\n      }\n    }\n    /**\n     * Gets the width of the view.\n     * @param [dimension=Width.Default] The dimension to return\n     * @returns The width of the view in pixel.\n     */\n    width(dimension) {\n      dimension = dimension || Width.Default;\n      switch (dimension) {\n        case Width.Inner:\n        case Width.Outer:\n          return this._width;\n        default:\n          return this._width - this.settings.stagePadding * 2 + this.settings.margin;\n      }\n    }\n    /**\n     * Refreshes the carousel primarily for adaptive purposes.\n     */\n    refresh() {\n      this.enter('refreshing');\n      this._trigger('refresh');\n      this._defineSlidesData();\n      this.setOptionsForViewport();\n      this._optionsLogic();\n      // this.$element.addClass(this.options.refreshClass);\n      this.update();\n      // this.$element.removeClass(this.options.refreshClass);\n      this.leave('refreshing');\n      this._trigger('refreshed');\n    }\n    /**\n     * Checks window `resize` event.\n     * @param curWidth width of .owl-carousel\n     */\n    onResize(curWidth) {\n      if (!this._items.length) {\n        return false;\n      }\n      this.setCarouselWidth(curWidth);\n      this.enter('resizing');\n      // if (this.trigger('resize').isDefaultPrevented()) {\n      // \tthis.leave('resizing');\n      // \treturn false;\n      // }\n      this._trigger('resize');\n      this.invalidate('width');\n      this.refresh();\n      this.leave('resizing');\n      this._trigger('resized');\n    }\n    /**\n     * Prepares data for dragging carousel. It starts after firing `touchstart` and `mousedown` events.\n     * @todo Horizontal swipe threshold as option\n     * @todo #261\n     * @param event - The event arguments.\n     * @returns stage - object with 'x' and 'y' coordinates of .owl-stage\n     */\n    prepareDragging(event) {\n      let stage = null,\n        transformArr;\n      // could be 5 commented lines below; However there's stage transform in stageData and in updates after each move of stage\n      // stage = getComputedStyle(this.el.nativeElement).transform.replace(/.*\\(|\\)| /g, '').split(',');\n      // stage = {\n      //   x: stage[stage.length === 16 ? 12 : 4],\n      //   y: stage[stage.length === 16 ? 13 : 5]\n      // };\n      transformArr = this.stageData.transform.replace(/.*\\(|\\)| |[^,-\\d]\\w|\\)/g, '').split(',');\n      stage = {\n        x: +transformArr[0],\n        y: +transformArr[1]\n      };\n      if (this.is('animating')) {\n        this.invalidate('position');\n      }\n      if (event.type === 'mousedown') {\n        this.owlDOMData.isGrab = true;\n      }\n      this.speed(0);\n      return stage;\n    }\n    /**\n     * Enters into a 'dragging' state\n     */\n    enterDragging() {\n      this.enter('dragging');\n      this._trigger('drag');\n    }\n    /**\n     * Defines new coords for .owl-stage while dragging it\n     * @todo #261\n     * @param event the event arguments.\n     * @param dragData initial data got after starting dragging\n     * @returns coords or false\n     */\n    defineNewCoordsDrag(event, dragData) {\n      let minimum = null,\n        maximum = null,\n        pull = null;\n      const delta = this.difference(dragData.pointer, this.pointer(event)),\n        stage = this.difference(dragData.stage.start, delta);\n      if (!this.is('dragging')) {\n        return false;\n      }\n      if (this.settings.loop) {\n        minimum = this.coordinates(this.minimum());\n        maximum = +this.coordinates(this.maximum() + 1) - minimum;\n        stage.x = ((stage.x - minimum) % maximum + maximum) % maximum + minimum;\n      } else {\n        minimum = this.settings.rtl ? this.coordinates(this.maximum()) : this.coordinates(this.minimum());\n        maximum = this.settings.rtl ? this.coordinates(this.minimum()) : this.coordinates(this.maximum());\n        pull = this.settings.pullDrag ? -1 * delta.x / 5 : 0;\n        stage.x = Math.max(Math.min(stage.x, minimum + pull), maximum + pull);\n      }\n      return stage;\n    }\n    /**\n     * Finishes dragging of carousel when `touchend` and `mouseup` events fire.\n     * @todo #261\n     * @todo Threshold for click event\n     * @param event the event arguments.\n     * @param dragObj the object with dragging settings and states\n     * @param clickAttacher function which attaches click handler to slide or its children elements in order to prevent event bubling\n     */\n    finishDragging(event, dragObj, clickAttacher) {\n      const directions = ['right', 'left'],\n        delta = this.difference(dragObj.pointer, this.pointer(event)),\n        stage = dragObj.stage.current,\n        direction = directions[+(this.settings.rtl ? delta.x < +this.settings.rtl : delta.x > +this.settings.rtl)];\n      let currentSlideI, current, newCurrent;\n      if (delta.x !== 0 && this.is('dragging') || !this.is('valid')) {\n        this.speed(+this.settings.dragEndSpeed || this.settings.smartSpeed);\n        currentSlideI = this.closest(stage.x, delta.x !== 0 ? direction : dragObj.direction);\n        current = this.current();\n        newCurrent = this.current(currentSlideI === -1 ? undefined : currentSlideI);\n        if (current !== newCurrent) {\n          this.invalidate('position');\n          this.update();\n        }\n        dragObj.direction = direction;\n        if (Math.abs(delta.x) > 3 || new Date().getTime() - dragObj.time > 300) {\n          clickAttacher();\n        }\n      }\n      if (!this.is('dragging')) {\n        return;\n      }\n      this.leave('dragging');\n      this._trigger('dragged');\n    }\n    /**\n     * Gets absolute position of the closest item for a coordinate.\n     * @todo Setting `freeDrag` makes `closest` not reusable. See #165.\n     * @param coordinate The coordinate in pixel.\n     * @param direction The direction to check for the closest item. Ether `left` or `right`.\n     * @returns The absolute position of the closest item.\n     */\n    closest(coordinate, direction) {\n      const pull = 30,\n        width = this.width();\n      let coordinates = this.coordinates(),\n        position = -1;\n      if (this.settings.center) {\n        coordinates = coordinates.map(item => {\n          if (item === 0) {\n            item += 0.000001;\n          }\n          return item;\n        });\n      }\n      // option 'freeDrag' doesn't have realization and using it here creates problem:\n      // variable 'position' stays unchanged (it equals -1 at the begging) and thus method returns -1\n      // Returning value is consumed by method current(), which taking -1 as argument calculates the index of new current slide\n      // In case of having 5 slides ans 'loop=false; calling 'current(-1)' sets props '_current' as 4. Just last slide remains visible instead of 3 last slides.\n      // if (!this.settings.freeDrag) {\n      // check closest item\n      for (let i = 0; i < coordinates.length; i++) {\n        if (direction === 'left' && coordinate > coordinates[i] - pull && coordinate < coordinates[i] + pull) {\n          position = i;\n          // on a right pull, check on previous index\n          // to do so, subtract width from value and set position = index + 1\n        } else if (direction === 'right' && coordinate > coordinates[i] - width - pull && coordinate < coordinates[i] - width + pull) {\n          position = i + 1;\n        } else if (this._op(coordinate, '<', coordinates[i]) && this._op(coordinate, '>', coordinates[i + 1] || coordinates[i] - width)) {\n          position = direction === 'left' ? i + 1 : i;\n        } else if (direction === null && coordinate > coordinates[i] - pull && coordinate < coordinates[i] + pull) {\n          position = i;\n        }\n        if (position !== -1) {\n          break;\n        }\n        ;\n      }\n      // }\n      if (!this.settings.loop) {\n        // non loop boundries\n        if (this._op(coordinate, '>', coordinates[this.minimum()])) {\n          position = coordinate = this.minimum();\n        } else if (this._op(coordinate, '<', coordinates[this.maximum()])) {\n          position = coordinate = this.maximum();\n        }\n      }\n      return position;\n    }\n    /**\n     * Animates the stage.\n     * @todo #270\n     * @param coordinate The coordinate in pixels.\n     */\n    animate(coordinate) {\n      const animate = this.speed() > 0;\n      if (this.is('animating')) {\n        this.onTransitionEnd();\n      }\n      if (animate) {\n        this.enter('animating');\n        this._trigger('translate');\n      }\n      this.stageData.transform = 'translate3d(' + coordinate + 'px,0px,0px)';\n      this.stageData.transition = this.speed() / 1000 + 's' + (this.settings.slideTransition ? ' ' + this.settings.slideTransition : '');\n      // also there was transition by means of JQuery.animate or css-changing property left\n    }\n    /**\n     * Checks whether the carousel is in a specific state or not.\n     * @param state The state to check.\n     * @returns The flag which indicates if the carousel is busy.\n     */\n    is(state) {\n      return this._states.current[state] && this._states.current[state] > 0;\n    }\n    /**\n     * Sets the absolute position of the current item.\n     * @param position The new absolute position or nothing to leave it unchanged.\n     * @returns The absolute position of the current item.\n     */\n    current(position) {\n      if (position === undefined) {\n        return this._current;\n      }\n      if (this._items.length === 0) {\n        return undefined;\n      }\n      position = this.normalize(position);\n      if (this._current !== position) {\n        const event = this._trigger('change', {\n          property: {\n            name: 'position',\n            value: position\n          }\n        });\n        // if (event.data !== undefined) {\n        // \tposition = this.normalize(event.data);\n        // }\n        this._current = position;\n        this.invalidate('position');\n        this._trigger('changed', {\n          property: {\n            name: 'position',\n            value: this._current\n          }\n        });\n      }\n      return this._current;\n    }\n    /**\n     * Invalidates the given part of the update routine.\n     * @param part The part to invalidate.\n     * @returns The invalidated parts.\n     */\n    invalidate(part) {\n      if (typeof part === 'string') {\n        this._invalidated[part] = true;\n        if (this.is('valid')) {\n          this.leave('valid');\n        }\n      }\n      return Object.keys(this._invalidated);\n    }\n    /**\n     * Resets the absolute position of the current item.\n     * @param position the absolute position of the new item.\n     */\n    reset(position) {\n      position = this.normalize(position);\n      if (position === undefined) {\n        return;\n      }\n      this._speed = 0;\n      this._current = position;\n      this._suppress(['translate', 'translated']);\n      this.animate(this.coordinates(position));\n      this._release(['translate', 'translated']);\n    }\n    /**\n     * Normalizes an absolute or a relative position of an item.\n     * @param position The absolute or relative position to normalize.\n     * @param relative Whether the given position is relative or not.\n     * @returns The normalized position.\n     */\n    normalize(position, relative) {\n      const n = this._items.length,\n        m = relative ? 0 : this._clones.length;\n      if (!this._isNumeric(position) || n < 1) {\n        position = undefined;\n      } else if (position < 0 || position >= n + m) {\n        position = ((position - m / 2) % n + n) % n + m / 2;\n      }\n      return position;\n    }\n    /**\n     * Converts an absolute position of an item into a relative one.\n     * @param position The absolute position to convert.\n     * @returns The converted position.\n     */\n    relative(position) {\n      position -= this._clones.length / 2;\n      return this.normalize(position, true);\n    }\n    /**\n     * Gets the maximum position for the current item.\n     * @param relative Whether to return an absolute position or a relative position.\n     * @returns number of maximum position\n     */\n    maximum(relative = false) {\n      const settings = this.settings;\n      let maximum = this._coordinates.length,\n        iterator,\n        reciprocalItemsWidth,\n        elementWidth;\n      if (settings.loop) {\n        maximum = this._clones.length / 2 + this._items.length - 1;\n      } else if (settings.autoWidth || settings.merge) {\n        iterator = this._items.length;\n        reciprocalItemsWidth = this.slidesData[--iterator].width;\n        elementWidth = this._width;\n        while (iterator-- > 0) {\n          // it could be use this._items instead of this.slidesData;\n          reciprocalItemsWidth += +this.slidesData[iterator].width + this.settings.margin;\n          if (reciprocalItemsWidth > elementWidth) {\n            break;\n          }\n        }\n        maximum = iterator + 1;\n      } else if (settings.center) {\n        maximum = this._items.length - 1;\n      } else {\n        maximum = this._items.length - settings.items;\n      }\n      if (relative) {\n        maximum -= this._clones.length / 2;\n      }\n      return Math.max(maximum, 0);\n    }\n    /**\n     * Gets the minimum position for the current item.\n     * @param relative Whether to return an absolute position or a relative position.\n     * @returns number of minimum position\n     */\n    minimum(relative = false) {\n      return relative ? 0 : this._clones.length / 2;\n    }\n    /**\n     * Gets an item at the specified relative position.\n     * @param position The relative position of the item.\n     * @returns The item at the given position or all items if no position was given.\n     */\n    items(position) {\n      if (position === undefined) {\n        return this._items.slice();\n      }\n      position = this.normalize(position, true);\n      return [this._items[position]];\n    }\n    /**\n     * Gets an item at the specified relative position.\n     * @param position The relative position of the item.\n     * @returns The item at the given position or all items if no position was given.\n     */\n    mergers(position) {\n      if (position === undefined) {\n        return this._mergers.slice();\n      }\n      position = this.normalize(position, true);\n      return this._mergers[position];\n    }\n    /**\n     * Gets the absolute positions of clones for an item.\n     * @param position The relative position of the item.\n     * @returns The absolute positions of clones for the item or all if no position was given.\n     */\n    clones(position) {\n      const odd = this._clones.length / 2,\n        even = odd + this._items.length,\n        map = index => index % 2 === 0 ? even + index / 2 : odd - (index + 1) / 2;\n      if (position === undefined) {\n        return this._clones.map((v, i) => map(i));\n      }\n      return this._clones.map((v, i) => v === position ? map(i) : null).filter(item => item);\n    }\n    /**\n     * Sets the current animation speed.\n     * @param speed The animation speed in milliseconds or nothing to leave it unchanged.\n     * @returns The current animation speed in milliseconds.\n     */\n    speed(speed) {\n      if (speed !== undefined) {\n        this._speed = speed;\n      }\n      return this._speed;\n    }\n    /**\n     * Gets the coordinate of an item.\n     * @todo The name of this method is missleanding.\n     * @param position The absolute position of the item within `minimum()` and `maximum()`.\n     * @returns The coordinate of the item in pixel or all coordinates.\n     */\n    coordinates(position) {\n      let multiplier = 1,\n        newPosition = position - 1,\n        coordinate,\n        result;\n      if (position === undefined) {\n        result = this._coordinates.map((item, index) => {\n          return this.coordinates(index);\n        });\n        return result;\n      }\n      if (this.settings.center) {\n        if (this.settings.rtl) {\n          multiplier = -1;\n          newPosition = position + 1;\n        }\n        coordinate = this._coordinates[position];\n        coordinate += (this.width() - coordinate + (this._coordinates[newPosition] || 0)) / 2 * multiplier;\n      } else {\n        coordinate = this._coordinates[newPosition] || 0;\n      }\n      coordinate = Math.ceil(coordinate);\n      return coordinate;\n    }\n    /**\n     * Calculates the speed for a translation.\n     * @param from The absolute position of the start item.\n     * @param to The absolute position of the target item.\n     * @param factor [factor=undefined] - The time factor in milliseconds.\n     * @returns The time in milliseconds for the translation.\n     */\n    _duration(from, to, factor) {\n      if (factor === 0) {\n        return 0;\n      }\n      return Math.min(Math.max(Math.abs(to - from), 1), 6) * Math.abs(+factor || this.settings.smartSpeed);\n    }\n    /**\n     * Slides to the specified item.\n     * @param position The position of the item.\n     * @param speed The time in milliseconds for the transition.\n     */\n    to(position, speed) {\n      let current = this.current(),\n        revert = null,\n        distance = position - this.relative(current),\n        maximum = this.maximum(),\n        delayForLoop = 0;\n      const direction = +(distance > 0) - +(distance < 0),\n        items = this._items.length,\n        minimum = this.minimum();\n      if (this.settings.loop) {\n        if (!this.settings.rewind && Math.abs(distance) > items / 2) {\n          distance += direction * -1 * items;\n        }\n        position = current + distance;\n        revert = ((position - minimum) % items + items) % items + minimum;\n        if (revert !== position && revert - distance <= maximum && revert - distance > 0) {\n          current = revert - distance;\n          position = revert;\n          delayForLoop = 30;\n          this.reset(current);\n          this.sendChanges();\n        }\n      } else if (this.settings.rewind) {\n        maximum += 1;\n        position = (position % maximum + maximum) % maximum;\n      } else {\n        position = Math.max(minimum, Math.min(maximum, position));\n      }\n      setTimeout(() => {\n        this.speed(this._duration(current, position, speed));\n        this.current(position);\n        this.update();\n      }, delayForLoop);\n    }\n    /**\n     * Slides to the next item.\n     * @param speed The time in milliseconds for the transition.\n     */\n    next(speed) {\n      speed = speed || false;\n      this.to(this.relative(this.current()) + 1, speed);\n    }\n    /**\n     * Slides to the previous item.\n     * @param speed The time in milliseconds for the transition.\n     */\n    prev(speed) {\n      speed = speed || false;\n      this.to(this.relative(this.current()) - 1, speed);\n    }\n    /**\n     * Handles the end of an animation.\n     * @param event - The event arguments.\n     */\n    onTransitionEnd(event) {\n      // if css2 animation then event object is undefined\n      if (event !== undefined) {\n        // event.stopPropagation();\n        // // Catch only owl-stage transitionEnd event\n        // if ((event.target || event.srcElement || event.originalTarget) !== this.$stage.get(0)\t) {\n        // \treturn false;\n        // }\n        return false;\n      }\n      this.leave('animating');\n      this._trigger('translated');\n    }\n    /**\n     * Gets viewport width.\n     * @returns - The width in pixel.\n     */\n    _viewport() {\n      let width;\n      if (this._width) {\n        width = this._width;\n      } else {\n        this.logger.log('Can not detect viewport width.');\n      }\n      return width;\n    }\n    /**\n     * Sets _items\n     * @param content The list of slides put into CarouselSlideDirectives.\n     */\n    setItems(content) {\n      this._items = content;\n    }\n    /**\n     * Sets slidesData using this._items\n     */\n    _defineSlidesData() {\n      // Maybe creating and using loadMap would be better in LazyLoadService.\n      // Hovewer in that case when 'resize' event fires, prop 'load' of all slides will get 'false' and such state of prop will be seen by View during its updating. Accordingly the code will remove slides's content from DOM even if it was loaded before.\n      // Thus it would be needed to add that content into DOM again.\n      // In order to avoid additional removing/adding loaded slides's content we use loadMap here and set restore state of prop 'load' before the View will get it.\n      let loadMap;\n      if (this.slidesData && this.slidesData.length) {\n        loadMap = new Map();\n        this.slidesData.forEach(item => {\n          if (item.load) {\n            loadMap.set(item.id, item.load);\n          }\n        });\n      }\n      this.slidesData = this._items.map(slide => {\n        return {\n          id: `${slide.id}`,\n          isActive: false,\n          tplRef: slide.tplRef,\n          dataMerge: slide.dataMerge,\n          width: 0,\n          isCloned: false,\n          load: loadMap ? loadMap.get(slide.id) : false,\n          hashFragment: slide.dataHash\n        };\n      });\n    }\n    /**\n     * Sets current classes for slide\n     * @param slide Slide of carousel\n     * @returns object with names of css-classes which are keys and true/false values\n     */\n    setCurSlideClasses(slide) {\n      // CSS classes: added/removed per current state of component properties\n      const currentClasses = {\n        'active': slide.isActive,\n        'center': slide.isCentered,\n        'cloned': slide.isCloned,\n        'animated': slide.isAnimated,\n        'owl-animated-in': slide.isDefAnimatedIn,\n        'owl-animated-out': slide.isDefAnimatedOut\n      };\n      if (this.settings.animateIn) {\n        currentClasses[this.settings.animateIn] = slide.isCustomAnimatedIn;\n      }\n      if (this.settings.animateOut) {\n        currentClasses[this.settings.animateOut] = slide.isCustomAnimatedOut;\n      }\n      return currentClasses;\n    }\n    /**\n     * Operators to calculate right-to-left and left-to-right.\n     * @param a - The left side operand.\n     * @param o - The operator.\n     * @param b - The right side operand.\n     * @returns true/false meaning right-to-left or left-to-right\n     */\n    _op(a, o, b) {\n      const rtl = this.settings.rtl;\n      switch (o) {\n        case '<':\n          return rtl ? a > b : a < b;\n        case '>':\n          return rtl ? a < b : a > b;\n        case '>=':\n          return rtl ? a <= b : a >= b;\n        case '<=':\n          return rtl ? a >= b : a <= b;\n        default:\n          break;\n      }\n    }\n    /**\n     * Triggers a public event.\n     * @todo Remove `status`, `relatedTarget` should be used instead.\n     * @param name The event name.\n     * @param data The event data.\n     * @param namespace The event namespace.\n     * @param state The state which is associated with the event.\n     * @param enter Indicates if the call enters the specified state or not.\n     */\n    _trigger(name, data, namespace, state, enter) {\n      switch (name) {\n        case 'initialized':\n          this._initializedCarousel$.next(name);\n          break;\n        case 'change':\n          this._changeSettingsCarousel$.next(data);\n          break;\n        case 'changed':\n          this._changedSettingsCarousel$.next(data);\n          break;\n        case 'drag':\n          this._dragCarousel$.next(name);\n          break;\n        case 'dragged':\n          this._draggedCarousel$.next(name);\n          break;\n        case 'resize':\n          this._resizeCarousel$.next(name);\n          break;\n        case 'resized':\n          this._resizedCarousel$.next(name);\n          break;\n        case 'refresh':\n          this._refreshCarousel$.next(name);\n          break;\n        case 'refreshed':\n          this._refreshedCarousel$.next(name);\n          break;\n        case 'translate':\n          this._translateCarousel$.next(name);\n          break;\n        case 'translated':\n          this._translatedCarousel$.next(name);\n          break;\n        default:\n          break;\n      }\n    }\n    /**\n     * Enters a state.\n     * @param name - The state name.\n     */\n    enter(name) {\n      [name].concat(this._states.tags[name] || []).forEach(stateName => {\n        if (this._states.current[stateName] === undefined) {\n          this._states.current[stateName] = 0;\n        }\n        this._states.current[stateName]++;\n      });\n    }\n    /**\n     * Leaves a state.\n     * @param name - The state name.\n     */\n    leave(name) {\n      [name].concat(this._states.tags[name] || []).forEach(stateName => {\n        if (this._states.current[stateName] === 0 || !!this._states.current[stateName]) {\n          this._states.current[stateName]--;\n        }\n      });\n    }\n    /**\n     * Registers an event or state.\n     * @param object - The event or state to register.\n     */\n    register(object) {\n      if (object.type === Type.State) {\n        if (!this._states.tags[object.name]) {\n          this._states.tags[object.name] = object.tags;\n        } else {\n          this._states.tags[object.name] = this._states.tags[object.name].concat(object.tags);\n        }\n        this._states.tags[object.name] = this._states.tags[object.name].filter((tag, i) => {\n          return this._states.tags[object.name].indexOf(tag) === i;\n        });\n      }\n    }\n    /**\n     * Suppresses events.\n     * @param events The events to suppress.\n     */\n    _suppress(events) {\n      events.forEach(event => {\n        this._supress[event] = true;\n      });\n    }\n    /**\n     * Releases suppressed events.\n     * @param events The events to release.\n     */\n    _release(events) {\n      events.forEach(event => {\n        delete this._supress[event];\n      });\n    }\n    /**\n     * Gets unified pointer coordinates from event.\n     * @todo #261\n     * @param event The `mousedown` or `touchstart` event.\n     * @returns Object Coords which contains `x` and `y` coordinates of current pointer position.\n     */\n    pointer(event) {\n      const result = {\n        x: null,\n        y: null\n      };\n      event = event.originalEvent || event || window.event;\n      event = event.touches && event.touches.length ? event.touches[0] : event.changedTouches && event.changedTouches.length ? event.changedTouches[0] : event;\n      if (event.pageX) {\n        result.x = event.pageX;\n        result.y = event.pageY;\n      } else {\n        result.x = event.clientX;\n        result.y = event.clientY;\n      }\n      return result;\n    }\n    /**\n     * Determines if the input is a Number or something that can be coerced to a Number\n     * @param number The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number\n     */\n    _isNumeric(number) {\n      return !isNaN(parseFloat(number));\n    }\n    /**\n     * Determines whether value is number or boolean type\n     * @param value The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number, or Boolean\n     */\n    _isNumberOrBoolean(value) {\n      return this._isNumeric(value) || typeof value === 'boolean';\n    }\n    /**\n     * Determines whether value is number or string type\n     * @param value The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number, or String\n     */\n    _isNumberOrString(value) {\n      return this._isNumeric(value) || typeof value === 'string';\n    }\n    /**\n     * Determines whether value is number or string type\n     * @param value The input to be tested\n     * @returns An indication if the input is a Number or can be coerced to a Number, or String\n     */\n    _isStringOrBoolean(value) {\n      return typeof value === 'string' || typeof value === 'boolean';\n    }\n    /**\n     * Gets the difference of two vectors.\n     * @todo #261\n     * @param first The first vector.\n     * @param second The second vector.\n     * @returns The difference.\n     */\n    difference(first, second) {\n      if (null === first || null === second) {\n        return {\n          x: 0,\n          y: 0\n        };\n      }\n      return {\n        x: first.x - second.x,\n        y: first.y - second.y\n      };\n    }\n    static ɵfac = function CarouselService_Factory(t) {\n      return new (t || CarouselService)(i0.ɵɵinject(OwlLogger));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CarouselService,\n      factory: CarouselService.ɵfac\n    });\n  }\n  return CarouselService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NavigationService = /*#__PURE__*/(() => {\n  class NavigationService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    navSubscription;\n    /**\n     * Indicates whether the plugin is initialized or not.\n     */\n    _initialized = false;\n    /**\n     * The current paging indexes.\n     */\n    _pages = [];\n    /**\n     * Data for navigation elements of the user interface.\n     */\n    _navData = {\n      disabled: false,\n      prev: {\n        disabled: false,\n        htmlText: ''\n      },\n      next: {\n        disabled: false,\n        htmlText: ''\n      }\n    };\n    /**\n     * Data for dot elements of the user interface.\n     */\n    _dotsData = {\n      disabled: false,\n      dots: []\n    };\n    constructor(carouselService) {\n      this.carouselService = carouselService;\n      this.spyDataStreams();\n    }\n    ngOnDestroy() {\n      this.navSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n      const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(state => {\n        this.initialize();\n        this._updateNavPages();\n        this.draw();\n        this.update();\n        this.carouselService.sendChanges();\n      }));\n      // mostly changes in carouselService and carousel at all causes carouselService.to(). It moves stage right-left by its code and calling needed functions\n      // Thus this method by calling carouselService.current(position) notifies about changes\n      const changedSettings$ = this.carouselService.getChangedState().pipe(filter(data => data.property.name === 'position'), tap(data => {\n        this.update();\n        // should be the call of the function written at the end of comment\n        // but the method carouselServive.to() has setTimeout(f, 0) which contains carouselServive.update() which calls sendChanges() method.\n        // carouselService.navData and carouselService.dotsData update earlier than carouselServive.update() gets called\n        // updates of carouselService.navData and carouselService.dotsData are being happening withing carouselService.current(position) method which calls next() of _changedSettingsCarousel$\n        // carouselService.current(position) is being calling earlier than carouselServive.update();\n        // this.carouselService.sendChanges();\n      }));\n      const refreshedCarousel$ = this.carouselService.getRefreshedState().pipe(tap(() => {\n        this._updateNavPages();\n        this.draw();\n        this.update();\n        this.carouselService.sendChanges();\n      }));\n      const navMerge$ = merge(initializedCarousel$, changedSettings$, refreshedCarousel$);\n      this.navSubscription = navMerge$.subscribe(() => {});\n    }\n    /**\n       * Initializes the layout of the plugin and extends the carousel.\n       */\n    initialize() {\n      this._navData.disabled = true;\n      this._navData.prev.htmlText = this.carouselService.settings.navText[0];\n      this._navData.next.htmlText = this.carouselService.settings.navText[1];\n      this._dotsData.disabled = true;\n      this.carouselService.navData = this._navData;\n      this.carouselService.dotsData = this._dotsData;\n    }\n    /**\n     * Calculates internal states and updates prop _pages\n     */\n    _updateNavPages() {\n      let i, j, k;\n      const lower = this.carouselService.clones().length / 2,\n        upper = lower + this.carouselService.items().length,\n        maximum = this.carouselService.maximum(true),\n        pages = [],\n        settings = this.carouselService.settings;\n      let size = settings.center || settings.autoWidth || settings.dotsData ? 1 : Math.floor(Number(settings.dotsEach)) || Math.floor(settings.items);\n      size = +size;\n      if (settings.slideBy !== 'page') {\n        settings.slideBy = Math.min(+settings.slideBy, settings.items);\n      }\n      if (settings.dots || settings.slideBy === 'page') {\n        for (i = lower, j = 0, k = 0; i < upper; i++) {\n          if (j >= size || j === 0) {\n            pages.push({\n              start: Math.min(maximum, i - lower),\n              end: i - lower + size - 1\n            });\n            if (Math.min(maximum, i - lower) === maximum) {\n              break;\n            }\n            j = 0, ++k;\n          }\n          j += this.carouselService.mergers(this.carouselService.relative(i));\n        }\n      }\n      this._pages = pages;\n    }\n    /**\n       * Draws the user interface.\n       * @todo The option `dotsData` wont work.\n       */\n    draw() {\n      let difference;\n      const settings = this.carouselService.settings,\n        items = this.carouselService.items(),\n        disabled = items.length <= settings.items;\n      this._navData.disabled = !settings.nav || disabled;\n      this._dotsData.disabled = !settings.dots || disabled;\n      if (settings.dots) {\n        difference = this._pages.length - this._dotsData.dots.length;\n        if (settings.dotsData && difference !== 0) {\n          this._dotsData.dots = [];\n          items.forEach(item => {\n            this._dotsData.dots.push({\n              active: false,\n              id: `dot-${item.id}`,\n              innerContent: item.dotContent,\n              showInnerContent: true\n            });\n          });\n        } else if (difference > 0) {\n          const startI = this._dotsData.dots.length > 0 ? this._dotsData.dots.length : 0;\n          for (let i = 0; i < difference; i++) {\n            this._dotsData.dots.push({\n              active: false,\n              id: `dot-${i + startI}`,\n              innerContent: '',\n              showInnerContent: false\n            });\n          }\n        } else if (difference < 0) {\n          this._dotsData.dots.splice(difference, Math.abs(difference));\n        }\n      }\n      this.carouselService.navData = this._navData;\n      this.carouselService.dotsData = this._dotsData;\n    }\n    /**\n     * Updates navigation buttons's and dots's states\n     */\n    update() {\n      this._updateNavButtons();\n      this._updateDots();\n    }\n    /**\n     * Changes state of nav buttons (disabled, enabled)\n     */\n    _updateNavButtons() {\n      const settings = this.carouselService.settings,\n        loop = settings.loop || settings.rewind,\n        index = this.carouselService.relative(this.carouselService.current());\n      if (settings.nav) {\n        this._navData.prev.disabled = !loop && index <= this.carouselService.minimum(true);\n        this._navData.next.disabled = !loop && index >= this.carouselService.maximum(true);\n      }\n      this.carouselService.navData = this._navData;\n    }\n    /**\n     * Changes active dot if page becomes changed\n     */\n    _updateDots() {\n      let curActiveDotI;\n      if (!this.carouselService.settings.dots) {\n        return;\n      }\n      this._dotsData.dots.forEach(item => {\n        if (item.active === true) {\n          item.active = false;\n        }\n      });\n      curActiveDotI = this._current();\n      if (this._dotsData.dots.length) {\n        this._dotsData.dots[curActiveDotI].active = true;\n      }\n      this.carouselService.dotsData = this._dotsData;\n    }\n    /**\n       * Gets the current page position of the carousel.\n       * @returns the current page position of the carousel\n       */\n    _current() {\n      const current = this.carouselService.relative(this.carouselService.current());\n      let finalCurrent;\n      const pages = this._pages.filter((page, index) => {\n        return page.start <= current && page.end >= current;\n      }).pop();\n      finalCurrent = this._pages.findIndex(page => {\n        return page.start === pages.start && page.end === pages.end;\n      });\n      return finalCurrent;\n    }\n    /**\n       * Gets the current succesor/predecessor position.\n     * @param sussessor position of slide\n       * @returns the current succesor/predecessor position\n       */\n    _getPosition(successor) {\n      let position, length;\n      const settings = this.carouselService.settings;\n      if (settings.slideBy === 'page') {\n        position = this._current();\n        length = this._pages.length;\n        successor ? ++position : --position;\n        position = this._pages[(position % length + length) % length].start;\n      } else {\n        position = this.carouselService.relative(this.carouselService.current());\n        length = this.carouselService.items().length;\n        successor ? position += +settings.slideBy : position -= +settings.slideBy;\n      }\n      return position;\n    }\n    /**\n       * Slides to the next item or page.\n       * @param speed The time in milliseconds for the transition.\n       */\n    next(speed) {\n      this.carouselService.to(this._getPosition(true), speed);\n    }\n    /**\n     * Slides to the previous item or page.\n     * @param speed The time in milliseconds for the transition.\n     */\n    prev(speed) {\n      this.carouselService.to(this._getPosition(false), speed);\n    }\n    /**\n     * Slides to the specified item or page.\n     * @param position - The position of the item or page.\n     * @param speed - The time in milliseconds for the transition.\n     * @param standard - Whether to use the standard behaviour or not. Default meaning false\n     */\n    to(position, speed, standard) {\n      let length;\n      if (!standard && this._pages.length) {\n        length = this._pages.length;\n        this.carouselService.to(this._pages[(position % length + length) % length].start, speed);\n      } else {\n        this.carouselService.to(position, speed);\n      }\n    }\n    /**\n     * Moves carousel after user's clicking on any dots\n     */\n    moveByDot(dotId) {\n      const index = this._dotsData.dots.findIndex(dot => dotId === dot.id);\n      this.to(index, this.carouselService.settings.dotsSpeed);\n    }\n    /**\n     * rewinds carousel to slide with needed id\n     * @param id id of slide\n     */\n    toSlideById(id) {\n      const position = this.carouselService.slidesData.findIndex(slide => slide.id === id && slide.isCloned === false);\n      if (position === -1 || position === this.carouselService.current()) {\n        return;\n      }\n      this.carouselService.to(this.carouselService.relative(position), false);\n    }\n    static ɵfac = function NavigationService_Factory(t) {\n      return new (t || NavigationService)(i0.ɵɵinject(CarouselService));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NavigationService,\n      factory: NavigationService.ɵfac\n    });\n  }\n  return NavigationService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// import { Injectable } from '@angular/core';\n// function _window(): any {\n//    // return the global native browser window object\n//    return window;\n// }\n// @Injectable()\n// export class WindowRefService {\n//    get nativeWindow(): any {\n//       return _window();\n//    }\n// }\n/**\n * Create a new injection token for injecting the window into a component.\n */\nconst WINDOW = new InjectionToken('WindowToken');\n/**\n * Define abstract class for obtaining reference to the global window object.\n */\nclass WindowRef {\n  get nativeWindow() {\n    throw new Error('Not implemented.');\n  }\n}\n/**\n * Define class that implements the abstract class and returns the native window object.\n */\nlet BrowserWindowRef = /*#__PURE__*/(() => {\n  class BrowserWindowRef extends WindowRef {\n    constructor() {\n      super();\n    }\n    /**\n     * @returns window object\n     */\n    get nativeWindow() {\n      return window;\n    }\n    static ɵfac = function BrowserWindowRef_Factory(t) {\n      return new (t || BrowserWindowRef)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserWindowRef,\n      factory: BrowserWindowRef.ɵfac\n    });\n  }\n  return BrowserWindowRef;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Create an factory function that returns the native window object.\n * @param browserWindowRef Native window object\n * @param platformId id of platform\n * @returns type of platform of empty object\n */\nfunction windowFactory(browserWindowRef, platformId) {\n  if (isPlatformBrowser(platformId)) {\n    return browserWindowRef.nativeWindow;\n  }\n  const obj = {\n    setTimeout: (func, time) => {},\n    clearTimeout: a => {}\n  };\n  return obj;\n}\n/**\n * Create a injectable provider for the WindowRef token that uses the BrowserWindowRef class.\n */\nconst browserWindowProvider = {\n  provide: WindowRef,\n  useClass: BrowserWindowRef\n};\n/**\n * Create an injectable provider that uses the windowFactory function for returning the native window object.\n */\nconst windowProvider = {\n  provide: WINDOW,\n  useFactory: windowFactory,\n  deps: [WindowRef, PLATFORM_ID]\n};\n/**\n * Create an array of providers.\n */\nconst WINDOW_PROVIDERS = [browserWindowProvider, windowProvider];\n\n/**\n * Create a new injection token for injecting the Document into a component.\n */\nconst DOCUMENT = new InjectionToken('DocumentToken');\n/**\n * Define abstract class for obtaining reference to the global Document object.\n */\nclass DocumentRef {\n  get nativeDocument() {\n    throw new Error('Not implemented.');\n  }\n}\n/**\n * Define class that implements the abstract class and returns the native Document object.\n */\nlet BrowserDocumentRef = /*#__PURE__*/(() => {\n  class BrowserDocumentRef extends DocumentRef {\n    constructor() {\n      super();\n    }\n    /**\n     * @returns Document object\n     */\n    get nativeDocument() {\n      return document;\n    }\n    static ɵfac = function BrowserDocumentRef_Factory(t) {\n      return new (t || BrowserDocumentRef)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserDocumentRef,\n      factory: BrowserDocumentRef.ɵfac\n    });\n  }\n  return BrowserDocumentRef;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Create an factory function that returns the native Document object.\n * @param browserDocumentRef Native Document object\n * @param platformId id of platform\n * @returns type of platform of empty object\n */\nfunction documentFactory(browserDocumentRef, platformId) {\n  if (isPlatformBrowser(platformId)) {\n    return browserDocumentRef.nativeDocument;\n  }\n  const doc = {\n    hidden: false,\n    visibilityState: 'visible'\n  };\n  return doc;\n}\n/**\n * Create a injectable provider for the DocumentRef token that uses the BrowserDocumentRef class.\n */\nconst browserDocumentProvider = {\n  provide: DocumentRef,\n  useClass: BrowserDocumentRef\n};\n/**\n * Create an injectable provider that uses the DocumentFactory function for returning the native Document object.\n */\nconst documentProvider = {\n  provide: DOCUMENT,\n  useFactory: documentFactory,\n  deps: [DocumentRef, PLATFORM_ID]\n};\n/**\n * Create an array of providers.\n */\nconst DOCUMENT_PROVIDERS = [browserDocumentProvider, documentProvider];\nlet AutoplayService = /*#__PURE__*/(() => {\n  class AutoplayService {\n    carouselService;\n    ngZone;\n    /**\n     * Subscrioption to merge Observables from CarouselService\n     */\n    autoplaySubscription;\n    /**\n     * The autoplay timeout.\n     */\n    _timeout = null;\n    /**\n     * Indicates whenever the autoplay is paused.\n     */\n    _paused = false;\n    /**\n     * Shows whether the code (the plugin) changed the option 'AutoplayTimeout' for own needs\n     */\n    _isArtificialAutoplayTimeout;\n    /**\n     * Shows whether the autoplay is paused for unlimited time by the developer.\n     * Use to prevent autoplaying in case of firing `mouseleave` by adding layers to `<body>` like `mat-menu` does\n     */\n    _isAutoplayStopped = false;\n    get isAutoplayStopped() {\n      return this._isAutoplayStopped;\n    }\n    set isAutoplayStopped(value) {\n      this._isAutoplayStopped = value;\n    }\n    winRef;\n    docRef;\n    constructor(carouselService, winRef, docRef, ngZone) {\n      this.carouselService = carouselService;\n      this.ngZone = ngZone;\n      this.winRef = winRef;\n      this.docRef = docRef;\n      this.spyDataStreams();\n    }\n    ngOnDestroy() {\n      this.autoplaySubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n      const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n        if (this.carouselService.settings.autoplay) {\n          this.play();\n        }\n      }));\n      const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n        this._handleChangeObservable(data);\n      }));\n      const resized$ = this.carouselService.getResizedState().pipe(tap(() => {\n        if (this.carouselService.settings.autoplay && !this._isAutoplayStopped) {\n          this.play();\n        } else {\n          this.stop();\n        }\n      }));\n      // original Autoplay Plugin has listeners on play.owl.core and stop.owl.core events.\n      // They are triggered by Video Plugin\n      const autoplayMerge$ = merge(initializedCarousel$, changedSettings$, resized$);\n      this.autoplaySubscription = autoplayMerge$.subscribe(() => {});\n    }\n    /**\n       * Starts the autoplay.\n       * @param timeout The interval before the next animation starts.\n       * @param speed The animation speed for the animations.\n       */\n    play(timeout, speed) {\n      if (this._paused) {\n        this._paused = false;\n        this._setAutoPlayInterval(this.carouselService.settings.autoplayMouseleaveTimeout);\n      }\n      if (this.carouselService.is('rotating')) {\n        return;\n      }\n      this.carouselService.enter('rotating');\n      this._setAutoPlayInterval();\n    }\n    /**\n       * Gets a new timeout\n       * @param timeout - The interval before the next animation starts.\n       * @param speed - The animation speed for the animations.\n       * @return\n       */\n    _getNextTimeout(timeout, speed) {\n      if (this._timeout) {\n        this.winRef.clearTimeout(this._timeout);\n      }\n      this._isArtificialAutoplayTimeout = timeout ? true : false;\n      return this.ngZone.runOutsideAngular(() => {\n        return this.winRef.setTimeout(() => {\n          this.ngZone.run(() => {\n            if (this._paused || this.carouselService.is('busy') || this.carouselService.is('interacting') || this.docRef.hidden) {\n              return;\n            }\n            this.carouselService.next(speed || this.carouselService.settings.autoplaySpeed);\n          });\n        }, timeout || this.carouselService.settings.autoplayTimeout);\n      });\n    }\n    /**\n       * Sets autoplay in motion.\n       */\n    _setAutoPlayInterval(timeout) {\n      this._timeout = this._getNextTimeout(timeout);\n    }\n    /**\n     * Stops the autoplay.\n     */\n    stop() {\n      if (!this.carouselService.is('rotating')) {\n        return;\n      }\n      this._paused = true;\n      this.winRef.clearTimeout(this._timeout);\n      this.carouselService.leave('rotating');\n    }\n    /**\n       * Stops the autoplay.\n       */\n    pause() {\n      if (!this.carouselService.is('rotating')) {\n        return;\n      }\n      this._paused = true;\n    }\n    /**\n     * Manages by autoplaying according to data passed by _changedSettingsCarousel$ Obsarvable\n     * @param data object with current position of carousel and type of change\n     */\n    _handleChangeObservable(data) {\n      if (data.property.name === 'settings') {\n        if (this.carouselService.settings.autoplay) {\n          this.play();\n        } else {\n          this.stop();\n        }\n      } else if (data.property.name === 'position') {\n        //console.log('play?', e);\n        if (this.carouselService.settings.autoplay) {\n          this._setAutoPlayInterval();\n        }\n      }\n    }\n    /**\n     * Starts autoplaying of the carousel in the case when user leaves the carousel before it starts translateing (moving)\n     */\n    _playAfterTranslated() {\n      of('translated').pipe(switchMap(data => this.carouselService.getTranslatedState()), first(), filter(() => this._isArtificialAutoplayTimeout), tap(() => this._setAutoPlayInterval())).subscribe(() => {});\n    }\n    /**\n     * Starts pausing\n     */\n    startPausing() {\n      if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n        this.pause();\n      }\n    }\n    /**\n     * Starts playing after mouse leaves carousel\n     */\n    startPlayingMouseLeave() {\n      if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n        this.play();\n        this._playAfterTranslated();\n      }\n    }\n    /**\n     * Starts playing after touch ends\n     */\n    startPlayingTouchEnd() {\n      if (this.carouselService.settings.autoplayHoverPause && this.carouselService.is('rotating')) {\n        this.play();\n        this._playAfterTranslated();\n      }\n    }\n    static ɵfac = function AutoplayService_Factory(t) {\n      return new (t || AutoplayService)(i0.ɵɵinject(CarouselService), i0.ɵɵinject(WINDOW), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutoplayService,\n      factory: AutoplayService.ɵfac\n    });\n  }\n  return AutoplayService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet LazyLoadService = /*#__PURE__*/(() => {\n  class LazyLoadService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    lazyLoadSubscription;\n    constructor(carouselService) {\n      this.carouselService = carouselService;\n      this.spyDataStreams();\n    }\n    ngOnDestroy() {\n      this.lazyLoadSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n      const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n        const isLazyLoad = this.carouselService.settings && !this.carouselService.settings.lazyLoad;\n        this.carouselService.slidesData.forEach(item => item.load = isLazyLoad ? true : false);\n      }));\n      const changeSettings$ = this.carouselService.getChangeState();\n      const resizedCarousel$ = this.carouselService.getResizedState();\n      const lazyLoadMerge$ = merge(initializedCarousel$, changeSettings$, resizedCarousel$).pipe(tap(data => this._defineLazyLoadSlides(data)));\n      this.lazyLoadSubscription = lazyLoadMerge$.subscribe(() => {});\n    }\n    _defineLazyLoadSlides(data) {\n      if (!this.carouselService.settings || !this.carouselService.settings.lazyLoad) {\n        return;\n      }\n      if (data.property && data.property.name === 'position' || data === 'initialized' || data === \"resized\") {\n        const settings = this.carouselService.settings,\n          clones = this.carouselService.clones().length;\n        let n = settings.center && Math.ceil(settings.items / 2) || settings.items,\n          i = settings.center && n * -1 || 0,\n          position = (data.property && data.property.value !== undefined ? data.property.value : this.carouselService.current()) + i;\n        // load = $.proxy(function(i, v) { this.load(v) }, this);\n        //TODO: Need documentation for this new option\n        if (settings.lazyLoadEager > 0) {\n          n += settings.lazyLoadEager;\n          // If the carousel is looping also preload images that are to the \"left\"\n          if (settings.loop) {\n            position -= settings.lazyLoadEager;\n            n++;\n          }\n        }\n        while (i++ < n) {\n          this._load(clones / 2 + this.carouselService.relative(position));\n          if (clones) {\n            this.carouselService.clones(this.carouselService.relative(position)).forEach(value => this._load(value));\n          }\n          position++;\n        }\n      }\n    }\n    /**\n       * Loads all resources of an item at the specified position.\n       * @param position - The absolute position of the item.\n       */\n    _load(position) {\n      if (this.carouselService.slidesData[position].load) {\n        return;\n      }\n      this.carouselService.slidesData[position].load = true;\n    }\n    static ɵfac = function LazyLoadService_Factory(t) {\n      return new (t || LazyLoadService)(i0.ɵɵinject(CarouselService));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LazyLoadService,\n      factory: LazyLoadService.ɵfac\n    });\n  }\n  return LazyLoadService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AnimateService = /*#__PURE__*/(() => {\n  class AnimateService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    animateSubscription;\n    /**\n     * s\n     */\n    swapping = true;\n    /**\n     * active slide before translating\n     */\n    previous = undefined;\n    /**\n     * new active slide after translating\n     */\n    next = undefined;\n    constructor(carouselService) {\n      this.carouselService = carouselService;\n      this.spyDataStreams();\n    }\n    ngOnDestroy() {\n      this.animateSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n      const changeSettings$ = this.carouselService.getChangeState().pipe(tap(data => {\n        if (data.property.name === 'position') {\n          this.previous = this.carouselService.current();\n          this.next = data.property.value;\n        }\n      }));\n      const dragCarousel$ = this.carouselService.getDragState();\n      const draggedCarousel$ = this.carouselService.getDraggedState();\n      const translatedCarousel$ = this.carouselService.getTranslatedState();\n      const dragTranslatedMerge$ = merge(dragCarousel$, draggedCarousel$, translatedCarousel$).pipe(tap(data => this.swapping = data === 'translated'));\n      const translateCarousel$ = this.carouselService.getTranslateState().pipe(tap(data => {\n        if (this.swapping && (this.carouselService._options.animateOut || this.carouselService._options.animateIn)) {\n          this._swap();\n        }\n      }));\n      const animateMerge$ = merge(changeSettings$, translateCarousel$, dragTranslatedMerge$).pipe();\n      this.animateSubscription = animateMerge$.subscribe(() => {});\n    }\n    /**\n       * Toggles the animation classes whenever an translations starts.\n       * @returns\n       */\n    _swap() {\n      if (this.carouselService.settings.items !== 1) {\n        return;\n      }\n      // if (!$.support.animation || !$.support.transition) {\n      // \treturn;\n      // }\n      this.carouselService.speed(0);\n      let left;\n      const previous = this.carouselService.slidesData[this.previous],\n        next = this.carouselService.slidesData[this.next],\n        incoming = this.carouselService.settings.animateIn,\n        outgoing = this.carouselService.settings.animateOut;\n      if (this.carouselService.current() === this.previous) {\n        return;\n      }\n      if (outgoing) {\n        left = +this.carouselService.coordinates(this.previous) - +this.carouselService.coordinates(this.next);\n        this.carouselService.slidesData.forEach(slide => {\n          if (slide.id === previous.id) {\n            slide.left = `${left}px`;\n            slide.isAnimated = true;\n            slide.isDefAnimatedOut = true;\n            slide.isCustomAnimatedOut = true;\n          }\n        });\n      }\n      if (incoming) {\n        this.carouselService.slidesData.forEach(slide => {\n          if (slide.id === next.id) {\n            slide.isAnimated = true;\n            slide.isDefAnimatedIn = true;\n            slide.isCustomAnimatedIn = true;\n          }\n        });\n      }\n    }\n    /**\n     * Handles the end of 'animationend' event\n     * @param id Id of slides\n     */\n    clear(id) {\n      this.carouselService.slidesData.forEach(slide => {\n        if (slide.id === id) {\n          slide.left = '';\n          slide.isAnimated = false;\n          slide.isDefAnimatedOut = false;\n          slide.isCustomAnimatedOut = false;\n          slide.isDefAnimatedIn = false;\n          slide.isCustomAnimatedIn = false;\n          slide.classes = this.carouselService.setCurSlideClasses(slide);\n        }\n      });\n      this.carouselService.onTransitionEnd();\n    }\n    static ɵfac = function AnimateService_Factory(t) {\n      return new (t || AnimateService)(i0.ɵɵinject(CarouselService));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AnimateService,\n      factory: AnimateService.ɵfac\n    });\n  }\n  return AnimateService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AutoHeightService = /*#__PURE__*/(() => {\n  class AutoHeightService {\n    carouselService;\n    /**\n     * Subscrioption to merge Observable  from CarouselService\n     */\n    autoHeightSubscription;\n    constructor(carouselService) {\n      this.carouselService = carouselService;\n      this.spyDataStreams();\n    }\n    ngOnDestroy() {\n      this.autoHeightSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n      const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(data => {\n        if (this.carouselService.settings.autoHeight) {\n          this.update();\n        } else {\n          this.carouselService.slidesData.forEach(slide => slide.heightState = 'full');\n        }\n      }));\n      const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n        if (this.carouselService.settings.autoHeight && data.property.name === 'position') {\n          this.update();\n        }\n      }));\n      const refreshedCarousel$ = this.carouselService.getRefreshedState().pipe(tap(data => {\n        if (this.carouselService.settings.autoHeight) {\n          this.update();\n        }\n      }));\n      const autoHeight$ = merge(initializedCarousel$, changedSettings$, refreshedCarousel$);\n      this.autoHeightSubscription = autoHeight$.subscribe(() => {});\n    }\n    /**\n     * Updates the prop 'heightState' of slides\n     */\n    update() {\n      const items = this.carouselService.settings.items;\n      let start = this.carouselService.current(),\n        end = start + items;\n      if (this.carouselService.settings.center) {\n        start = items % 2 === 1 ? start - (items - 1) / 2 : start - items / 2;\n        end = items % 2 === 1 ? start + items : start + items + 1;\n      }\n      this.carouselService.slidesData.forEach((slide, i) => {\n        slide.heightState = i >= start && i < end ? 'full' : 'nulled';\n      });\n    }\n    static ɵfac = function AutoHeightService_Factory(t) {\n      return new (t || AutoHeightService)(i0.ɵɵinject(CarouselService));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutoHeightService,\n      factory: AutoHeightService.ɵfac\n    });\n  }\n  return AutoHeightService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet HashService = /*#__PURE__*/(() => {\n  class HashService {\n    carouselService;\n    route;\n    router;\n    /**\n     * Subscription to merge Observable from CarouselService\n     */\n    hashSubscription;\n    /**\n     * Current url fragment (hash)\n     */\n    currentHashFragment;\n    constructor(carouselService, route, router) {\n      this.carouselService = carouselService;\n      this.route = route;\n      this.router = router;\n      this.spyDataStreams();\n      if (!this.route) {\n        this.route = {\n          fragment: of('no route').pipe(take(1))\n        };\n      }\n      ;\n      if (!this.router) {\n        this.router = {\n          navigate: (commands, extras) => {\n            return;\n          }\n        };\n      }\n    }\n    ngOnDestroy() {\n      this.hashSubscription.unsubscribe();\n    }\n    /**\n     * Defines Observables which service must observe\n     */\n    spyDataStreams() {\n      const initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => this.listenToRoute()));\n      const changedSettings$ = this.carouselService.getChangedState().pipe(tap(data => {\n        if (this.carouselService.settings.URLhashListener && data.property.name === 'position') {\n          const newCurSlide = this.carouselService.current();\n          const newCurFragment = this.carouselService.slidesData[newCurSlide].hashFragment;\n          if (!newCurFragment || newCurFragment === this.currentHashFragment) {\n            return;\n          }\n          this.router.navigate(['./'], {\n            fragment: newCurFragment,\n            relativeTo: this.route\n          });\n        }\n      }));\n      const hashFragment$ = merge(initializedCarousel$, changedSettings$);\n      this.hashSubscription = hashFragment$.subscribe(() => {});\n    }\n    /**\n     * rewinds carousel to slide which has the same hashFragment as fragment of current url\n     * @param fragment fragment of url\n     */\n    rewind(fragment) {\n      const position = this.carouselService.slidesData.findIndex(slide => slide.hashFragment === fragment && slide.isCloned === false);\n      if (position === -1 || position === this.carouselService.current()) {\n        return;\n      }\n      this.carouselService.to(this.carouselService.relative(position), false);\n    }\n    /**\n     * Initiate listening to ActivatedRoute.fragment\n     */\n    listenToRoute() {\n      const count = this.carouselService.settings.startPosition === 'URLHash' ? 0 : 2;\n      this.route.fragment.pipe(skip(count)).subscribe(fragment => {\n        this.currentHashFragment = fragment;\n        this.rewind(fragment);\n      });\n    }\n    static ɵfac = function HashService_Factory(t) {\n      return new (t || HashService)(i0.ɵɵinject(CarouselService), i0.ɵɵinject(i1.ActivatedRoute, 8), i0.ɵɵinject(i1.Router, 8));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HashService,\n      factory: HashService.ɵfac\n    });\n  }\n  return HashService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet nextId = 0;\nlet CarouselSlideDirective = /*#__PURE__*/(() => {\n  class CarouselSlideDirective {\n    tplRef;\n    /**\n     * Unique slide identifier. Must be unique for the entire document for proper accessibility support.\n     * Will be auto-generated if not provided.\n     */\n    id = `owl-slide-${nextId++}`;\n    /**\n     * Defines how much widths of common slide will current slide have\n     * e.g. if _mergeData=2, the slide will twice wider then slides with _mergeData=1\n     */\n    _dataMerge = 1;\n    set dataMerge(data) {\n      this._dataMerge = this.isNumeric(data) ? data : 1;\n    }\n    get dataMerge() {\n      return this._dataMerge;\n    }\n    /**\n     * Width of slide\n     */\n    width = 0;\n    /**\n     * Inner content of dot for certain slide; can be html-markup\n     */\n    dotContent = '';\n    /**\n     * Hash (fragment) of url which corresponds to certain slide\n     */\n    dataHash = '';\n    constructor(tplRef) {\n      this.tplRef = tplRef;\n    }\n    /**\n       * Determines if the input is a Number or something that can be coerced to a Number\n       * @param - The input to be tested\n       * @returns - An indication if the input is a Number or can be coerced to a Number\n       */\n    isNumeric(number) {\n      return !isNaN(parseFloat(number));\n    }\n    static ɵfac = function CarouselSlideDirective_Factory(t) {\n      return new (t || CarouselSlideDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CarouselSlideDirective,\n      selectors: [[\"ng-template\", \"carouselSlide\", \"\"]],\n      inputs: {\n        id: \"id\",\n        dataMerge: \"dataMerge\",\n        width: \"width\",\n        dotContent: \"dotContent\",\n        dataHash: \"dataHash\"\n      }\n    });\n  }\n  return CarouselSlideDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ResizeService = /*#__PURE__*/(() => {\n  class ResizeService {\n    resizeObservable$;\n    /**\n     * Makes resizeSubject become Observable\n     * @returns Observable of resizeSubject\n     */\n    get onResize$() {\n      return this.resizeObservable$;\n    }\n    constructor(winRef, platformId) {\n      this.resizeObservable$ = isPlatformBrowser(platformId) ? fromEvent(winRef, 'resize') : new Subject().asObservable();\n    }\n    static ɵfac = function ResizeService_Factory(t) {\n      return new (t || ResizeService)(i0.ɵɵinject(WINDOW), i0.ɵɵinject(PLATFORM_ID));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ResizeService,\n      factory: ResizeService.ɵfac\n    });\n  }\n  return ResizeService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet StageComponent = /*#__PURE__*/(() => {\n  class StageComponent {\n    zone;\n    el;\n    renderer;\n    carouselService;\n    animateService;\n    /**\n     * Object with settings which make carousel draggable by touch or mouse\n     */\n    owlDraggable;\n    /**\n     * Data of owl-stage\n     */\n    stageData;\n    /**\n     *  Data of every slide\n     */\n    slidesData;\n    /**\n     * Function wich will be returned after attaching listener to 'mousemove' event\n     */\n    listenerMouseMove;\n    /**\n     * Function wich will be returned after attaching listener to 'touchmove' event\n     */\n    listenerTouchMove;\n    /**\n     * Function wich will be returned after attaching listener to 'mousemove' event\n     */\n    listenerOneMouseMove;\n    /**\n     * Function wich will be returned after attaching listener to 'touchmove' event\n     */\n    listenerOneTouchMove;\n    /**\n     * Function wich will be returned after attaching listener to 'mouseup' event\n     */\n    listenerMouseUp;\n    /**\n     * Function wich will be returned after attaching listener to 'touchend' event\n     */\n    listenerTouchEnd;\n    /**\n     * Function wich will be returned after attaching listener to 'click' event\n     */\n    listenerOneClick;\n    listenerATag;\n    /**\n     * Object with data needed for dragging\n     */\n    _drag = {\n      time: null,\n      target: null,\n      pointer: null,\n      stage: {\n        start: null,\n        current: null\n      },\n      direction: null,\n      active: false,\n      moving: false\n    };\n    /**\n     * Subject for notification when the carousel's rebuilding caused by resize event starts\n     */\n    _oneDragMove$ = new Subject();\n    /**\n     * Subsctiption to _oneDragMove$ Subject\n     */\n    _oneMoveSubsription;\n    preparePublicSlide = slide => {\n      const newSlide = {\n        ...slide\n      };\n      delete newSlide.tplRef;\n      return newSlide;\n    };\n    constructor(zone, el, renderer, carouselService, animateService) {\n      this.zone = zone;\n      this.el = el;\n      this.renderer = renderer;\n      this.carouselService = carouselService;\n      this.animateService = animateService;\n    }\n    onMouseDown(event) {\n      if (this.owlDraggable.isMouseDragable) {\n        this._onDragStart(event);\n      }\n    }\n    onTouchStart(event) {\n      if (event.targetTouches.length >= 2) {\n        return false;\n      }\n      if (this.owlDraggable.isTouchDragable) {\n        this._onDragStart(event);\n      }\n    }\n    onTouchCancel(event) {\n      this._onDragEnd(event);\n    }\n    onDragStart() {\n      if (this.owlDraggable.isMouseDragable) {\n        return false;\n      }\n    }\n    onSelectStart() {\n      if (this.owlDraggable.isMouseDragable) {\n        return false;\n      }\n    }\n    ngOnInit() {\n      this._oneMoveSubsription = this._oneDragMove$.pipe(first()).subscribe(() => {\n        this._sendChanges();\n      });\n    }\n    ngOnDestroy() {\n      this._oneMoveSubsription.unsubscribe();\n    }\n    /**\n     * Passes this to _oneMouseTouchMove();\n     */\n    bindOneMouseTouchMove = ev => {\n      this._oneMouseTouchMove(ev);\n    };\n    /**\n     * Passes this to _onDragMove();\n     */\n    bindOnDragMove = ev => {\n      this._onDragMove(ev);\n    };\n    /**\n     * Passes this to _onDragMove();\n     */\n    bindOnDragEnd = ev => {\n      // this.zone.run(() => {\n      this._onDragEnd(ev);\n      // });\n    };\n    /**\n       * Handles `touchstart` and `mousedown` events.\n       * @todo Horizontal swipe threshold as option\n       * @todo #261\n       * @param event - The event arguments.\n       */\n    _onDragStart(event) {\n      let stage = null;\n      if (event.which === 3) {\n        return;\n      }\n      stage = this._prepareDragging(event);\n      this._drag.time = new Date().getTime();\n      this._drag.target = event.target;\n      this._drag.stage.start = stage;\n      this._drag.stage.current = stage;\n      this._drag.pointer = this._pointer(event);\n      this.listenerMouseUp = this.renderer.listen(document, 'mouseup', this.bindOnDragEnd);\n      this.listenerTouchEnd = this.renderer.listen(document, 'touchend', this.bindOnDragEnd);\n      this.zone.runOutsideAngular(() => {\n        this.listenerOneMouseMove = this.renderer.listen(document, 'mousemove', this.bindOneMouseTouchMove);\n        this.listenerOneTouchMove = this.renderer.listen(document, 'touchmove', this.bindOneMouseTouchMove);\n      });\n    }\n    /**\n     * Attaches listeners to `touchmove` and `mousemove` events; initiates updating carousel after starting dragging\n     * @param event event objech of mouse or touch event\n     */\n    _oneMouseTouchMove(event) {\n      const delta = this._difference(this._drag.pointer, this._pointer(event));\n      if (this.listenerATag) {\n        this.listenerATag();\n      }\n      if (Math.abs(delta.x) < 3 && Math.abs(delta.y) < 3 && this._is('valid')) {\n        return;\n      }\n      if (Math.abs(delta.x) < 3 && Math.abs(delta.x) < Math.abs(delta.y) && this._is('valid')) {\n        return;\n      }\n      this.listenerOneMouseMove();\n      this.listenerOneTouchMove();\n      this._drag.moving = true;\n      this.blockClickAnchorInDragging(event);\n      this.listenerMouseMove = this.renderer.listen(document, 'mousemove', this.bindOnDragMove);\n      this.listenerTouchMove = this.renderer.listen(document, 'touchmove', this.bindOnDragMove);\n      event.preventDefault();\n      this._enterDragging();\n      this._oneDragMove$.next(event);\n      // this._sendChanges();\n    }\n    /**\n     * Attaches handler to HTMLAnchorElement for preventing click while carousel is being dragged\n     * @param event event object\n     */\n    blockClickAnchorInDragging(event) {\n      let target = event.target;\n      while (target && !(target instanceof HTMLAnchorElement)) {\n        target = target.parentElement;\n      }\n      if (target instanceof HTMLAnchorElement) {\n        this.listenerATag = this.renderer.listen(target, 'click', () => false);\n      }\n    }\n    /**\n     * Handles the `touchmove` and `mousemove` events.\n     * @todo #261\n     * @param event - The event arguments.\n     */\n    _onDragMove(event) {\n      let stage;\n      const stageOrExit = this.carouselService.defineNewCoordsDrag(event, this._drag);\n      if (stageOrExit === false) {\n        return;\n      }\n      stage = stageOrExit;\n      event.preventDefault();\n      this._drag.stage.current = stage;\n      this._animate(stage.x - this._drag.stage.start.x);\n    }\n    /**\n     * Moves .owl-stage left-right\n     * @param coordinate coordinate to be set to .owl-stage\n     */\n    _animate(coordinate) {\n      this.renderer.setStyle(this.el.nativeElement.children[0], 'transform', `translate3d(${coordinate}px,0px,0px`);\n      this.renderer.setStyle(this.el.nativeElement.children[0], 'transition', '0s');\n    }\n    /**\n       * Handles the `touchend` and `mouseup` events.\n       * @todo #261\n       * @todo Threshold for click event\n       * @param event - The event arguments.\n       */\n    _onDragEnd(event) {\n      this.carouselService.owlDOMData.isGrab = false;\n      this.listenerOneMouseMove();\n      this.listenerOneTouchMove();\n      if (this._drag.moving) {\n        this.renderer.setStyle(this.el.nativeElement.children[0], 'transform', ``);\n        this.renderer.setStyle(this.el.nativeElement.children[0], 'transition', this.carouselService.speed(+this.carouselService.settings.dragEndSpeed || this.carouselService.settings.smartSpeed) / 1000 + 's');\n        this._finishDragging(event);\n        this.listenerMouseMove();\n        this.listenerTouchMove();\n      }\n      this._drag = {\n        time: null,\n        target: null,\n        pointer: null,\n        stage: {\n          start: null,\n          current: null\n        },\n        direction: null,\n        active: false,\n        moving: false\n      };\n      // this.carouselService.trigger('dragged');\n      this.listenerMouseUp();\n      this.listenerTouchEnd();\n    }\n    /**\n       * Prepares data for dragging carousel. It starts after firing `touchstart` and `mousedown` events.\n       * @param event - The event arguments.\n       * @returns stage - object with 'x' and 'y' coordinates of .owl-stage\n       */\n    _prepareDragging(event) {\n      return this.carouselService.prepareDragging(event);\n    }\n    /**\n     * Attaches handler for 'click' event on any element in .owl-stage in order to prevent dragging when moving of cursor is less than 3px\n     */\n    _oneClickHandler = () => {\n      this.listenerOneClick = this.renderer.listen(this._drag.target, 'click', () => false);\n      this.listenerOneClick();\n    };\n    /**\n     * Finishes dragging\n     * @param event object event of 'mouseUp' of 'touchend' events\n     */\n    _finishDragging(event) {\n      this.carouselService.finishDragging(event, this._drag, this._oneClickHandler);\n    }\n    /**\n       * Gets unified pointer coordinates from event.\n       * @param event The `mousedown` or `touchstart` event.\n       * @returns Contains `x` and `y` coordinates of current pointer position.\n       */\n    _pointer(event) {\n      return this.carouselService.pointer(event);\n    }\n    /**\n       * Gets the difference of two vectors.\n       * @param first The first vector.\n       * @param second The second vector.\n       * @returns The difference.\n       */\n    _difference(firstC, second) {\n      return this.carouselService.difference(firstC, second);\n    }\n    /**\n       * Checks whether the carousel is in a specific state or not.\n       * @param specificState The state to check.\n       * @returns The flag which indicates if the carousel is busy.\n       */\n    _is(specificState) {\n      return this.carouselService.is(specificState);\n    }\n    /**\n    * Enters a state.\n    * @param name The state name.\n    */\n    _enter(name) {\n      this.carouselService.enter(name);\n    }\n    /**\n       * Sends all data needed for View.\n       */\n    _sendChanges() {\n      this.carouselService.sendChanges();\n    }\n    /**\n     * Handler for transitioend event\n     */\n    onTransitionEnd() {\n      this.carouselService.onTransitionEnd();\n    }\n    /**\n       * Enters into a 'dragging' state\n       */\n    _enterDragging() {\n      this.carouselService.enterDragging();\n    }\n    /**\n     * Handles the end of 'animationend' event\n     * @param id Id of slides\n     */\n    clear(id) {\n      this.animateService.clear(id);\n    }\n    static ɵfac = function StageComponent_Factory(t) {\n      return new (t || StageComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(CarouselService), i0.ɵɵdirectiveInject(AnimateService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: StageComponent,\n      selectors: [[\"owl-stage\"]],\n      hostBindings: function StageComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mousedown\", function StageComponent_mousedown_HostBindingHandler($event) {\n            return ctx.onMouseDown($event);\n          })(\"touchstart\", function StageComponent_touchstart_HostBindingHandler($event) {\n            return ctx.onTouchStart($event);\n          })(\"touchcancel\", function StageComponent_touchcancel_HostBindingHandler($event) {\n            return ctx.onTouchCancel($event);\n          })(\"dragstart\", function StageComponent_dragstart_HostBindingHandler() {\n            return ctx.onDragStart();\n          })(\"selectstart\", function StageComponent_selectstart_HostBindingHandler() {\n            return ctx.onSelectStart();\n          });\n        }\n      },\n      inputs: {\n        owlDraggable: \"owlDraggable\",\n        stageData: \"stageData\",\n        slidesData: \"slidesData\"\n      },\n      decls: 3,\n      vars: 8,\n      consts: [[1, \"owl-stage\", 3, \"transitionend\", \"ngStyle\"], [4, \"ngFor\", \"ngForOf\"], [1, \"owl-item\", 3, \"animationend\", \"ngClass\", \"ngStyle\"], [4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function StageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n          i0.ɵɵlistener(\"transitionend\", function StageComponent_Template_div_transitionend_1_listener() {\n            return ctx.onTransitionEnd();\n          });\n          i0.ɵɵtemplate(2, StageComponent_ng_container_2_Template, 3, 9, \"ng-container\", 1);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction5(2, _c0, ctx.stageData.width + \"px\", ctx.stageData.transform, ctx.stageData.transition, ctx.stageData.paddingL ? ctx.stageData.paddingL + \"px\" : \"\", ctx.stageData.paddingR ? ctx.stageData.paddingR + \"px\" : \"\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.slidesData);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgTemplateOutlet, i3.NgStyle],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('autoHeight', [state('nulled', style({\n          height: 0\n        })), state('full', style({\n          height: '*'\n        })), transition('full => nulled', [\n        // style({height: '*'}),\n        animate('700ms 350ms')]), transition('nulled => full', [\n        // style({height: 0}),\n        animate(350)])])]\n      }\n    });\n  }\n  return StageComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CarouselComponent = /*#__PURE__*/(() => {\n  class CarouselComponent {\n    el;\n    resizeService;\n    carouselService;\n    navigationService;\n    autoplayService;\n    lazyLoadService;\n    animateService;\n    autoHeightService;\n    hashService;\n    logger;\n    changeDetectorRef;\n    slides;\n    translated = new EventEmitter();\n    dragging = new EventEmitter();\n    change = new EventEmitter();\n    changed = new EventEmitter();\n    initialized = new EventEmitter();\n    /**\n     * Width of carousel window (tag with class .owl-carousel), in wich we can see moving sliders\n     */\n    carouselWindowWidth;\n    /**\n     * Subscription to 'resize' event\n     */\n    resizeSubscription;\n    /**\n     * Subscription merge Observable, which merges all Observables in the component except 'resize' Observable and this.slides.changes()\n     */\n    _allObservSubscription;\n    /**\n     * Subscription to `this.slides.changes().\n     * It could be included in 'this._allObservSubscription', but that subcription get created during the initializing of component\n     * and 'this.slides' are undefined at that moment. So it's needed to wait for initialization of content.\n     */\n    _slidesChangesSubscription;\n    /**\n     * Current settings for the carousel.\n     */\n    owlDOMData;\n    /**\n     * Data of owl-stage\n     */\n    stageData;\n    /**\n     *  Data of every slide\n     */\n    slidesData = [];\n    /**\n     * Data of navigation block\n     */\n    navData;\n    /**\n     * Data of dots block\n     */\n    dotsData;\n    /**\n     * Data, wich are passed out of carousel after ending of transioning of carousel\n     */\n    slidesOutputData;\n    /**\n     * Shows whether carousel is loaded of not.\n     */\n    carouselLoaded = false;\n    /**\n     * User's options\n     */\n    options;\n    prevOptions;\n    /**\n     * Observable for getting current View Settings\n     */\n    _viewCurSettings$;\n    /**\n     * Observable for catching the end of transition of carousel\n     */\n    _translatedCarousel$;\n    /**\n     * Observable for catching the start of dragging of the carousel\n     */\n    _draggingCarousel$;\n    /**\n     * Observable for catching the start of changing of the carousel\n     */\n    _changeCarousel$;\n    /**\n     * Observable for catching the moment when the data about slides changed, more exactly when the position changed.\n     */\n    _changedCarousel$;\n    /**\n     * Observable for catching the initialization of changing the carousel\n     */\n    _initializedCarousel$;\n    /**\n     * Observable for merging all Observables and creating one subscription\n     */\n    _carouselMerge$;\n    docRef;\n    constructor(el, resizeService, carouselService, navigationService, autoplayService, lazyLoadService, animateService, autoHeightService, hashService, logger, changeDetectorRef, docRef) {\n      this.el = el;\n      this.resizeService = resizeService;\n      this.carouselService = carouselService;\n      this.navigationService = navigationService;\n      this.autoplayService = autoplayService;\n      this.lazyLoadService = lazyLoadService;\n      this.animateService = animateService;\n      this.autoHeightService = autoHeightService;\n      this.hashService = hashService;\n      this.logger = logger;\n      this.changeDetectorRef = changeDetectorRef;\n      this.docRef = docRef;\n    }\n    onVisibilityChange(ev) {\n      if (!this.carouselService.settings.autoplay) return;\n      switch (this.docRef.visibilityState) {\n        case 'visible':\n          !this.autoplayService.isAutoplayStopped && this.autoplayService.play();\n          break;\n        case 'hidden':\n          this.autoplayService.pause();\n          break;\n        default:\n          break;\n      }\n    }\n    ngOnInit() {\n      this.spyDataStreams();\n      this.carouselWindowWidth = this.el.nativeElement.querySelector('.owl-carousel').clientWidth;\n    }\n    ngOnChanges() {\n      if (this.prevOptions !== this.options) {\n        if (this.prevOptions && this.slides?.toArray().length) {\n          this.carouselService.setup(this.carouselWindowWidth, this.slides.toArray(), this.options);\n          this.carouselService.initialize(this.slides.toArray());\n        } else if (this.prevOptions && !this.slides?.toArray().length) {\n          this.carouselLoaded = false;\n          this.logger.log(`There are no slides to show. So the carousel won't be re-rendered`);\n        } else {\n          this.carouselLoaded = false;\n        }\n        this.prevOptions = this.options;\n      }\n    }\n    ngAfterContentInit() {\n      if (this.slides.toArray().length) {\n        this.carouselService.setup(this.carouselWindowWidth, this.slides.toArray(), this.options);\n        this.carouselService.initialize(this.slides.toArray());\n        this._winResizeWatcher();\n      } else {\n        this.logger.log(`There are no slides to show. So the carousel won't be rendered`);\n      }\n      this._slidesChangesSubscription = this.slides.changes.pipe(tap(slides => {\n        this.carouselService.setup(this.carouselWindowWidth, slides.toArray(), this.options);\n        this.carouselService.initialize(slides.toArray());\n        if (!slides.toArray().length) {\n          this.carouselLoaded = false;\n        }\n        if (slides.toArray().length && !this.resizeSubscription) {\n          this._winResizeWatcher();\n        }\n      })).subscribe(() => {});\n    }\n    ngOnDestroy() {\n      if (this.resizeSubscription) {\n        this.resizeSubscription.unsubscribe();\n      }\n      if (this._slidesChangesSubscription) {\n        this._slidesChangesSubscription.unsubscribe();\n      }\n      if (this._allObservSubscription) {\n        this._allObservSubscription.unsubscribe();\n      }\n    }\n    /**\n     * Joins the observable login in one place: sets values to some observables, merges this observables and\n     * subcribes to merge func\n     */\n    spyDataStreams() {\n      this._viewCurSettings$ = this.carouselService.getViewCurSettings().pipe(tap(data => {\n        this.owlDOMData = data.owlDOMData;\n        this.stageData = data.stageData;\n        this.slidesData = data.slidesData;\n        if (!this.carouselLoaded) {\n          this.carouselLoaded = true;\n        }\n        this.navData = data.navData;\n        this.dotsData = data.dotsData;\n        this.changeDetectorRef.markForCheck();\n      }));\n      this._initializedCarousel$ = this.carouselService.getInitializedState().pipe(tap(() => {\n        this.gatherTranslatedData();\n        this.initialized.emit(this.slidesOutputData);\n        // this.slidesOutputData = {};\n      }));\n      this._translatedCarousel$ = this.carouselService.getTranslatedState().pipe(tap(() => {\n        this.gatherTranslatedData();\n        this.translated.emit(this.slidesOutputData);\n        // this.slidesOutputData = {};\n      }));\n      this._changeCarousel$ = this.carouselService.getChangeState().pipe(tap(() => {\n        this.gatherTranslatedData();\n        this.change.emit(this.slidesOutputData);\n        // this.slidesOutputData = {};\n      }));\n      this._changedCarousel$ = this.carouselService.getChangeState().pipe(switchMap(value => {\n        const changedPosition = of(value).pipe(filter(() => value.property.name === 'position'), switchMap(() => from(this.slidesData)), skip(value.property.value), take(this.carouselService.settings.items), map(slide => {\n          const clonedIdPrefix = this.carouselService.clonedIdPrefix;\n          const id = slide.id.indexOf(clonedIdPrefix) >= 0 ? slide.id.slice(clonedIdPrefix.length) : slide.id;\n          return {\n            ...slide,\n            id: id,\n            isActive: true\n          };\n        }), toArray(), map(slides => {\n          return {\n            slides: slides,\n            startPosition: this.carouselService.relative(value.property.value)\n          };\n        }));\n        // const changedSetting: Observable<SlidesOutputData> = of(value).pipe(\n        //   filter(() => value.property.name === 'settings'),\n        //   map(() => {\n        //     return {\n        //       slides: [],\n        //       startPosition: this.carouselService.relative(value.property.value)\n        //     }\n        //   })\n        // )\n        return merge(changedPosition);\n      }), tap(slidesData => {\n        this.gatherTranslatedData();\n        this.changed.emit(slidesData.slides.length ? slidesData : this.slidesOutputData);\n        // console.log(this.slidesOutputData);\n        // this.slidesOutputData = {};\n      }));\n      this._draggingCarousel$ = this.carouselService.getDragState().pipe(tap(() => {\n        this.gatherTranslatedData();\n        this.dragging.emit({\n          dragging: true,\n          data: this.slidesOutputData\n        });\n      }), switchMap(() => this.carouselService.getDraggedState().pipe(map(() => !!this.carouselService.is('animating')))), switchMap(anim => {\n        if (anim) {\n          return this.carouselService.getTranslatedState().pipe(first());\n        } else {\n          return of('not animating');\n        }\n      }), tap(() => {\n        this.dragging.emit({\n          dragging: false,\n          data: this.slidesOutputData\n        });\n      }));\n      this._carouselMerge$ = merge(this._viewCurSettings$, this._translatedCarousel$, this._draggingCarousel$, this._changeCarousel$, this._changedCarousel$, this._initializedCarousel$);\n      this._allObservSubscription = this._carouselMerge$.subscribe(() => {});\n    }\n    /**\n     * Init subscription to resize event and attaches handler for this event\n     */\n    _winResizeWatcher() {\n      if (Object.keys(this.carouselService._options.responsive).length) {\n        this.resizeSubscription = this.resizeService.onResize$.pipe(filter(() => this.carouselWindowWidth !== this.el.nativeElement.querySelector('.owl-carousel').clientWidth), delay(this.carouselService.settings.responsiveRefreshRate)).subscribe(() => {\n          this.carouselService.onResize(this.el.nativeElement.querySelector('.owl-carousel').clientWidth);\n          this.carouselWindowWidth = this.el.nativeElement.querySelector('.owl-carousel').clientWidth;\n        });\n      }\n    }\n    /**\n     * Handler for transitioend event\n     */\n    onTransitionEnd() {\n      this.carouselService.onTransitionEnd();\n    }\n    /**\n     * Handler for click event, attached to next button\n     */\n    next() {\n      if (!this.carouselLoaded) return;\n      this.navigationService.next(this.carouselService.settings.navSpeed);\n    }\n    /**\n     * Handler for click event, attached to prev button\n     */\n    prev() {\n      if (!this.carouselLoaded) return;\n      this.navigationService.prev(this.carouselService.settings.navSpeed);\n    }\n    /**\n     * Handler for click event, attached to dots\n     */\n    moveByDot(dotId) {\n      if (!this.carouselLoaded) return;\n      this.navigationService.moveByDot(dotId);\n    }\n    /**\n     * rewinds carousel to slide with needed id\n     * @param id fragment of url\n     */\n    to(id) {\n      // if (!this.carouselLoaded || ((this.navData && this.navData.disabled) && (this.dotsData && this.dotsData.disabled))) return;\n      if (!this.carouselLoaded) return;\n      this.navigationService.toSlideById(id);\n    }\n    /**\n     * Gathers and prepares data intended for passing to the user by means of firing event translatedCarousel\n     */\n    gatherTranslatedData() {\n      let startPosition;\n      const clonedIdPrefix = this.carouselService.clonedIdPrefix;\n      const activeSlides = this.slidesData.filter(slide => slide.isActive === true).map(slide => {\n        const id = slide.id.indexOf(clonedIdPrefix) >= 0 ? slide.id.slice(clonedIdPrefix.length) : slide.id;\n        return {\n          id: id,\n          width: slide.width,\n          marginL: slide.marginL,\n          marginR: slide.marginR,\n          center: slide.isCentered\n        };\n      });\n      startPosition = this.carouselService.relative(this.carouselService.current());\n      this.slidesOutputData = {\n        startPosition: startPosition,\n        slides: activeSlides\n      };\n    }\n    /**\n     * Starts pausing\n     */\n    startPausing() {\n      this.autoplayService.startPausing();\n    }\n    /**\n     * Starts playing after mouse leaves carousel\n     */\n    startPlayML() {\n      this.autoplayService.startPlayingMouseLeave();\n    }\n    /**\n     * Starts playing after touch ends\n     */\n    startPlayTE() {\n      this.autoplayService.startPlayingTouchEnd();\n    }\n    stopAutoplay() {\n      this.autoplayService.isAutoplayStopped = true;\n      this.autoplayService.stop();\n    }\n    startAutoplay() {\n      this.autoplayService.isAutoplayStopped = false;\n      this.autoplayService.play();\n    }\n    static ɵfac = function CarouselComponent_Factory(t) {\n      return new (t || CarouselComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ResizeService), i0.ɵɵdirectiveInject(CarouselService), i0.ɵɵdirectiveInject(NavigationService), i0.ɵɵdirectiveInject(AutoplayService), i0.ɵɵdirectiveInject(LazyLoadService), i0.ɵɵdirectiveInject(AnimateService), i0.ɵɵdirectiveInject(AutoHeightService), i0.ɵɵdirectiveInject(HashService), i0.ɵɵdirectiveInject(OwlLogger), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CarouselComponent,\n      selectors: [[\"owl-carousel-o\"]],\n      contentQueries: function CarouselComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CarouselSlideDirective, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slides = _t);\n        }\n      },\n      hostBindings: function CarouselComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"visibilitychange\", function CarouselComponent_visibilitychange_HostBindingHandler($event) {\n            return ctx.onVisibilityChange($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        options: \"options\"\n      },\n      outputs: {\n        translated: \"translated\",\n        dragging: \"dragging\",\n        change: \"change\",\n        changed: \"changed\",\n        initialized: \"initialized\"\n      },\n      features: [i0.ɵɵProvidersFeature([NavigationService, AutoplayService, CarouselService, LazyLoadService, AnimateService, AutoHeightService, HashService]), i0.ɵɵNgOnChangesFeature],\n      decls: 4,\n      vars: 9,\n      consts: [[\"owlCarousel\", \"\"], [1, \"owl-carousel\", \"owl-theme\", 3, \"mouseover\", \"mouseleave\", \"touchstart\", \"touchend\", \"ngClass\"], [\"class\", \"owl-stage-outer\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"owl-stage-outer\"], [3, \"owlDraggable\", \"stageData\", \"slidesData\"], [1, \"owl-nav\", 3, \"ngClass\"], [1, \"owl-prev\", 3, \"click\", \"ngClass\", \"innerHTML\"], [1, \"owl-next\", 3, \"click\", \"ngClass\", \"innerHTML\"], [1, \"owl-dots\", 3, \"ngClass\"], [\"class\", \"owl-dot\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"owl-dot\", 3, \"click\", \"ngClass\"], [3, \"innerHTML\"]],\n      template: function CarouselComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵlistener(\"mouseover\", function CarouselComponent_Template_div_mouseover_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.startPausing());\n          })(\"mouseleave\", function CarouselComponent_Template_div_mouseleave_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.startPlayML());\n          })(\"touchstart\", function CarouselComponent_Template_div_touchstart_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.startPausing());\n          })(\"touchend\", function CarouselComponent_Template_div_touchend_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.startPlayTE());\n          });\n          i0.ɵɵtemplate(2, CarouselComponent_div_2_Template, 2, 6, \"div\", 2)(3, CarouselComponent_ng_container_3_Template, 6, 15, \"ng-container\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(3, _c3, ctx.owlDOMData == null ? null : ctx.owlDOMData.rtl, ctx.owlDOMData == null ? null : ctx.owlDOMData.isLoaded, ctx.owlDOMData == null ? null : ctx.owlDOMData.isResponsive, ctx.owlDOMData == null ? null : ctx.owlDOMData.isMouseDragable, ctx.owlDOMData == null ? null : ctx.owlDOMData.isGrab));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.carouselLoaded);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.slides.toArray().length);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, StageComponent],\n      styles: [\".owl-theme[_ngcontent-%COMP%]{display:block}\"],\n      changeDetection: 0\n    });\n  }\n  return CarouselComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet OwlRouterLinkDirective = /*#__PURE__*/(() => {\n  class OwlRouterLinkDirective {\n    router;\n    route;\n    // TODO(issue/24571): remove '!'.\n    queryParams;\n    // TODO(issue/24571): remove '!'.\n    fragment;\n    // TODO(issue/24571): remove '!'.\n    queryParamsHandling;\n    // TODO(issue/24571): remove '!'.\n    preserveFragment;\n    // TODO(issue/24571): remove '!'.\n    skipLocationChange;\n    // TODO(issue/24571): remove '!'.\n    replaceUrl;\n    stopLink = false;\n    commands = [];\n    // TODO(issue/24571): remove '!'.\n    preserve;\n    constructor(router, route, tabIndex, renderer, el) {\n      this.router = router;\n      this.route = route;\n      if (tabIndex == null) {\n        renderer.setAttribute(el.nativeElement, 'tabindex', '0');\n      }\n    }\n    set owlRouterLink(commands) {\n      if (commands != null) {\n        this.commands = Array.isArray(commands) ? commands : [commands];\n      } else {\n        this.commands = [];\n      }\n    }\n    /**\n     * @deprecated 4.0.0 use `queryParamsHandling` instead.\n     */\n    set preserveQueryParams(value) {\n      if (isDevMode() && console && console.warn) {\n        console.warn('preserveQueryParams is deprecated!, use queryParamsHandling instead.');\n      }\n      this.preserve = value;\n    }\n    onClick() {\n      const extras = {\n        skipLocationChange: attrBoolValue(this.skipLocationChange),\n        replaceUrl: attrBoolValue(this.replaceUrl)\n      };\n      if (this.stopLink) {\n        return false;\n      }\n      this.router.navigateByUrl(this.urlTree, extras);\n      return true;\n    }\n    get urlTree() {\n      return this.router.createUrlTree(this.commands, {\n        relativeTo: this.route,\n        queryParams: this.queryParams,\n        fragment: this.fragment,\n        queryParamsHandling: this.queryParamsHandling,\n        preserveFragment: attrBoolValue(this.preserveFragment)\n      });\n    }\n    static ɵfac = function OwlRouterLinkDirective_Factory(t) {\n      return new (t || OwlRouterLinkDirective)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OwlRouterLinkDirective,\n      selectors: [[\"\", \"owlRouterLink\", \"\", 5, \"a\"]],\n      hostBindings: function OwlRouterLinkDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function OwlRouterLinkDirective_click_HostBindingHandler() {\n            return ctx.onClick();\n          });\n        }\n      },\n      inputs: {\n        queryParams: \"queryParams\",\n        fragment: \"fragment\",\n        queryParamsHandling: \"queryParamsHandling\",\n        preserveFragment: \"preserveFragment\",\n        skipLocationChange: \"skipLocationChange\",\n        replaceUrl: \"replaceUrl\",\n        stopLink: \"stopLink\",\n        owlRouterLink: \"owlRouterLink\",\n        preserveQueryParams: \"preserveQueryParams\"\n      }\n    });\n  }\n  return OwlRouterLinkDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n *\n * Lets you link to specific routes in your app.\n *\n * See `RouterLink` for more information.\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nlet OwlRouterLinkWithHrefDirective = /*#__PURE__*/(() => {\n  class OwlRouterLinkWithHrefDirective {\n    router;\n    route;\n    locationStrategy;\n    // TODO(issue/24571): remove '!'.\n    target;\n    // TODO(issue/24571): remove '!'.\n    queryParams;\n    // TODO(issue/24571): remove '!'.\n    fragment;\n    // TODO(issue/24571): remove '!'.\n    queryParamsHandling;\n    // TODO(issue/24571): remove '!'.\n    preserveFragment;\n    // TODO(issue/24571): remove '!'.\n    skipLocationChange;\n    // TODO(issue/24571): remove '!'.\n    replaceUrl;\n    stopLink = false;\n    commands = [];\n    subscription;\n    // TODO(issue/24571): remove '!'.\n    preserve;\n    // the url displayed on the anchor element.\n    // TODO(issue/24571): remove '!'.\n    href;\n    constructor(router, route, locationStrategy) {\n      this.router = router;\n      this.route = route;\n      this.locationStrategy = locationStrategy;\n      this.subscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.updateTargetUrlAndHref();\n        }\n      });\n    }\n    set owlRouterLink(commands) {\n      if (commands != null) {\n        this.commands = Array.isArray(commands) ? commands : [commands];\n      } else {\n        this.commands = [];\n      }\n    }\n    set preserveQueryParams(value) {\n      if (isDevMode() && console && console.warn) {\n        console.warn('preserveQueryParams is deprecated, use queryParamsHandling instead.');\n      }\n      this.preserve = value;\n    }\n    ngOnChanges(changes) {\n      this.updateTargetUrlAndHref();\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n    }\n    onClick(button, ctrlKey, metaKey, shiftKey) {\n      if (button !== 0 || ctrlKey || metaKey || shiftKey) {\n        return true;\n      }\n      if (typeof this.target === 'string' && this.target !== '_self') {\n        return true;\n      }\n      if (this.stopLink) {\n        return false;\n      }\n      const extras = {\n        skipLocationChange: attrBoolValue(this.skipLocationChange),\n        replaceUrl: attrBoolValue(this.replaceUrl)\n      };\n      this.router.navigateByUrl(this.urlTree, extras);\n      return false;\n    }\n    updateTargetUrlAndHref() {\n      this.href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.urlTree));\n    }\n    get urlTree() {\n      return this.router.createUrlTree(this.commands, {\n        relativeTo: this.route,\n        queryParams: this.queryParams,\n        fragment: this.fragment,\n        queryParamsHandling: this.queryParamsHandling,\n        preserveFragment: attrBoolValue(this.preserveFragment)\n      });\n    }\n    static ɵfac = function OwlRouterLinkWithHrefDirective_Factory(t) {\n      return new (t || OwlRouterLinkWithHrefDirective)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OwlRouterLinkWithHrefDirective,\n      selectors: [[\"a\", \"owlRouterLink\", \"\"]],\n      hostVars: 2,\n      hostBindings: function OwlRouterLinkWithHrefDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function OwlRouterLinkWithHrefDirective_click_HostBindingHandler($event) {\n            return ctx.onClick($event.button, $event.ctrlKey, $event.metaKey, $event.shiftKey);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"href\", ctx.href, i0.ɵɵsanitizeUrl);\n          i0.ɵɵattribute(\"target\", ctx.target);\n        }\n      },\n      inputs: {\n        target: \"target\",\n        queryParams: \"queryParams\",\n        fragment: \"fragment\",\n        queryParamsHandling: \"queryParamsHandling\",\n        preserveFragment: \"preserveFragment\",\n        skipLocationChange: \"skipLocationChange\",\n        replaceUrl: \"replaceUrl\",\n        stopLink: \"stopLink\",\n        owlRouterLink: \"owlRouterLink\",\n        preserveQueryParams: \"preserveQueryParams\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return OwlRouterLinkWithHrefDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction attrBoolValue(s) {\n  return s === '' || !!s;\n}\n\n/**\n * Data which will be passed out after ending of transition of carousel\n */\nclass SlidesOutputData {\n  startPosition;\n  slides;\n}\n;\nconst routes = [];\nlet CarouselModule = /*#__PURE__*/(() => {\n  class CarouselModule {\n    static ɵfac = function CarouselModule_Factory(t) {\n      return new (t || CarouselModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CarouselModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [WINDOW_PROVIDERS, ResizeService, DOCUMENT_PROVIDERS, OwlLogger],\n      imports: [CommonModule]\n    });\n  }\n  return CarouselModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass SlideModel {\n  /**\n   * Id of slide\n   */\n  id;\n  /**\n   * Active state of slide. If true slide gets css-class .active\n   */\n  isActive;\n  /**\n   * TemplateRef of slide. In other words its html-markup\n   */\n  tplRef;\n  /**\n   * Number of grid parts to be used\n   */\n  dataMerge;\n  /**\n   * Width of slide\n   */\n  width;\n  /**\n   * Css-rule 'margin-left'\n   */\n  marginL;\n  /**\n   * Css-rule 'margin-right'\n   */\n  marginR;\n  /**\n   * Make slide to be on center of the carousel\n   */\n  isCentered;\n  /**\n   * Mark slide to be on center of the carousel (has .center)\n   */\n  center;\n  /**\n   * Cloned slide. It's being used when 'loop'=true\n   */\n  isCloned;\n  /**\n   * Indicates whether slide should be lazy loaded\n   */\n  load;\n  /**\n   * Css-rule 'left'\n   */\n  left;\n  /**\n   * Changeable classes of slide\n   */\n  classes;\n  /**\n   * Shows whether slide could be animated and could have css-class '.animated'\n   */\n  isAnimated;\n  /**\n   * Shows whether slide could be animated-in and could have css-class '.owl-animated-in'\n   */\n  isDefAnimatedIn;\n  /**\n   * Shows whether slide could be animated-out and could have css-class '.owl-animated-out'\n   */\n  isDefAnimatedOut;\n  /**\n   * Shows whether slide could be animated-in and could have animation css-class defined by user\n   */\n  isCustomAnimatedIn;\n  /**\n   * Shows whether slide could be animated-out and could have animation css-class defined by user\n   */\n  isCustomAnimatedOut;\n  /**\n   * State for defining the height of slide.It's values could be 'full' and 'nulled'. 'Full' sets css-height to 'auto', 'nulled' sets height to '0'.\n   */\n  heightState;\n  /**\n   * Hash (fragment) of url which corresponds to slide\n   */\n  hashFragment;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CarouselComponent, CarouselModule, CarouselSlideDirective, OwlRouterLinkDirective, OwlRouterLinkWithHrefDirective, SlideModel, SlidesOutputData };\n//# sourceMappingURL=ngx-owl-carousel-o.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}