{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst NAMESPACE = 'ionic';\nconst BUILD = /* ionic */{\n  allRenderFn: false,\n  appendChildSlotFix: false,\n  asyncLoading: true,\n  asyncQueue: false,\n  attachStyles: true,\n  cloneNodeFix: false,\n  cmpDidLoad: true,\n  cmpDidRender: true,\n  cmpDidUnload: false,\n  cmpDidUpdate: true,\n  cmpShouldUpdate: false,\n  cmpWillLoad: true,\n  cmpWillRender: true,\n  cmpWillUpdate: false,\n  connectedCallback: true,\n  constructableCSS: true,\n  cssAnnotations: true,\n  devTools: false,\n  disconnectedCallback: true,\n  element: false,\n  event: true,\n  experimentalScopedSlotChanges: false,\n  experimentalSlotFixes: false,\n  formAssociated: false,\n  hasRenderFn: true,\n  hostListener: true,\n  hostListenerTarget: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetParent: false,\n  hostListenerTargetWindow: true,\n  hotModuleReplacement: false,\n  hydrateClientSide: true,\n  hydrateServerSide: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  initializeNextTick: false,\n  invisiblePrehydration: true,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  lazyLoad: true,\n  lifecycle: true,\n  lifecycleDOMEvents: false,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  profile: false,\n  prop: true,\n  propBoolean: true,\n  propMutable: true,\n  propNumber: true,\n  propString: true,\n  reflect: true,\n  scoped: true,\n  scopedSlotTextContentFix: false,\n  scriptDataOpts: false,\n  shadowDelegatesFocus: true,\n  shadowDom: true,\n  slot: true,\n  slotChildNodesFix: false,\n  slotRelocation: true,\n  state: true,\n  style: true,\n  svg: true,\n  taskQueue: true,\n  transformTagName: false,\n  updatable: true,\n  vdomAttribute: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomPropOrAttr: true,\n  vdomRef: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  vdomXlink: true,\n  watchCallback: true\n};\n\n/**\n * Virtual DOM patching algorithm based on Snabbdom by\n * Simon Friis Vindum (@paldepind)\n * Licensed under the MIT License\n * https://github.com/snabbdom/snabbdom/blob/master/LICENSE\n *\n * Modified for Stencil's renderer and slot projection\n */\nlet scopeId;\nlet contentRef;\nlet hostTagName;\nlet useNativeShadowDom = false;\nlet checkSlotFallbackVisibility = false;\nlet checkSlotRelocate = false;\nlet isSvgMode = false;\nlet queuePending = false;\nconst Build = {\n  isDev: false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: false\n};\nconst getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nconst createTime = (fnName, tagName = '') => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nconst uniqueTime = (key, measureText) => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nconst CONTENT_REF_ID = 'r';\nconst ORG_LOCATION_ID = 'o';\nconst SLOT_NODE_ID = 's';\nconst TEXT_NODE_ID = 't';\nconst HYDRATE_ID = 's-id';\nconst HYDRATED_STYLE_ID = 'sty-id';\nconst HYDRATE_CHILD_ID = 'c-id';\nconst HYDRATED_CSS = '{visibility:hidden}.hydrated{visibility:inherit}';\n/**\n * Constant for styles to be globally applied to `slot-fb` elements for pseudo-slot behavior.\n *\n * Two cascading rules must be used instead of a `:not()` selector due to Stencil browser\n * support as of Stencil v4.\n */\nconst SLOT_FB_CSS = 'slot-fb{display:contents}slot-fb[hidden]{display:none}';\nconst XLINK_NS = 'http://www.w3.org/1999/xlink';\n/**\n * Default style mode id\n */\n/**\n * Reusable empty obj/array\n * Don't add values to these!!\n */\nconst EMPTY_OBJ = {};\n/**\n * Namespaces\n */\nconst SVG_NS = 'http://www.w3.org/2000/svg';\nconst HTML_NS = 'http://www.w3.org/1999/xhtml';\nconst isDef = v => v != null;\n/**\n * Check whether a value is a 'complex type', defined here as an object or a\n * function.\n *\n * @param o the value to check\n * @returns whether it's a complex type or not\n */\nconst isComplexType = o => {\n  // https://jsperf.com/typeof-fn-object/5\n  o = typeof o;\n  return o === 'object' || o === 'function';\n};\n/**\n * Helper method for querying a `meta` tag that contains a nonce value\n * out of a DOM's head.\n *\n * @param doc The DOM containing the `head` to query against\n * @returns The content of the meta tag representing the nonce value, or `undefined` if no tag\n * exists or the tag has no content.\n */\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) === null || _a === void 0 ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) === null || _b === void 0 ? void 0 : _b.getAttribute('content')) !== null && _c !== void 0 ? _c : undefined;\n}\n/**\n * Production h() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, child?: d.ChildType): d.VNode;\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, ...children: d.ChildType[]): d.VNode;\nconst h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i = 0; i < c.length; i++) {\n      child = c[i];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== 'boolean') {\n        if (simple = typeof nodeName !== 'function' && !isComplexType(child)) {\n          child = String(child);\n        }\n        if (simple && lastSimple) {\n          // If the previous child was simple (string), we merge both\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          // Append a new vNode, if it's text, we create a text vNode\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    // normalize class / className attributes\n    {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== 'object' ? classData : Object.keys(classData).filter(k => classData[k]).join(' ');\n      }\n    }\n  }\n  if (typeof nodeName === 'function') {\n    // nodeName is a functional component\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  {\n    vnode.$key$ = key;\n  }\n  {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\n/**\n * A utility function for creating a virtual DOM node from a tag and some\n * possible text content.\n *\n * @param tag the tag for this element\n * @param text possible text content for the node\n * @returns a newly-minted virtual DOM node\n */\nconst newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    vnode.$attrs$ = null;\n  }\n  {\n    vnode.$key$ = null;\n  }\n  {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nconst Host = {};\n/**\n * Check whether a given node is a Host node or not\n *\n * @param node the virtual DOM node to check\n * @returns whether it's a Host node or not\n */\nconst isHost = node => node && node.$tag$ === Host;\n/**\n * Implementation of {@link d.FunctionalUtilities} for Stencil's VDom.\n *\n * Note that these functions convert from {@link d.VNode} to\n * {@link d.ChildNode} to give functional component developers a friendly\n * interface.\n */\nconst vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\n/**\n * Convert a {@link d.VNode} to a {@link d.ChildNode} in order to present a\n * friendlier public interface (hence, 'convertToPublic').\n *\n * @param node the virtual DOM node to convert\n * @returns a converted child node\n */\nconst convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\n/**\n * Convert a {@link d.ChildNode} back to an equivalent {@link d.VNode} in\n * order to use the resulting object in the virtual DOM. The initial object was\n * likely created as part of presenting a public API, so converting it back\n * involved making it 'private' again (hence, `convertToPrivate`).\n *\n * @param node the child node to convert\n * @returns a converted virtual DOM node\n */\nconst convertToPrivate = node => {\n  if (typeof node.vtag === 'function') {\n    const vnodeData = Object.assign({}, node.vattrs);\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\n/**\n * Entrypoint of the client-side hydration process. Facilitates calls to hydrate the\n * document and all its nodes.\n *\n * This process will also reconstruct the shadow root and slot DOM nodes for components using shadow DOM.\n *\n * @param hostElm The element to hydrate.\n * @param tagName The element's tag name.\n * @param hostId The host ID assigned to the element by the server.\n * @param hostRef The host reference for the element.\n */\nconst initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime('hydrateClient', tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map(c => {\n    const orgLocationId = c.$hostId$ + '.' + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    // Put the node back in its original location since the native Shadow DOM\n    // can handle rendering it its correct location now\n    if (orgLocationNode && supportsShadow && orgLocationNode['s-en'] === '') {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node['s-hn'] = tagName;\n      if (orgLocationNode) {\n        node['s-ol'] = orgLocationNode;\n        node['s-ol']['s-nr'] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (shadowRoot) {\n    shadowRootNodes.map(shadowRootNode => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\n/**\n * Recursively constructs the virtual node tree for a host element and its children.\n * The tree is constructed by parsing the annotations set on the nodes by the server.\n *\n * In addition to constructing the vNode tree, we also track information about the node's\n * descendants like which are slots, which should exist in the shadow root, and which\n * are nodes that should be rendered as children of the parent node.\n *\n * @param parentVNode The vNode representing the parent node.\n * @param childRenderNodes An array of all child nodes in the parent's node tree.\n * @param slotNodes An array of all slot nodes in the parent's node tree.\n * @param shadowRootNodes An array all nodes that should be rendered in the shadow root in the parent's node tree.\n * @param hostElm The parent element.\n * @param node The node to construct the vNode tree for.\n * @param hostId The host ID assigned to the element by the server.\n */\nconst clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i;\n  if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      // got the node data from the element's attribute\n      // `${hostId}.${nodeId}.${depth}.${index}`\n      childIdSplt = childNodeType.split('.');\n      if (childIdSplt[0] === hostId || childIdSplt[0] === '0') {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        // this is a new child vnode\n        // so ensure its parent vnode has the vchildren array\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        // add our child vnode to a specific index of the vnode's children\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        // this is now the new parent vnode for all the next child checks\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === '0') {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    // recursively drill down, end to start so we can remove nodes\n    for (i = node.childNodes.length - 1; i >= 0; i--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i], hostId);\n    }\n    if (node.shadowRoot) {\n      // keep drilling down through the shadow root nodes\n      for (i = node.shadowRoot.childNodes.length - 1; i >= 0; i--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i], hostId);\n      }\n    }\n  } else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n    // `${COMMENT_TYPE}.${hostId}.${nodeId}.${depth}.${index}`\n    childIdSplt = node.nodeValue.split('.');\n    if (childIdSplt[1] === hostId || childIdSplt[1] === '0') {\n      // comment node for either the host id or a 0 host id\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* NODE_TYPE.TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          // remove the text comment since it's no longer needed\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === '0') {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        // this comment node is specifically for this host id\n        if (childNodeType === SLOT_NODE_ID) {\n          // `${SLOT_NODE_ID}.${hostId}.${nodeId}.${depth}.${index}.${slotName}`;\n          childVNode.$tag$ = 'slot';\n          if (childIdSplt[5]) {\n            node['s-sn'] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node['s-sn'] = '';\n          }\n          node['s-sr'] = true;\n          if (shadowRootNodes) {\n            // browser support shadowRoot and this is a shadow dom component\n            // create an actual slot element\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              // add the slot name attribute\n              childVNode.$elm$.setAttribute('name', childVNode.$name$);\n            }\n            // insert the new slot element before the slot comment\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            // remove the slot comment since it's not needed for shadow\n            node.remove();\n            if (childVNode.$depth$ === '0') {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          // `${CONTENT_REF_ID}.${hostId}`;\n          if (shadowRootNodes) {\n            // remove the content ref comment since it's not needed for shadow\n            node.remove();\n          } else {\n            hostElm['s-cr'] = node;\n            node['s-cn'] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === 'style') {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = '0';\n    parentVNode.$children$ = [vnode];\n  }\n};\n/**\n * Recursively locate any comments representing an original location for a node in a node's\n * children or shadowRoot children.\n *\n * @param node The node to search.\n * @param orgLocNodes A map of the original location annotation and the current node being searched.\n */\nconst initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n    let i = 0;\n    for (; i < node.childNodes.length; i++) {\n      initializeDocumentHydrate(node.childNodes[i], orgLocNodes);\n    }\n    if (node.shadowRoot) {\n      for (i = 0; i < node.shadowRoot.childNodes.length; i++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i], orgLocNodes);\n      }\n    }\n  } else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n    const childIdSplt = node.nodeValue.split('.');\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + '.' + childIdSplt[2], node);\n      node.nodeValue = '';\n      // useful to know if the original location is\n      // the root light-dom of a shadow dom component\n      node['s-en'] = childIdSplt[3];\n    }\n  }\n};\n// Private\nconst computeMode = elm => modeResolutionChain.map(h => h(elm)).find(m => !!m);\n// Public\nconst setMode = handler => modeResolutionChain.push(handler);\nconst getMode = ref => getHostRef(ref).$modeName$;\n/**\n * Parse a new property value for a given property type.\n *\n * While the prop value can reasonably be expected to be of `any` type as far as TypeScript's type checker is concerned,\n * it is not safe to assume that the string returned by evaluating `typeof propValue` matches:\n *   1. `any`, the type given to `propValue` in the function signature\n *   2. the type stored from `propType`.\n *\n * This function provides the capability to parse/coerce a property's value to potentially any other JavaScript type.\n *\n * Property values represented in TSX preserve their type information. In the example below, the number 0 is passed to\n * a component. This `propValue` will preserve its type information (`typeof propValue === 'number'`). Note that is\n * based on the type of the value being passed in, not the type declared of the class member decorated with `@Prop`.\n * ```tsx\n * <my-cmp prop-val={0}></my-cmp>\n * ```\n *\n * HTML prop values on the other hand, will always a string\n *\n * @param propValue the new value to coerce to some type\n * @param propType the type of the prop, expressed as a binary number\n * @returns the parsed/coerced value\n */\nconst parsePropertyValue = (propValue, propType) => {\n  // ensure this value is of the correct prop type\n  if (propValue != null && !isComplexType(propValue)) {\n    if (propType & 4 /* MEMBER_FLAGS.Boolean */) {\n      // per the HTML spec, any string value means it is a boolean true value\n      // but we'll cheat here and say that the string \"false\" is the boolean false\n      return propValue === 'false' ? false : propValue === '' || !!propValue;\n    }\n    if (propType & 2 /* MEMBER_FLAGS.Number */) {\n      // force it to be a number\n      return parseFloat(propValue);\n    }\n    if (propType & 1 /* MEMBER_FLAGS.String */) {\n      // could have been passed as a number or boolean\n      // but we still want it as a string\n      return String(propValue);\n    }\n    // redundant return here for better minification\n    return propValue;\n  }\n  // not sure exactly what type we want\n  // so no need to change to a different type\n  return propValue;\n};\nconst getElement = ref => getHostRef(ref).$hostElement$;\nconst createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* EVENT_FLAGS.Bubbles */),\n        composed: !!(flags & 2 /* EVENT_FLAGS.Composed */),\n        cancelable: !!(flags & 1 /* EVENT_FLAGS.Cancellable */),\n        detail\n      });\n    }\n  };\n};\n/**\n * Helper function to create & dispatch a custom Event on a provided target\n * @param elm the target of the Event\n * @param name the name to give the custom Event\n * @param opts options for configuring a custom Event\n * @returns the custom Event\n */\nconst emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\nconst rootAppliedStyles = /*@__PURE__*/new WeakMap();\nconst registerStyle = (scopeId, cssText, allowCS) => {\n  let style = styles.get(scopeId);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === 'string') {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId, style);\n};\nconst addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId);\n  // if an element is NOT connected then getRootNode() will return the wrong root node\n  // so the fallback is to always use the document for the root node in those cases\n  styleContainerNode = styleContainerNode.nodeType === 11 /* NODE_TYPE.DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === 'string') {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = new Set());\n      }\n      if (!appliedStyles.has(scopeId)) {\n        if (styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId}\"]`))) {\n          // This is only happening on native shadow-dom, do not needs CSS var shim\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement('style');\n          styleElm.innerHTML = style;\n          // Apply CSP nonce to the style tag if it exists\n          const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute('nonce', nonce);\n          }\n          styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector('link'));\n        }\n        // Add styles for `slot-fb` elements if we're using slots outside the Shadow DOM\n        if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId);\n        }\n      }\n    } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId;\n};\nconst attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime('attachStyles', cmpMeta.$tagName$);\n  const scopeId = addStyle(elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if (flags & 10 /* CMP_FLAGS.needsScopedEncapsulation */) {\n    // only required when we're NOT using native shadow dom (slot)\n    // or this browser doesn't support native shadow dom\n    // and this host element was NOT created with SSR\n    // let's pick out the inner content for slot projection\n    // create a node to represent where the original\n    // content was first placed, which is useful later on\n    // DOM WRITE!!\n    elm['s-sc'] = scopeId;\n    elm.classList.add(scopeId + '-h');\n    if (flags & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n      elm.classList.add(scopeId + '-s');\n    }\n  }\n  endAttachStyles();\n};\nconst getScopeId = (cmp, mode) => 'sc-' + (mode && cmp.$flags$ & 32 /* CMP_FLAGS.hasMode */ ? cmp.$tagName$ + '-' + mode : cmp.$tagName$);\nconst convertScopedToShadow = css => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, '$1{');\n/**\n * Production setAccessor() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n/**\n * When running a VDom render set properties present on a VDom node onto the\n * corresponding HTML element.\n *\n * Note that this function has special functionality for the `class`,\n * `style`, `key`, and `ref` attributes, as well as event handlers (like\n * `onClick`, etc). All others are just passed through as-is.\n *\n * @param elm the HTMLElement onto which attributes should be set\n * @param memberName the name of the attribute to set\n * @param oldValue the old value for the attribute\n * @param newValue the new value for the attribute\n * @param isSvg whether we're in an svg context or not\n * @param flags bitflags for Vdom variables\n */\nconst setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (memberName === 'class') {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    } else if (memberName === 'style') {\n      // update style attribute, css properties and values\n      {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (prop.includes('-')) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = '';\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (prop.includes('-')) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (memberName === 'key') ;else if (memberName === 'ref') {\n      // minifier will clean this up\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (!isProp && memberName[0] === 'o' && memberName[1] === 'n') {\n      // Event Handlers\n      // so if the member name starts with \"on\" and the 3rd characters is\n      // a capital letter, and it's not already a member on the element,\n      // then we're assuming it's an event listener\n      if (memberName[2] === '-') {\n        // on- prefixed events\n        // allows to be explicit about the dom event to listen without any magic\n        // under the hood:\n        // <my-cmp on-click> // listens for \"click\"\n        // <my-cmp on-Click> // listens for \"Click\"\n        // <my-cmp on-ionChange> // listens for \"ionChange\"\n        // <my-cmp on-EVENTS> // listens for \"EVENTS\"\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        // standard event\n        // the JSX attribute could have been \"onMouseOver\" and the\n        // member name \"onmouseover\" is on the window's prototype\n        // so let's add the listener \"mouseover\", which is all lowercased\n        memberName = ln.slice(2);\n      } else {\n        // custom event\n        // the JSX attribute could have been \"onMyCustomEvent\"\n        // so let's trim off the \"on\" prefix and lowercase the first character\n        // and add the listener \"myCustomEvent\"\n        // except for the first character, we keep the event name case\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        // Need to account for \"capture\" events.\n        // If the event name ends with \"Capture\", we'll update the name to remove\n        // the \"Capture\" suffix and make sure the event listener is setup to handle the capture event.\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        // Make sure we only replace the last instance of \"Capture\"\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, '');\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else {\n      // Set property if it exists and it's not a SVG\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes('-')) {\n            const n = newValue == null ? '' : newValue;\n            // Workaround for Safari, moving the <input> caret when re-assigning the same valued\n            if (memberName === 'list') {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {\n          /**\n           * in case someone tries to set a read-only property, e.g. \"namespaceURI\", we just ignore it\n           */\n        }\n      }\n      /**\n       * Need to manually update attribute if:\n       * - memberName is not an attribute\n       * - if we are rendering the host element in order to reflect attribute\n       * - if it's a SVG, since properties might not work in <svg>\n       * - if the newValue is null/undefined or 'false'.\n       */\n      let xlink = false;\n      {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, ''))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === '') {\n          if (xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* VNODE_FLAGS.isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? '' : newValue;\n        if (xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nconst parseClassListRegex = /\\s/;\n/**\n * Parsed a string of classnames into an array\n * @param value className string, e.g. \"foo bar baz\"\n * @returns list of classes, e.g. [\"foo\", \"bar\", \"baz\"]\n */\nconst parseClassList = value => !value ? [] : value.split(parseClassListRegex);\nconst CAPTURE_EVENT_SUFFIX = 'Capture';\nconst CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + '$');\nconst updateElement = (oldVnode, newVnode, isSvgMode, memberName) => {\n  // if the element passed in is a shadow root, which is a document fragment\n  // then we want to be adding attrs/props to the shadow root's \"host\" element\n  // if it's not a shadow root, then we add attrs/props to the same element\n  const elm = newVnode.$elm$.nodeType === 11 /* NODE_TYPE.DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  {\n    // remove attributes no longer present on the vnode by setting them to undefined\n    for (memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], undefined, isSvgMode, newVnode.$flags$);\n      }\n    }\n  }\n  // add new & update changed attributes\n  for (memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode, newVnode.$flags$);\n  }\n};\n/**\n * Sort a list of attribute names to ensure that all the attribute names which\n * are _not_ `\"ref\"` come before `\"ref\"`. Preserve the order of the non-ref\n * attributes.\n *\n * **Note**: if the supplied attributes do not include `'ref'` then the same\n * (by reference) array will be returned without modification.\n *\n * @param attrNames attribute names to sort\n * @returns a list of attribute names, sorted if they include `\"ref\"`\n */\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes('ref') ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== 'ref'), 'ref'] :\n  // no need to sort, return the original array\n  attrNames;\n}\n/**\n * Create a DOM Node corresponding to one of the children of a given VNode.\n *\n * @param oldParentVNode the parent VNode from the previous render\n * @param newParentVNode the parent VNode from the current render\n * @param childIndex the index of the VNode, in the _new_ parent node's\n * children, for which we will create a new DOM node\n * @param parentElm the parent DOM node which our new node will be a child of\n * @returns the newly created node\n */\nconst createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  // tslint:disable-next-line: prefer-const\n  const newVNode = newParentVNode.$children$[childIndex];\n  let i = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (!useNativeShadowDom) {\n    // remember for later we need to check to relocate nodes\n    checkSlotRelocate = true;\n    if (newVNode.$tag$ === 'slot') {\n      if (scopeId) {\n        // scoped css needs to add its scoped id to the parent element\n        parentElm.classList.add(scopeId + '-s');\n      }\n      newVNode.$flags$ |= newVNode.$children$ ?\n      // slot element has fallback content\n      2 /* VNODE_FLAGS.isSlotFallback */ :\n      // slot element does not have fallback content\n      1 /* VNODE_FLAGS.isSlotReference */;\n    }\n  }\n  if (newVNode.$text$ !== null) {\n    // create text node\n    elm = newVNode.$elm$ = doc.createTextNode(newVNode.$text$);\n  } else if (newVNode.$flags$ & 1 /* VNODE_FLAGS.isSlotReference */) {\n    // create a slot reference node\n    elm = newVNode.$elm$ = doc.createTextNode('');\n  } else {\n    if (!isSvgMode) {\n      isSvgMode = newVNode.$tag$ === 'svg';\n    }\n    // create element\n    elm = newVNode.$elm$ = doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, newVNode.$flags$ & 2 /* VNODE_FLAGS.isSlotFallback */ ? 'slot-fb' : newVNode.$tag$);\n    if (isSvgMode && newVNode.$tag$ === 'foreignObject') {\n      isSvgMode = false;\n    }\n    // add css classes, attrs, props, listeners, etc.\n    {\n      updateElement(null, newVNode, isSvgMode);\n    }\n    if (isDef(scopeId) && elm['s-si'] !== scopeId) {\n      // if there is a scopeId and this is the initial render\n      // then let's add the scopeId as a css class\n      elm.classList.add(elm['s-si'] = scopeId);\n    }\n    if (newVNode.$children$) {\n      for (i = 0; i < newVNode.$children$.length; ++i) {\n        // create the node\n        childNode = createElm(oldParentVNode, newVNode, i, elm);\n        // return node could have been null\n        if (childNode) {\n          // append our new node\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    {\n      if (newVNode.$tag$ === 'svg') {\n        // Only reset the SVG context when we're exiting <svg> element\n        isSvgMode = false;\n      } else if (elm.tagName === 'foreignObject') {\n        // Reenter SVG context when we're exiting <foreignObject> element\n        isSvgMode = true;\n      }\n    }\n  }\n  // This needs to always happen so we can hide nodes that are projected\n  // to another component but don't end up in a slot\n  elm['s-hn'] = hostTagName;\n  {\n    if (newVNode.$flags$ & (2 /* VNODE_FLAGS.isSlotFallback */ | 1 /* VNODE_FLAGS.isSlotReference */)) {\n      // remember the content reference comment\n      elm['s-sr'] = true;\n      // remember the content reference comment\n      elm['s-cr'] = contentRef;\n      // remember the slot name, or empty string for default slot\n      elm['s-sn'] = newVNode.$name$ || '';\n      // remember the ref callback function\n      elm['s-rf'] = (_a = newVNode.$attrs$) === null || _a === void 0 ? void 0 : _a.ref;\n      // check if we've got an old vnode for this slot\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode.$tag$ && oldParentVNode.$elm$) {\n        {\n          // we've got an old slot vnode and the wrapper is being replaced\n          // so let's move the old slot content back to its original location\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n    }\n  }\n  return elm;\n};\nconst putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm['s-sr'] && BUILD.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node['s-sn'] === parentElm['s-sn'] && node['s-sh'] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i = oldSlotChildNodes.length - 1; i >= 0; i--) {\n    const childNode = oldSlotChildNodes[i];\n    if (childNode['s-hn'] !== hostTagName && childNode['s-ol']) {\n      // and relocate it back to it's original location\n      parentReferenceNode(childNode).insertBefore(childNode, referenceNode(childNode));\n      // remove the old original location comment entirely\n      // later on the patch function will know what to do\n      // and move this to the correct spot if need be\n      childNode['s-ol'].remove();\n      childNode['s-ol'] = undefined;\n      // Reset so we can correctly move the node around again.\n      childNode['s-sh'] = undefined;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n};\n/**\n * Create DOM nodes corresponding to a list of {@link d.Vnode} objects and\n * add them to the DOM in the appropriate place.\n *\n * @param parentElm the DOM node which should be used as a parent for the new\n * DOM nodes\n * @param before a child of the `parentElm` which the new children should be\n * inserted before (optional)\n * @param parentVNode the parent virtual DOM node\n * @param vnodes the new child virtual DOM nodes to produce DOM nodes for\n * @param startIdx the index in the child virtual DOM nodes at which to start\n * creating DOM nodes (inclusive)\n * @param endIdx the index in the child virtual DOM nodes at which to stop\n * creating DOM nodes (inclusive)\n */\nconst addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = parentElm['s-cr'] && parentElm['s-cr'].parentNode || parentElm;\n  let childNode;\n  if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        containerElm.insertBefore(childNode, referenceNode(before));\n      }\n    }\n  }\n};\n/**\n * Remove the DOM elements corresponding to a list of {@link d.VNode} objects.\n * This can be used to, for instance, clean up after a list of children which\n * should no longer be shown.\n *\n * This function also handles some of Stencil's slot relocation logic.\n *\n * @param vnodes a list of virtual DOM nodes to remove\n * @param startIdx the index at which to start removing nodes (inclusive)\n * @param endIdx the index at which to stop removing nodes (inclusive)\n */\nconst removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        {\n          // we're removing this element\n          // so it's possible we need to show slot fallback content now\n          checkSlotFallbackVisibility = true;\n          if (elm['s-ol']) {\n            // remove the original location comment\n            elm['s-ol'].remove();\n          } else {\n            // it's possible that child nodes of the node\n            // that's being removed are slot nodes\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        // remove the vnode's element from the dom\n        elm.remove();\n      }\n    }\n  }\n};\n/**\n * Reconcile the children of a new VNode with the children of an old VNode by\n * traversing the two collections of children, identifying nodes that are\n * conserved or changed, calling out to `patch` to make any necessary\n * updates to the DOM, and rearranging DOM nodes as needed.\n *\n * The algorithm for reconciling children works by analyzing two 'windows' onto\n * the two arrays of children (`oldCh` and `newCh`). We keep track of the\n * 'windows' by storing start and end indices and references to the\n * corresponding array entries. Initially the two 'windows' are basically equal\n * to the entire array, but we progressively narrow the windows until there are\n * no children left to update by doing the following:\n *\n * 1. Skip any `null` entries at the beginning or end of the two arrays, so\n *    that if we have an initial array like the following we'll end up dealing\n *    only with a window bounded by the highlighted elements:\n *\n *    [null, null, VNode1 , ... , VNode2, null, null]\n *                 ^^^^^^         ^^^^^^\n *\n * 2. Check to see if the elements at the head and tail positions are equal\n *    across the windows. This will basically detect elements which haven't\n *    been added, removed, or changed position, i.e. if you had the following\n *    VNode elements (represented as HTML):\n *\n *    oldVNode: `<div><p><span>HEY</span></p></div>`\n *    newVNode: `<div><p><span>THERE</span></p></div>`\n *\n *    Then when comparing the children of the `<div>` tag we check the equality\n *    of the VNodes corresponding to the `<p>` tags and, since they are the\n *    same tag in the same position, we'd be able to avoid completely\n *    re-rendering the subtree under them with a new DOM element and would just\n *    call out to `patch` to handle reconciling their children and so on.\n *\n * 3. Check, for both windows, to see if the element at the beginning of the\n *    window corresponds to the element at the end of the other window. This is\n *    a heuristic which will let us identify _some_ situations in which\n *    elements have changed position, for instance it _should_ detect that the\n *    children nodes themselves have not changed but merely moved in the\n *    following example:\n *\n *    oldVNode: `<div><element-one /><element-two /></div>`\n *    newVNode: `<div><element-two /><element-one /></div>`\n *\n *    If we find cases like this then we also need to move the concrete DOM\n *    elements corresponding to the moved children to write the re-order to the\n *    DOM.\n *\n * 4. Finally, if VNodes have the `key` attribute set on them we check for any\n *    nodes in the old children which have the same key as the first element in\n *    our window on the new children. If we find such a node we handle calling\n *    out to `patch`, moving relevant DOM nodes, and so on, in accordance with\n *    what we find.\n *\n * Finally, once we've narrowed our 'windows' to the point that either of them\n * collapse (i.e. they have length 0) we then handle any remaining VNode\n * insertion or deletion that needs to happen to get a DOM state that correctly\n * reflects the new child VNodes. If, for instance, after our window on the old\n * children has collapsed we still have more nodes on the new children that\n * we haven't dealt with yet then we need to add them, or if the new children\n * collapse but we still have unhandled _old_ children then we need to make\n * sure the corresponding DOM nodes are removed.\n *\n * @param parentElm the node into which the parent VNode is rendered\n * @param oldCh the old children of the parent node\n * @param newVNode the new VNode which will replace the parent\n * @param newCh the new children of the parent node\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst updateChildren = (parentElm, oldCh, newVNode, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      // VNode might have been moved left\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      // if the start nodes are the same then we should patch the new VNode\n      // onto the old one, and increment our `newStartIdx` and `oldStartIdx`\n      // indices to reflect that. We don't need to move any DOM Nodes around\n      // since things are matched up in order.\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      // likewise, if the end nodes are the same we patch new onto old and\n      // decrement our end indices, and also likewise in this case we don't\n      // need to move any DOM Nodes.\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      // case: \"Vnode moved right\"\n      //\n      // We've found that the last node in our window on the new children is\n      // the same VNode as the _first_ node in our window on the old children\n      // we're dealing with now. Visually, this is the layout of these two\n      // nodes:\n      //\n      // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n      //                                    ^^^^^^^^^^^\n      // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n      //              ^^^^^^^^^^^^^\n      //\n      // In this situation we need to patch `newEndVnode` onto `oldStartVnode`\n      // and move the DOM element for `oldStartVnode`.\n      if (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot') {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      // We need to move the element for `oldStartVnode` into a position which\n      // will be appropriate for `newEndVnode`. For this we can use\n      // `.insertBefore` and `oldEndVnode.$elm$.nextSibling`. If there is a\n      // sibling for `oldEndVnode.$elm$` then we want to move the DOM node for\n      // `oldStartVnode` between `oldEndVnode` and it's sibling, like so:\n      //\n      // <old-start-node />\n      // <some-intervening-node />\n      // <old-end-node />\n      // <!-- ->              <-- `oldStartVnode.$elm$` should be inserted here\n      // <next-sibling />\n      //\n      // If instead `oldEndVnode.$elm$` has no sibling then we just want to put\n      // the node for `oldStartVnode` at the end of the children of\n      // `parentElm`. Luckily, `Node.nextSibling` will return `null` if there\n      // aren't any siblings, and passing `null` to `Node.insertBefore` will\n      // append it to the children of the parent element.\n      parentElm.insertBefore(oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      // case: \"Vnode moved left\"\n      //\n      // We've found that the first node in our window on the new children is\n      // the same VNode as the _last_ node in our window on the old children.\n      // Visually, this is the layout of these two nodes:\n      //\n      // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n      //              ^^^^^^^^^^^^^\n      // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n      //                                    ^^^^^^^^^^^\n      //\n      // In this situation we need to patch `newStartVnode` onto `oldEndVnode`\n      // (which will handle updating any changed attributes, reconciling their\n      // children etc) but we also need to move the DOM node to which\n      // `oldEndVnode` corresponds.\n      if (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot') {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      // We've already checked above if `oldStartVnode` and `newStartVnode` are\n      // the same node, so since we're here we know that they are not. Thus we\n      // can move the element for `oldEndVnode` _before_ the element for\n      // `oldStartVnode`, leaving `oldStartVnode` to be reconciled in the\n      // future.\n      parentElm.insertBefore(oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      // Here we do some checks to match up old and new nodes based on the\n      // `$key$` attribute, which is set by putting a `key=\"my-key\"` attribute\n      // in the JSX for a DOM element in the implementation of a Stencil\n      // component.\n      //\n      // First we check to see if there are any nodes in the array of old\n      // children which have the same key as the first node in the new\n      // children.\n      idxInOld = -1;\n      {\n        for (i = oldStartIdx; i <= oldEndIdx; ++i) {\n          if (oldCh[i] && oldCh[i].$key$ !== null && oldCh[i].$key$ === newStartVnode.$key$) {\n            idxInOld = i;\n            break;\n          }\n        }\n      }\n      if (idxInOld >= 0) {\n        // We found a node in the old children which matches up with the first\n        // node in the new children! So let's deal with that\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          // the tag doesn't match so we'll need a new DOM element\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          // invalidate the matching old node so that we won't try to update it\n          // again later on\n          oldCh[idxInOld] = undefined;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        // We either didn't find an element in the old children that matches\n        // the key of the first new child OR the build is not using `key`\n        // attributes at all. In either case we need to create a new element\n        // for the new node.\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        // if we created a new node then handle inserting it to the DOM\n        {\n          parentReferenceNode(oldStartVnode.$elm$).insertBefore(node, referenceNode(oldStartVnode.$elm$));\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    // we have some more new nodes to add which don't match up with old nodes\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode, newCh, newStartIdx, newEndIdx);\n  } else if (newStartIdx > newEndIdx) {\n    // there are nodes in the `oldCh` array which no longer correspond to nodes\n    // in the new array, so lets remove them (which entails cleaning up the\n    // relevant DOM nodes)\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\n/**\n * Compare two VNodes to determine if they are the same\n *\n * **NB**: This function is an equality _heuristic_ based on the available\n * information set on the two VNodes and can be misleading under certain\n * circumstances. In particular, if the two nodes do not have `key` attrs\n * (available under `$key$` on VNodes) then the function falls back on merely\n * checking that they have the same tag.\n *\n * So, in other words, if `key` attrs are not set on VNodes which may be\n * changing order within a `children` array or something along those lines then\n * we could obtain a false negative and then have to do needless re-rendering\n * (i.e. we'd say two VNodes aren't equal when in fact they should be).\n *\n * @param leftVNode the first VNode to check\n * @param rightVNode the second VNode to check\n * @param isInitialRender whether or not this is the first render of the vdom\n * @returns whether they're equal or not\n */\nconst isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  // compare if two vnode to see if they're \"technically\" the same\n  // need to have the same element tag, and same key to be the same\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (leftVNode.$tag$ === 'slot') {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    // this will be set if JSX tags in the build have `key` attrs set on them\n    // we only want to check this if we're not on the first render since on\n    // first render `leftVNode.$key$` will always be `null`, so we can be led\n    // astray and, for instance, accidentally delete a DOM node that we want to\n    // keep around.\n    if (!isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nconst referenceNode = node => {\n  // this node was relocated to a new location in the dom\n  // because of some other component's slot\n  // but we still have an html comment in place of where\n  // it's original location was according to it's original vdom\n  return node && node['s-ol'] || node;\n};\nconst parentReferenceNode = node => (node['s-ol'] ? node['s-ol'] : node).parentNode;\n/**\n * Handle reconciling an outdated VNode with a new one which corresponds to\n * it. This function handles flushing updates to the DOM and reconciling the\n * children of the two nodes (if any).\n *\n * @param oldVNode an old VNode whose DOM element and children we want to update\n * @param newVNode a new VNode representing an updated version of the old one\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst patch = (oldVNode, newVNode, isInitialRender = false) => {\n  const elm = newVNode.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode.$children$;\n  const tag = newVNode.$tag$;\n  const text = newVNode.$text$;\n  let defaultHolder;\n  if (text === null) {\n    {\n      // test if we're rendering an svg element, or still rendering nodes inside of one\n      // only add this to the when the compiler sees we're using an svg somewhere\n      isSvgMode = tag === 'svg' ? true : tag === 'foreignObject' ? false : isSvgMode;\n    }\n    {\n      if (tag === 'slot' && !useNativeShadowDom) ;else {\n        // either this is the first render of an element OR it's an update\n        // AND we already know it's possible it could have changed\n        // this updates the element's css classes, attrs, props, listeners, etc.\n        updateElement(oldVNode, newVNode, isSvgMode);\n      }\n    }\n    if (oldChildren !== null && newChildren !== null) {\n      // looks like there's child vnodes for both the old and new vnodes\n      // so we need to call `updateChildren` to reconcile them\n      updateChildren(elm, oldChildren, newVNode, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      // no old child vnodes, but there are new child vnodes to add\n      if (oldVNode.$text$ !== null) {\n        // the old vnode was text, so be sure to clear it out\n        elm.textContent = '';\n      }\n      // add the new vnode children\n      addVnodes(elm, null, newVNode, newChildren, 0, newChildren.length - 1);\n    } else if (oldChildren !== null) {\n      // no new child vnodes, but there are old child vnodes to remove\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (isSvgMode && tag === 'svg') {\n      isSvgMode = false;\n    }\n  } else if (defaultHolder = elm['s-cr']) {\n    // this element has slotted content\n    defaultHolder.parentNode.textContent = text;\n  } else if (oldVNode.$text$ !== text) {\n    // update the text content for the text only vnode\n    // and also only if the text is different than before\n    elm.data = text;\n  }\n};\n/**\n * Adjust the `.hidden` property as-needed on any nodes in a DOM subtree which\n * are slot fallbacks nodes.\n *\n * A slot fallback node should be visible by default. Then, it should be\n * conditionally hidden if:\n *\n * - it has a sibling with a `slot` property set to its slot name or if\n * - it is a default fallback slot node, in which case we hide if it has any\n *   content\n *\n * @param elm the element of interest\n */\nconst updateFallbackSlotVisibility = elm => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n      if (childNode['s-sr']) {\n        // this is a slot fallback node\n        // get the slot name for this slot reference node\n        const slotName = childNode['s-sn'];\n        // by default always show a fallback slot node\n        // then hide it if there are other slots in the light dom\n        childNode.hidden = false;\n        // we need to check all of its sibling nodes in order to see if\n        // `childNode` should be hidden\n        for (const siblingNode of childNodes) {\n          // Don't check the node against itself\n          if (siblingNode !== childNode) {\n            if (siblingNode['s-hn'] !== childNode['s-hn'] || slotName !== '') {\n              // this sibling node is from a different component OR is a named\n              // fallback slot node\n              if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ && (slotName === siblingNode.getAttribute('slot') || slotName === siblingNode['s-sn'])) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              // this is a default fallback slot node\n              // any element or text node (with content)\n              // should hide the default fallback slot node\n              if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ || siblingNode.nodeType === 3 /* NODE_TYPE.TextNode */ && siblingNode.textContent.trim() !== '') {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      // keep drilling down\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\n/**\n * Component-global information about nodes which are either currently being\n * relocated or will be shortly.\n */\nconst relocateNodes = [];\n/**\n * Mark the contents of a slot for relocation via adding references to them to\n * the {@link relocateNodes} data structure. The actual work of relocating them\n * will then be handled in {@link renderVdom}.\n *\n * @param elm a render node whose child nodes need to be relocated\n */\nconst markSlotContentForRelocation = elm => {\n  // tslint:disable-next-line: prefer-const\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    // we need to find child nodes which are slot references so we can then try\n    // to match them up with nodes that need to be relocated\n    if (childNode['s-sr'] && (node = childNode['s-cr']) && node.parentNode) {\n      // first get the content reference comment node ('s-cr'), then we get\n      // its parent, which is where all the host content is now\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode['s-sn'];\n      // iterate through all the nodes under the location where the host was\n      // originally rendered\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        // check that the node is not a content reference node or a node\n        // reference and then check that the host name does not match that of\n        // childNode.\n        // In addition, check that the slot either has not already been relocated, or\n        // that its current location's host is not childNode's host. This is essentially\n        // a check so that we don't try to relocate (and then hide) a node that is already\n        // where it should be.\n        if (!node['s-cn'] && !node['s-nr'] && node['s-hn'] !== childNode['s-hn'] && !BUILD.experimentalSlotFixes) {\n          // if `node` is located in the slot that `childNode` refers to (via the\n          // `'s-sn'` property) then we need to relocate it from it's current spot\n          // (under the host element parent) to the right slot location\n          if (isNodeLocatedInSlot(node, slotName)) {\n            // it's possible we've already decided to relocate this node\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            // made some changes to slots\n            // let's make sure we also double check\n            // fallbacks are correctly hidden or shown\n            checkSlotFallbackVisibility = true;\n            // ensure that the slot-name attr is correct\n            node['s-sn'] = node['s-sn'] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$['s-sh'] = childNode['s-hn'];\n              // we marked this node for relocation previously but didn't find\n              // out the slot reference node to which it needs to be relocated\n              // so write it down now!\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node['s-sh'] = childNode['s-hn'];\n              // add to our list of nodes to relocate\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node['s-sr']) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node['s-sn'])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            // the node is not found within the slot (`childNode`) that we're\n            // currently looking at, so we stick it into `relocateNodes` to\n            // handle later. If we never find a home for this element then\n            // we'll need to hide it\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    // if we're dealing with any type of element (capable of itself being a\n    // slot reference or containing one) then we recur\n    if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\n/**\n * Check whether a node is located in a given named slot.\n *\n * @param nodeToRelocate the node of interest\n * @param slotName the slot name to check\n * @returns whether the node is located in the slot or not\n */\nconst isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n    if (nodeToRelocate.getAttribute('slot') === null && slotName === '') {\n      // if the node doesn't have a slot attribute, and the slot we're checking\n      // is not a named slot, then we assume the node should be within the slot\n      return true;\n    }\n    if (nodeToRelocate.getAttribute('slot') === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate['s-sn'] === slotName) {\n    return true;\n  }\n  return slotName === '';\n};\n/**\n * 'Nullify' any VDom `ref` callbacks on a VDom node or its children by calling\n * them with `null`. This signals that the DOM element corresponding to the VDom\n * node has been removed from the DOM.\n *\n * @param vNode a virtual DOM node\n */\nconst nullifyVNodeRefs = vNode => {\n  {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\n/**\n * The main entry point for Stencil's virtual DOM-based rendering engine\n *\n * Given a {@link d.HostRef} container and some virtual DOM nodes, this\n * function will handle creating a virtual DOM tree with a single root, patching\n * the current virtual DOM tree onto an old one (if any), dealing with slot\n * relocation, and reflecting attributes.\n *\n * @param hostRef data needed to root and render the virtual DOM tree, such as\n * the DOM node into which it should be rendered.\n * @param renderFnResults the virtual DOM nodes to be rendered\n * @param isInitialLoad whether or not this is the first call after page load\n */\nconst renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  // if `renderFnResults` is a Host node then we can use it directly. If not,\n  // we need to call `h` again to wrap the children of our component in a\n  // 'dummy' Host node (well, an empty vnode) since `renderVdom` assumes\n  // implicitly that the top-level vdom node is 1) an only child and 2)\n  // contains attrs that need to be set on the host element.\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  // On the first render and *only* on the first render we want to check for\n  // any attributes set on the host element which are also set on the vdom\n  // node. If we find them, we override the value on the VDom node attrs with\n  // the value from the host element, which allows developers building apps\n  // with Stencil components to override e.g. the `role` attribute on a\n  // component even if it's already set on the `Host`.\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      // We have a special implementation in `setAccessor` for `style` and\n      // `class` which reconciles values coming from the VDom with values\n      // already present on the DOM element, so we don't want to override those\n      // attributes on the VDom tree with values from the host element if they\n      // are present.\n      //\n      // Likewise, `ref` and `key` are special internal values for the Stencil\n      // runtime and we don't want to override those either.\n      if (hostElm.hasAttribute(key) && !['key', 'ref', 'style', 'class'].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* VNODE_FLAGS.isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm;\n  {\n    scopeId = hostElm['s-sc'];\n  }\n  useNativeShadowDom = (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) !== 0;\n  {\n    contentRef = hostElm['s-cr'];\n    // always reset\n    checkSlotFallbackVisibility = false;\n  }\n  // synchronous patch\n  patch(oldVNode, rootVnode, isInitialLoad);\n  {\n    // while we're moving nodes around existing nodes, temporarily disable\n    // the disconnectCallback from working\n    plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate['s-ol']) {\n          // add a reference node marking this node's original location\n          // keep a reference to this node for later lookups\n          const orgLocationNode = doc.createTextNode('');\n          orgLocationNode['s-nr'] = nodeToRelocate;\n          nodeToRelocate.parentNode.insertBefore(nodeToRelocate['s-ol'] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          // When determining where to insert content, the most simple case would be\n          // to relocate the node immediately following the slot reference node. We do this\n          // by getting a reference to the node immediately following the slot reference node\n          // since we will use `insertBefore` to manipulate the DOM.\n          //\n          // If there is no node immediately following the slot reference node, then we will just\n          // end up appending the node as the last child of the parent.\n          let insertBeforeNode = slotRefNode.nextSibling;\n          // If the node we're currently planning on inserting the new node before is an element,\n          // we need to do some additional checks to make sure we're inserting the node in the correct order.\n          // The use case here would be that we have multiple nodes being relocated to the same slot. So, we want\n          // to make sure they get inserted into their new home in the same order they were declared in their original location.\n          //\n          // TODO(STENCIL-914): Remove `experimentalSlotFixes` check\n          {\n            let orgLocationNode = (_a = nodeToRelocate['s-ol']) === null || _a === void 0 ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode['s-nr']) !== null && _b !== void 0 ? _b : null;\n              if (refNode && refNode['s-sn'] === nodeToRelocate['s-sn'] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                // If the refNode is the same node to be relocated or another element's slot reference, keep searching to find the\n                // correct relocation target\n                while (refNode === nodeToRelocate || (refNode === null || refNode === void 0 ? void 0 : refNode['s-sr'])) {\n                  refNode = refNode === null || refNode === void 0 ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode['s-nr']) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            // we've checked that it's worth while to relocate\n            // since that the node to relocate\n            // has a different next sibling or parent relocated\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!nodeToRelocate['s-hn'] && nodeToRelocate['s-ol']) {\n                // probably a component in the index.html that doesn't have its hostname set\n                nodeToRelocate['s-hn'] = nodeToRelocate['s-ol'].parentNode.nodeName;\n              }\n              // Add it back to the dom but in its new home\n              // If we get to this point and `insertBeforeNode` is `null`, that means\n              // we're just going to append the node as the last child of the parent. Passing\n              // `null` as the second arg here will trigger that behavior.\n              parentNodeRef.insertBefore(nodeToRelocate, insertBeforeNode);\n              // Reset the `hidden` value back to what it was defined as originally\n              // This solves a problem where a `slot` is dynamically rendered and `hidden` may have\n              // been set on content originally, but now it has a slot to go to so it should have\n              // the value it was defined as having in the DOM, not what we overrode it to.\n              if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate['s-ih']) !== null && _c !== void 0 ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode['s-rf'] === 'function' && slotRefNode['s-rf'](nodeToRelocate);\n        } else {\n          // this node doesn't have a slot home to go to, so let's hide it\n          if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n            // Store the initial value of `hidden` so we can reset it later when\n            // moving nodes around.\n            if (isInitialLoad) {\n              nodeToRelocate['s-ih'] = (_d = nodeToRelocate.hidden) !== null && _d !== void 0 ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    // done moving nodes around\n    // allow the disconnect callback to work again\n    plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    // always reset\n    relocateNodes.length = 0;\n  }\n  // Clear the content ref so we don't create a memory leak\n  contentRef = undefined;\n};\nconst attachToAncestor = (hostRef, ancestorComponent) => {\n  if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent['s-p']) {\n    ancestorComponent['s-p'].push(new Promise(r => hostRef.$onRenderResolve$ = r));\n  }\n};\nconst scheduleUpdate = (hostRef, isInitialLoad) => {\n  {\n    hostRef.$flags$ |= 16 /* HOST_FLAGS.isQueuedForUpdate */;\n  }\n  if (hostRef.$flags$ & 4 /* HOST_FLAGS.isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* HOST_FLAGS.needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  // there is no ancestor component or the ancestor component\n  // has already fired off its lifecycle update then\n  // fire off the initial update\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return writeTask(dispatch);\n};\n/**\n * Dispatch initial-render and update lifecycle hooks, enqueuing calls to\n * component lifecycle methods like `componentWillLoad` as well as\n * {@link updateComponent}, which will kick off the virtual DOM re-render.\n *\n * @param hostRef a reference to a host DOM node\n * @param isInitialLoad whether we're on the initial load or not\n * @returns an empty Promise which is used to enqueue a series of operations for\n * the component\n */\nconst dispatchHooks = (hostRef, isInitialLoad) => {\n  const endSchedule = createTime('scheduleUpdate', hostRef.$cmpMeta$.$tagName$);\n  const instance = hostRef.$lazyInstance$;\n  // We're going to use this variable together with `enqueue` to implement a\n  // little promise-based queue. We start out with it `undefined`. When we add\n  // the first function to the queue we'll set this variable to be that\n  // function's return value. When we attempt to add subsequent values to the\n  // queue we'll check that value and, if it was a `Promise`, we'll then chain\n  // the new function off of that `Promise` using `.then()`. This will give our\n  // queue two nice properties:\n  //\n  // 1. If all functions added to the queue are synchronous they'll be called\n  //    synchronously right away.\n  // 2. If all functions added to the queue are asynchronous they'll all be\n  //    called in order after `dispatchHooks` exits.\n  let maybePromise;\n  if (isInitialLoad) {\n    {\n      hostRef.$flags$ |= 256 /* HOST_FLAGS.isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = undefined;\n      }\n    }\n    {\n      // If `componentWillLoad` returns a `Promise` then we want to wait on\n      // whatever's going on in that `Promise` before we launch into\n      // rendering the component, doing other lifecycle stuff, etc. So\n      // in that case we assign the returned promise to the variable we\n      // declared above to hold a possible 'queueing' Promise\n      maybePromise = safeCall(instance, 'componentWillLoad');\n    }\n  }\n  {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, 'componentWillRender'));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\n/**\n * This function uses a Promise to implement a simple first-in, first-out queue\n * of functions to be called.\n *\n * The queue is ordered on the basis of the first argument. If it's\n * `undefined`, then nothing is on the queue yet, so the provided function can\n * be called synchronously (although note that this function may return a\n * `Promise`). The idea is that then the return value of that enqueueing\n * operation is kept around, so that if it was a `Promise` then subsequent\n * functions can be enqueued by calling this function again with that `Promise`\n * as the first argument.\n *\n * @param maybePromise either a `Promise` which should resolve before the next function is called or an 'empty' sentinel\n * @param fn a function to enqueue\n * @returns either a `Promise` or the return value of the provided function\n */\nconst enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn) : fn();\n/**\n * Check that a value is a `Promise`. To check, we first see if the value is an\n * instance of the `Promise` global. In a few circumstances, in particular if\n * the global has been overwritten, this is could be misleading, so we also do\n * a little 'duck typing' check to see if the `.then` property of the value is\n * defined and a function.\n *\n * @param maybePromise it might be a promise!\n * @returns whether it is or not\n */\nconst isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === 'function';\n/**\n * Update a component given reference to its host elements and so on.\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param isInitialLoad whether or not this function is being called as part of\n * the first render cycle\n */\nconst updateComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (hostRef, instance, isInitialLoad) {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime('update', hostRef.$cmpMeta$.$tagName$);\n    const rc = elm['s-rc'];\n    if (isInitialLoad) {\n      // DOM WRITE!\n      attachStyles(hostRef);\n    }\n    const endRender = createTime('render', hostRef.$cmpMeta$.$tagName$);\n    {\n      callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (rc) {\n      // ok, so turns out there are some child host elements\n      // waiting on this parent element to load\n      // let's fire off all update callbacks waiting\n      rc.map(cb => cb());\n      elm['s-rc'] = undefined;\n    }\n    endRender();\n    endUpdate();\n    {\n      const childrenPromises = (_a = elm['s-p']) !== null && _a !== void 0 ? _a : [];\n      const postUpdate = () => postUpdateComponent(hostRef);\n      if (childrenPromises.length === 0) {\n        postUpdate();\n      } else {\n        Promise.all(childrenPromises).then(postUpdate);\n        hostRef.$flags$ |= 4 /* HOST_FLAGS.isWaitingForChildren */;\n        childrenPromises.length = 0;\n      }\n    }\n  });\n  return function updateComponent(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Handle making the call to the VDom renderer with the proper context given\n * various build variables\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param elm the Host element for the component\n * @param isInitialLoad whether or not this function is being called as part of\n * @returns an empty promise\n */\nconst callRender = (hostRef, instance, elm, isInitialLoad) => {\n  try {\n    /**\n     * minification optimization: `allRenderFn` is `true` if all components have a `render`\n     * method, so we can call the method immediately. If not, check before calling it.\n     */\n    instance = instance.render && instance.render();\n    {\n      hostRef.$flags$ &= ~16 /* HOST_FLAGS.isQueuedForUpdate */;\n    }\n    {\n      hostRef.$flags$ |= 2 /* HOST_FLAGS.hasRendered */;\n    }\n    {\n      {\n        // looks like we've got child nodes to render into this host element\n        // or we need to update the css class/attrs on the host element\n        // DOM WRITE!\n        {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  return null;\n};\nconst postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime('postUpdate', tagName);\n  const instance = hostRef.$lazyInstance$;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  {\n    safeCall(instance, 'componentDidRender');\n  }\n  if (!(hostRef.$flags$ & 64 /* HOST_FLAGS.hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* HOST_FLAGS.hasLoadedComponent */;\n    {\n      // DOM WRITE!\n      addHydratedFlag(elm);\n    }\n    {\n      safeCall(instance, 'componentDidLoad');\n    }\n    endPostUpdate();\n    {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad();\n      }\n    }\n  } else {\n    {\n      safeCall(instance, 'componentDidUpdate');\n    }\n    endPostUpdate();\n  }\n  {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  // load events fire from bottom to top\n  // the deepest elements load first then bubbles up\n  {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = undefined;\n    }\n    if (hostRef.$flags$ & 512 /* HOST_FLAGS.needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* HOST_FLAGS.isWaitingForChildren */ | 512 /* HOST_FLAGS.needsRerender */);\n  }\n  // ( •_•)\n  // ( •_•)>⌐■-■\n  // (⌐■_■)\n};\nconst forceUpdate = ref => {\n  {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    // Returns \"true\" when the forced update was successfully scheduled\n    return isConnected;\n  }\n};\nconst appDidLoad = who => {\n  // on appload\n  // we have finish the first big initial render\n  {\n    addHydratedFlag(doc.documentElement);\n  }\n  nextTick(() => emitEvent(win, 'appload', {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n};\n/**\n * Allows to safely call a method, e.g. `componentDidLoad`, on an instance,\n * e.g. custom element node. If a build figures out that e.g. no component\n * has a `componentDidLoad` method, the instance method gets removed from the\n * output bundle and this function returns `undefined`.\n * @param instance any object that may or may not contain methods\n * @param method method name\n * @param arg single arbitrary argument\n * @returns result of method call if it exists, otherwise `undefined`\n */\nconst safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return undefined;\n};\nconst addHydratedFlag = elm => elm.classList.add('hydrated');\nconst getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nconst setValue = (ref, propName, newVal, cmpMeta) => {\n  // check our new property value against our internal value\n  const hostRef = getHostRef(ref);\n  const elm = hostRef.$hostElement$;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = hostRef.$lazyInstance$;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  // explicitly check for NaN on both sides, as `NaN === NaN` is always false\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!(flags & 8 /* HOST_FLAGS.isConstructingInstance */) || oldVal === undefined) && didValueChange) {\n    // gadzooks! the property's value has changed!!\n    // set our new value!\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (instance) {\n      // get an array of method names of watch functions to call\n      if (cmpMeta.$watchers$ && flags & 128 /* HOST_FLAGS.isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          // this instance is watching for when this property changed\n          watchMethods.map(watchMethodName => {\n            try {\n              // fire off each of the watch methods that are watching this property\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if ((flags & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n        // looks like this value actually changed, so we've got work to do!\n        // but only if we've already rendered, otherwise just chill out\n        // queue that we need to do an update, but don't worry about queuing\n        // up millions cuz this function ensures it only runs once\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n/**\n * Attach a series of runtime constructs to a compiled Stencil component\n * constructor, including getters and setters for the `@Prop` and `@State`\n * decorators, callbacks for when attributes change, and so on.\n *\n * @param Cstr the constructor for a component that we need to process\n * @param cmpMeta metadata collected previously about the component\n * @param flags a number used to store a series of bit flags\n * @returns a reference to the same constructor passed in (but now mutated)\n */\nconst proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a;\n  const prototype = Cstr.prototype;\n  if (cmpMeta.$members$) {\n    if (Cstr.watchers) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    // It's better to have a const than two Object.entries()\n    const members = Object.entries(cmpMeta.$members$);\n    members.map(([memberName, [memberFlags]]) => {\n      if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ || flags & 2 /* PROXY_FLAGS.proxyState */ && memberFlags & 32 /* MEMBER_FLAGS.State */) {\n        // proxyComponent - prop\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            // proxyComponent, get value\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            // proxyComponent, set value\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (flags & 1 /* PROXY_FLAGS.isElementConstructor */ && memberFlags & 64 /* MEMBER_FLAGS.Method */) {\n        // proxyComponent - method\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a;\n            const ref = getHostRef(this);\n            return (_a = ref === null || ref === void 0 ? void 0 : ref.$onInstancePromise$) === null || _a === void 0 ? void 0 : _a.then(() => {\n              var _a;\n              return (_a = ref.$lazyInstance$) === null || _a === void 0 ? void 0 : _a[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (flags & 1 /* PROXY_FLAGS.isElementConstructor */) {\n      const attrNameToPropName = new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a;\n          const propName = attrNameToPropName.get(attrName);\n          //  In a web component lifecycle the attributeChangedCallback runs prior to connectedCallback\n          //  in the case where an attribute was set inline.\n          //  ```html\n          //    <my-component some-attribute=\"some-value\"></my-component>\n          //  ```\n          //\n          //  There is an edge case where a developer sets the attribute inline on a custom element and then\n          //  programmatically changes it before it has been upgraded as shown below:\n          //\n          //  ```html\n          //    <!-- this component has _not_ been upgraded yet -->\n          //    <my-component id=\"test\" some-attribute=\"some-value\"></my-component>\n          //    <script>\n          //      // grab non-upgraded component\n          //      el = document.querySelector(\"#test\");\n          //      el.someAttribute = \"another-value\";\n          //      // upgrade component\n          //      customElements.define('my-component', MyComponent);\n          //    </script>\n          //  ```\n          //  In this case if we do not un-shadow here and use the value of the shadowing property, attributeChangedCallback\n          //  will be called with `newValue = \"some-value\"` and will set the shadowed property (this.someAttribute = \"another-value\")\n          //  to the value that was set inline i.e. \"some-value\" from above example. When\n          //  the connectedCallback attempts to un-shadow it will use \"some-value\" as the initial value rather than \"another-value\"\n          //\n          //  The case where the attribute was NOT set inline but was not set programmatically shall be handled/un-shadowed\n          //  by connectedCallback as this attributeChangedCallback will not fire.\n          //\n          //  https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n          //\n          //  TODO(STENCIL-16) we should think about whether or not we actually want to be reflecting the attributes to\n          //  properties here given that this goes against best practices outlined here\n          //  https://developers.google.com/web/fundamentals/web-components/best-practices#avoid-reentrancy\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === 'number' && this[propName] == newValue) {\n            // if the propName exists on the prototype of `Cstr`, this update may be a result of Stencil using native\n            // APIs to reflect props as attributes. Calls to `setAttribute(someElement, propName)` will result in\n            // `propName` to be converted to a `DOMString`, which may not be what we want for other primitive props.\n            return;\n          } else if (propName == null) {\n            // At this point we should know this is not a \"member\", so we can treat it like watching an attribute\n            // on a vanilla web component\n            const hostRef = getHostRef(this);\n            const flags = hostRef === null || hostRef === void 0 ? void 0 : hostRef.$flags$;\n            // We only want to trigger the callback(s) if:\n            // 1. The instance is ready\n            // 2. The watchers are ready\n            // 3. The value has changed\n            if (flags && !(flags & 8 /* HOST_FLAGS.isConstructingInstance */) && flags & 128 /* HOST_FLAGS.isWatchReady */ && newValue !== oldValue) {\n              const instance = hostRef.$lazyInstance$;\n              const entry = (_a = cmpMeta.$watchers$) === null || _a === void 0 ? void 0 : _a[attrName];\n              entry === null || entry === void 0 ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === 'boolean' ? false : newValue;\n        });\n      };\n      // Create an array of attributes to observe\n      // This list in comprised of all strings used within a `@Watch()` decorator\n      // on a component as well as any Stencil-specific \"members\" (`@Prop()`s and `@State()`s).\n      // As such, there is no way to guarantee type-safety here that a user hasn't entered\n      // an invalid attribute.\n      Cstr.observedAttributes = Array.from(new Set([...Object.keys((_a = cmpMeta.$watchers$) !== null && _a !== void 0 ? _a : {}), ...members.filter(([_, m]) => m[0] & 15 /* MEMBER_FLAGS.HasAttribute */).map(([propName, m]) => {\n        var _a;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (m[0] & 512 /* MEMBER_FLAGS.ReflectAttr */) {\n          (_a = cmpMeta.$attrsToReflect$) === null || _a === void 0 ? void 0 : _a.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n/**\n * Initialize a Stencil component given a reference to its host element, its\n * runtime bookkeeping data structure, runtime metadata about the component,\n * and (optionally) an HMR version ID.\n *\n * @param elm a host element\n * @param hostRef the element's runtime bookkeeping object\n * @param cmpMeta runtime metadata for the Stencil component\n * @param hmrVersionId an (optional) HMR version ID\n */\nconst initializeComponent = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (elm, hostRef, cmpMeta, hmrVersionId) {\n    let Cstr;\n    // initializeComponent\n    if ((hostRef.$flags$ & 32 /* HOST_FLAGS.hasInitializedComponent */) === 0) {\n      // Let the runtime know that the component has been initialized\n      hostRef.$flags$ |= 32 /* HOST_FLAGS.hasInitializedComponent */;\n      const bundleId = cmpMeta.$lazyBundleId$;\n      if (bundleId) {\n        // lazy loaded components\n        // request the component's implementation to be\n        // wired up with the host element\n        Cstr = loadModule(cmpMeta);\n        if (Cstr.then) {\n          // Await creates a micro-task avoid if possible\n          const endLoad = uniqueTime();\n          Cstr = yield Cstr;\n          endLoad();\n        }\n        if (!Cstr.isProxied) {\n          // we've never proxied this Constructor before\n          // let's add the getters/setters to its prototype before\n          // the first time we create an instance of the implementation\n          {\n            cmpMeta.$watchers$ = Cstr.watchers;\n          }\n          proxyComponent(Cstr, cmpMeta, 2 /* PROXY_FLAGS.proxyState */);\n          Cstr.isProxied = true;\n        }\n        const endNewInstance = createTime('createInstance', cmpMeta.$tagName$);\n        // ok, time to construct the instance\n        // but let's keep track of when we start and stop\n        // so that the getters/setters don't incorrectly step on data\n        {\n          hostRef.$flags$ |= 8 /* HOST_FLAGS.isConstructingInstance */;\n        }\n        // construct the lazy-loaded component implementation\n        // passing the hostRef is very important during\n        // construction in order to directly wire together the\n        // host element and the lazy-loaded instance\n        try {\n          new Cstr(hostRef);\n        } catch (e) {\n          consoleError(e);\n        }\n        {\n          hostRef.$flags$ &= ~8 /* HOST_FLAGS.isConstructingInstance */;\n        }\n        {\n          hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */;\n        }\n        endNewInstance();\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else {\n        // sync constructor component\n        Cstr = elm.constructor;\n        // wait for the CustomElementRegistry to mark the component as ready before setting `isWatchReady`. Otherwise,\n        // watchers may fire prematurely if `customElements.get()`/`customElements.whenDefined()` resolves _before_\n        // Stencil has completed instantiating the component.\n        customElements.whenDefined(cmpMeta.$tagName$).then(() => hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */);\n      }\n      if (Cstr.style) {\n        // this component has styles but we haven't registered them yet\n        let style = Cstr.style;\n        if (typeof style !== 'string') {\n          style = style[hostRef.$modeName$ = computeMode(elm)];\n        }\n        const scopeId = getScopeId(cmpMeta, hostRef.$modeName$);\n        if (!styles.has(scopeId)) {\n          const endRegisterStyles = createTime('registerStyles', cmpMeta.$tagName$);\n          registerStyle(scopeId, style, !!(cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */));\n          endRegisterStyles();\n        }\n      }\n    }\n    // we've successfully created a lazy instance\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (ancestorComponent && ancestorComponent['s-rc']) {\n      // this is the initial load and this component it has an ancestor component\n      // but the ancestor component has NOT fired its will update lifecycle yet\n      // so let's just cool our jets and wait for the ancestor to continue first\n      // this will get fired off when the ancestor component\n      // finally gets around to rendering its lazy self\n      // fire off the initial update\n      ancestorComponent['s-rc'].push(schedule);\n    } else {\n      schedule();\n    }\n  });\n  return function initializeComponent(_x4, _x5, _x6, _x7) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nconst fireConnectedCallback = instance => {\n  {\n    safeCall(instance, 'connectedCallback');\n  }\n};\nconst connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime('connectedCallback', cmpMeta.$tagName$);\n    if (!(hostRef.$flags$ & 1 /* HOST_FLAGS.hasConnected */)) {\n      // first time this component has connected\n      hostRef.$flags$ |= 1 /* HOST_FLAGS.hasConnected */;\n      let hostId;\n      {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n            const scopeId = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute('s-mode'));\n            elm.classList.remove(scopeId + '-h', scopeId + '-s');\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (!hostId) {\n        // initUpdate\n        // if the slot polyfill is required we'll need to put some nodes\n        // in here to act as original content anchors as we move nodes around\n        // host element has been connected to the DOM\n        if (\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* CMP_FLAGS.hasSlotRelocation */ | 8 /* CMP_FLAGS.needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      {\n        // find the first ancestor component (if there is one) and register\n        // this component as one of the actively loading child components for its ancestor\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          // climb up the ancestors looking for the first\n          // component that hasn't finished its lifecycle update yet\n          if (ancestorComponent.nodeType === 1 /* NODE_TYPE.ElementNode */ && ancestorComponent.hasAttribute('s-id') && ancestorComponent['s-p'] || ancestorComponent['s-p']) {\n            // we found this components first ancestor component\n            // keep a reference to this component's ancestor component\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      // Lazy properties\n      // https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n      if (cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      // not the first time this has connected\n      // reattach any event listeners to the host\n      // since they would have been removed when disconnected\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n      // fire off connectedCallback() on component instance\n      if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nconst setContentReference = elm => {\n  // only required when we're NOT using native shadow dom (slot)\n  // or this browser doesn't support native shadow dom\n  // and this host element was NOT created with SSR\n  // let's pick out the inner content for slot projection\n  // create a node to represent where the original\n  // content was first placed, which is useful later on\n  const contentRefElm = elm['s-cr'] = doc.createComment('');\n  contentRefElm['s-cn'] = true;\n  elm.insertBefore(contentRefElm, elm.firstChild);\n};\nconst disconnectInstance = instance => {\n  {\n    safeCall(instance, 'disconnectedCallback');\n  }\n};\nconst disconnectedCallback = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (elm) {\n    if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n      const hostRef = getHostRef(elm);\n      {\n        if (hostRef.$rmListeners$) {\n          hostRef.$rmListeners$.map(rmListener => rmListener());\n          hostRef.$rmListeners$ = undefined;\n        }\n      }\n      if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n        disconnectInstance(hostRef.$lazyInstance$);\n      } else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n      }\n    }\n  });\n  return function disconnectedCallback(_x8) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nconst bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  const endBootstrap = createTime();\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements = win.customElements;\n  const head = doc.head;\n  const metaCharset = /*@__PURE__*/head.querySelector('meta[charset]');\n  const dataStyles = /*@__PURE__*/doc.createElement('style');\n  const deferredConnectedCallbacks = [];\n  const styles = /*@__PURE__*/doc.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n  let appLoadFallback;\n  let isBootstrapping = true;\n  let i = 0;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || './', doc.baseURI).href;\n  {\n    // If the app is already hydrated there is not point to disable the\n    // async queue. This will improve the first input delay\n    plt.$flags$ |= 2 /* PLATFORM_FLAGS.appLoaded */;\n  }\n  {\n    for (; i < styles.length; i++) {\n      registerStyle(styles[i].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles[i].innerHTML), true);\n    }\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      // Check if we are using slots outside the shadow DOM in this component.\n      // We'll use this information later to add styles for `slot-fb` elements\n      if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      {\n        cmpMeta.$watchers$ = (_a = compactMeta[4]) !== null && _a !== void 0 ? _a : {};\n      }\n      const tagName = cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          // @ts-ignore\n          super(self);\n          self = this;\n          registerHost(self, cmpMeta);\n          if (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n            // this component is using shadow dom\n            // and this browser supports shadow dom\n            // add the read-only property \"shadowRoot\" to the host element\n            // adding the shadow root build conditionals to minimize runtime\n            {\n              {\n                self.attachShadow({\n                  mode: 'open',\n                  delegatesFocus: !!(cmpMeta.$flags$ & 16 /* CMP_FLAGS.shadowDelegatesFocus */)\n                });\n              }\n            }\n          }\n        }\n        connectedCallback() {\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            // connectedCallback will be processed once all components have been registered\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* PROXY_FLAGS.isElementConstructor */));\n      }\n    });\n  });\n  // Only bother generating CSS if we have components\n  // TODO(STENCIL-1118): Add test cases for CSS content based on conditionals\n  if (cmpTags.length > 0) {\n    // Add styles for `slot-fb` elements if any of our components are using slots outside the Shadow DOM\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    // Add hydration styles\n    {\n      dataStyles.textContent += cmpTags + HYDRATED_CSS;\n    }\n    // If we have styles, add them to the DOM\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute('data-styles', '');\n      // Apply CSP nonce to the style tag if it exists\n      const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute('nonce', nonce);\n      }\n      // Insert the styles into the document head\n      // NOTE: this _needs_ to happen last so we can ensure the nonce (and other attributes) are applied\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  // Process deferred connectedCallbacks now all components have been registered\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  // Fallback appLoad event\n  endBootstrap();\n};\nconst addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (listeners) {\n    listeners.map(([flags, name, method]) => {\n      const target = getHostListenerTarget(elm, flags);\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nconst hostListenerProxy = (hostRef, methodName) => ev => {\n  try {\n    {\n      if (hostRef.$flags$ & 256 /* HOST_FLAGS.isListenReady */) {\n        // instance is ready, let's call it's member method for this event\n        hostRef.$lazyInstance$[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nconst getHostListenerTarget = (elm, flags) => {\n  if (flags & 4 /* LISTENER_FLAGS.TargetDocument */) return doc;\n  if (flags & 8 /* LISTENER_FLAGS.TargetWindow */) return win;\n  if (flags & 16 /* LISTENER_FLAGS.TargetBody */) return doc.body;\n  return elm;\n};\n// prettier-ignore\nconst hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* LISTENER_FLAGS.Passive */) !== 0,\n  capture: (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0\n} : (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0;\n/**\n * Assigns the given value to the nonce property on the runtime platform object.\n * During runtime, this value is used to set the nonce attribute on all dynamically created script and style tags.\n * @param nonce The value to be assigned to the platform nonce property.\n * @returns void\n */\nconst setNonce = nonce => plt.$nonce$ = nonce;\n/**\n * A WeakMap mapping runtime component references to their corresponding host reference\n * instances.\n *\n * **Note**: If we're in an HMR context we need to store a reference to this\n * value on `window` in order to maintain the mapping of {@link d.RuntimeRef}\n * to {@link d.HostRef} across HMR updates.\n *\n * This is necessary because when HMR updates for a component are processed by\n * the browser-side dev server client the JS bundle for that component is\n * re-fetched. Since the module containing {@link hostRefs} is included in\n * that bundle, if we do not store a reference to it the new iteration of the\n * component will not have access to the previous hostRef map, leading to a\n * bug where the new version of the component cannot properly initialize.\n */\nconst hostRefs = new WeakMap();\n/**\n * Given a {@link d.RuntimeRef} retrieve the corresponding {@link d.HostRef}\n *\n * @param ref the runtime ref of interest\n * @returns the Host reference (if found) or undefined\n */\nconst getHostRef = ref => hostRefs.get(ref);\n/**\n * Register a lazy instance with the {@link hostRefs} object so it's\n * corresponding {@link d.HostRef} can be retrieved later.\n *\n * @param lazyInstance the lazy instance of interest\n * @param hostRef that instances `HostRef` object\n * @returns a reference to the host ref WeakMap\n */\nconst registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\n/**\n * Register a host element for a Stencil component, setting up various metadata\n * and callbacks based on {@link BUILD} flags as well as the component's runtime\n * metadata.\n *\n * @param hostElement the host element to register\n * @param cmpMeta runtime metadata for that component\n * @returns a reference to the host ref WeakMap\n */\nconst registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: new Map()\n  };\n  {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement['s-p'] = [];\n    hostElement['s-rc'] = [];\n  }\n  addHostEventListeners(hostElement, hostRef, cmpMeta.$listeners$);\n  return hostRefs.set(hostElement, hostRef);\n};\nconst isMemberInElement = (elm, memberName) => memberName in elm;\nconst consoleError = (e, el) => (0, console.error)(e, el);\nconst cmpModules = /*@__PURE__*/new Map();\nconst loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  // loadModuleImport\n  const exportName = cmpMeta.$tagName$.replace(/-/g, '_');\n  const bundleId = cmpMeta.$lazyBundleId$;\n  const module = cmpModules.get(bundleId);\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${''}`).then(importedModule => {\n    {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\nconst styles = /*@__PURE__*/new Map();\nconst modeResolutionChain = [];\nconst win = typeof window !== 'undefined' ? window : {};\nconst doc = win.document || {\n  head: {}\n};\nconst plt = {\n  $flags$: 0,\n  $resourcesUrl$: '',\n  jmp: h => h(),\n  raf: h => requestAnimationFrame(h),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nconst setPlatformHelpers = helpers => {\n  Object.assign(plt, helpers);\n};\nconst supportsShadow =\n// TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\ntrue;\nconst supportsListenerOptions = /*@__PURE__*/(() => {\n  let supportsListenerOptions = false;\n  try {\n    doc.addEventListener('e', null, Object.defineProperty({}, 'passive', {\n      get() {\n        supportsListenerOptions = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions;\n})();\nconst promiseResolve = v => Promise.resolve(v);\nconst supportsConstructableStylesheets = /*@__PURE__*/(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === 'function';\n  } catch (e) {}\n  return false;\n})();\nconst queueDomReads = [];\nconst queueDomWrites = [];\nconst queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* PLATFORM_FLAGS.queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nconst consume = queue => {\n  for (let i = 0; i < queue.length; i++) {\n    try {\n      queue[i](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nconst flush = () => {\n  // always force a bunch of medium callbacks to run, but still have\n  // a throttle on how many can run in a certain time\n  // DOM READS!!!\n  consume(queueDomReads);\n  // DOM WRITES!!!\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      // still more to do yet, but we've run out of time\n      // let's let this thing cool off and try again in the next tick\n      plt.raf(flush);\n    }\n  }\n};\nconst nextTick = cb => promiseResolve().then(cb);\nconst readTask = /*@__PURE__*/queueTask(queueDomReads, false);\nconst writeTask = /*@__PURE__*/queueTask(queueDomWrites, true);\nexport { Build as B, Host as H, setPlatformHelpers as a, bootstrapLazy as b, setMode as c, createEvent as d, readTask as e, getElement as f, getMode as g, h, forceUpdate as i, getAssetPath as j, promiseResolve as p, registerInstance as r, setNonce as s, writeTask as w };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}