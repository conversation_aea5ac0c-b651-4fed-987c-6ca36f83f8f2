{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"@angular/common\";\nfunction ProductDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵelement(2, \"img\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"h2\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 9);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 10);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 11)(11, \"button\", 12);\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 13);\n    i0.ɵɵtext(14, \"Add to Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 14);\n    i0.ɵɵtext(16, \"Buy Now\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.getProductImage(ctx_r0.product), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", ctx_r0.product.price, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.product.description);\n  }\n}\nexport let ProductDetailComponent = /*#__PURE__*/(() => {\n  class ProductDetailComponent {\n    constructor(route, router, productService) {\n      this.route = route;\n      this.router = router;\n      this.productService = productService;\n      this.productId = null;\n      this.product = null;\n      this.loading = true;\n    }\n    ngOnInit() {\n      this.route.params.subscribe(params => {\n        this.productId = params['id'];\n        if (this.productId) {\n          this.loadProduct();\n        }\n      });\n    }\n    loadProduct() {\n      if (!this.productId) return;\n      this.loading = true;\n      this.productService.getProductById(this.productId).subscribe({\n        next: response => {\n          this.product = response?.data || null;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading product:', error);\n          this.product = null;\n          this.loading = false;\n        }\n      });\n    }\n    getProductImage(product) {\n      return product?.images?.[0]?.url || '/assets/images/default-product.svg';\n    }\n    goBack() {\n      this.router.navigate(['/']);\n    }\n    static {\n      this.ɵfac = function ProductDetailComponent_Factory(t) {\n        return new (t || ProductDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProductDetailComponent,\n        selectors: [[\"app-product-detail\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 8,\n        vars: 1,\n        consts: [[1, \"product-detail-container\"], [1, \"product-header\"], [1, \"back-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [\"class\", \"product-content\", 4, \"ngIf\"], [1, \"product-content\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"price\"], [1, \"description\"], [1, \"product-actions\"], [1, \"btn-cart\"], [1, \"btn-wishlist\"], [1, \"btn-buy\"]],\n        template: function ProductDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function ProductDetailComponent_Template_button_click_2_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" Back \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"h1\");\n            i0.ɵɵtext(6, \"Product Details\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(7, ProductDetailComponent_div_7_Template, 17, 5, \"div\", 4);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.product);\n          }\n        },\n        dependencies: [CommonModule, i3.NgIf, FormsModule],\n        styles: [\".product-detail-container[_ngcontent-%COMP%]{padding:2rem;max-width:1200px;margin:0 auto}.product-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;margin-bottom:2rem}.back-btn[_ngcontent-%COMP%]{background:#f8f9fa;border:1px solid #ddd;padding:.5rem 1rem;border-radius:6px;cursor:pointer;display:flex;align-items:center;gap:.5rem;transition:background .2s}.back-btn[_ngcontent-%COMP%]:hover{background:#e9ecef}.product-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:2rem}.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;border-radius:8px}.product-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:1rem;color:#333}.price[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#e91e63;margin-bottom:1rem}.description[_ngcontent-%COMP%]{color:#666;line-height:1.6;margin-bottom:2rem}.product-actions[_ngcontent-%COMP%]{display:flex;gap:1rem}.btn-cart[_ngcontent-%COMP%], .btn-wishlist[_ngcontent-%COMP%], .btn-buy[_ngcontent-%COMP%]{padding:1rem 2rem;border:none;border-radius:6px;font-weight:600;cursor:pointer;transition:all .2s}.btn-cart[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-cart[_ngcontent-%COMP%]:hover{background:#0056b3}.btn-wishlist[_ngcontent-%COMP%]{background:#f8f9fa;color:#666;border:1px solid #ddd}.btn-wishlist[_ngcontent-%COMP%]:hover{background:#e9ecef}.btn-buy[_ngcontent-%COMP%]{background:#28a745;color:#fff}.btn-buy[_ngcontent-%COMP%]:hover{background:#218838}@media (max-width: 768px){.product-content[_ngcontent-%COMP%]{grid-template-columns:1fr}.product-actions[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return ProductDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}