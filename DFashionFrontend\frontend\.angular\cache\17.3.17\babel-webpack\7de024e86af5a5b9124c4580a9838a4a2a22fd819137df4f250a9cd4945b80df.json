{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nfunction VendorStoriesComponent_div_7_div_1_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 30);\n  }\n  if (rf & 2) {\n    const story_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", story_r2.media.url, i0.ɵɵsanitizeUrl)(\"alt\", story_r2.caption);\n  }\n}\nfunction VendorStoriesComponent_div_7_div_1_video_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 31);\n  }\n  if (rf & 2) {\n    const story_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", story_r2.media.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction VendorStoriesComponent_div_7_div_1_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind3(2, 2, story_r2.caption, 0, 80), \"\", story_r2.caption.length > 80 ? \"...\" : \"\", \"\");\n  }\n}\nfunction VendorStoriesComponent_div_7_div_1_div_20_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r3.name, \" \");\n  }\n}\nfunction VendorStoriesComponent_div_7_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h4\");\n    i0.ɵɵtext(2, \"Tagged Products:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, VendorStoriesComponent_div_7_div_1_div_20_span_4_Template, 2, 1, \"span\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", story_r2.taggedProducts);\n  }\n}\nfunction VendorStoriesComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵtemplate(2, VendorStoriesComponent_div_7_div_1_img_2_Template, 1, 2, \"img\", 10)(3, VendorStoriesComponent_div_7_div_1_video_3_Template, 1, 1, \"video\", 11);\n    i0.ɵɵelementStart(4, \"div\", 12);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵtemplate(9, VendorStoriesComponent_div_7_div_1_p_9_Template, 3, 6, \"p\", 15);\n    i0.ɵɵelementStart(10, \"div\", 16)(11, \"span\");\n    i0.ɵɵelement(12, \"i\", 17);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵelement(15, \"i\", 18);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵelement(18, \"i\", 19);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, VendorStoriesComponent_div_7_div_1_div_20_Template, 5, 1, \"div\", 20);\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"span\", 22);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 23);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 24)(28, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function VendorStoriesComponent_div_7_div_1_Template_button_click_28_listener() {\n      const story_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.viewStory(story_r2));\n    });\n    i0.ɵɵelement(29, \"i\", 17);\n    i0.ɵɵtext(30, \" View \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function VendorStoriesComponent_div_7_div_1_Template_button_click_31_listener() {\n      const story_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.viewAnalytics(story_r2));\n    });\n    i0.ɵɵelement(32, \"i\", 27);\n    i0.ɵɵtext(33, \" Analytics \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function VendorStoriesComponent_div_7_div_1_Template_button_click_34_listener() {\n      const story_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.deleteStory(story_r2));\n    });\n    i0.ɵɵelement(35, \"i\", 29);\n    i0.ɵɵtext(36, \" Delete \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const story_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", story_r2.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r2.media.type === \"video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(story_r2.media.type === \"video\" ? \"fas fa-play\" : \"fas fa-image\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getTimeRemaining(story_r2.createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", story_r2.caption);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", story_r2.views || 0, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", story_r2.replies || 0, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", story_r2.productClicks || 0, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r2.taggedProducts && story_r2.taggedProducts.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(24, 14, story_r2.createdAt, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.getStoryStatus(story_r2.createdAt));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStoryStatus(story_r2.createdAt), \" \");\n  }\n}\nfunction VendorStoriesComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, VendorStoriesComponent_div_7_div_1_Template, 37, 17, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.stories);\n  }\n}\nfunction VendorStoriesComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"No stories yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Create engaging 24-hour stories to showcase your products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 2);\n    i0.ɵɵtext(8, \"Create Your First Story\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let VendorStoriesComponent = /*#__PURE__*/(() => {\n  class VendorStoriesComponent {\n    constructor() {\n      this.stories = [];\n    }\n    ngOnInit() {\n      this.loadStories();\n    }\n    loadStories() {\n      // Load vendor stories from API\n      this.stories = [];\n    }\n    getTimeRemaining(createdAt) {\n      const now = new Date();\n      const storyTime = new Date(createdAt);\n      const diffMs = now.getTime() - storyTime.getTime();\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      const remainingHours = 24 - diffHours;\n      if (remainingHours <= 0) {\n        return 'Expired';\n      } else if (remainingHours < 1) {\n        const remainingMinutes = 60 - Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n        return `${remainingMinutes}m left`;\n      } else {\n        return `${remainingHours}h left`;\n      }\n    }\n    getStoryStatus(createdAt) {\n      const now = new Date();\n      const storyTime = new Date(createdAt);\n      const diffHours = (now.getTime() - storyTime.getTime()) / (1000 * 60 * 60);\n      return diffHours >= 24 ? 'expired' : 'active';\n    }\n    viewStory(story) {\n      // TODO: Open story viewer\n      console.log('View story:', story);\n    }\n    viewAnalytics(story) {\n      // TODO: Show story analytics\n      console.log('View analytics for story:', story);\n    }\n    deleteStory(story) {\n      if (confirm('Are you sure you want to delete this story?')) {\n        // TODO: Implement delete API call\n        this.stories = this.stories.filter(s => s._id !== story._id);\n      }\n    }\n    static {\n      this.ɵfac = function VendorStoriesComponent_Factory(t) {\n        return new (t || VendorStoriesComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VendorStoriesComponent,\n        selectors: [[\"app-vendor-stories\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 9,\n        vars: 2,\n        consts: [[1, \"vendor-stories-container\"], [1, \"header\"], [\"routerLink\", \"/vendor/stories/create\", 1, \"btn-primary\"], [1, \"fas\", \"fa-plus\"], [\"class\", \"stories-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"stories-grid\"], [\"class\", \"story-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-card\"], [1, \"story-media\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [\"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [1, \"story-type\"], [1, \"story-duration\"], [1, \"story-content\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [1, \"story-stats\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-reply\"], [1, \"fas\", \"fa-shopping-bag\"], [\"class\", \"story-products\", 4, \"ngIf\"], [1, \"story-meta\"], [1, \"story-date\"], [1, \"story-status\"], [1, \"story-actions\"], [1, \"btn-view\", 3, \"click\"], [1, \"btn-analytics\", 3, \"click\"], [1, \"fas\", \"fa-chart-bar\"], [1, \"btn-delete\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [3, \"src\", \"alt\"], [\"muted\", \"\", 3, \"src\"], [1, \"story-caption\"], [1, \"story-products\"], [1, \"tagged-products\"], [\"class\", \"product-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"fas\", \"fa-play-circle\"]],\n        template: function VendorStoriesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"My Stories\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"a\", 2);\n            i0.ɵɵelement(5, \"i\", 3);\n            i0.ɵɵtext(6, \" Create Story \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(7, VendorStoriesComponent_div_7_Template, 2, 1, \"div\", 4)(8, VendorStoriesComponent_div_8_Template, 9, 0, \"div\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.stories.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.stories.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.SlicePipe, i1.DatePipe, RouterModule, i2.RouterLink],\n        styles: [\".vendor-stories-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600}.stories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:24px}.story-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000001a;transition:transform .2s}.story-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 4px 16px #00000026}.story-media[_ngcontent-%COMP%]{position:relative;height:200px;overflow:hidden;background:#000}.story-media[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .story-media[_ngcontent-%COMP%]   video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.story-type[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:#000000b3;color:#fff;padding:6px 8px;border-radius:12px;font-size:.75rem}.story-duration[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#000000b3;color:#fff;padding:4px 8px;border-radius:12px;font-size:.75rem;font-weight:500}.story-content[_ngcontent-%COMP%]{padding:16px}.story-caption[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.4;margin-bottom:12px;color:#333}.story-stats[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:12px;font-size:.85rem;color:#666}.story-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.story-products[_ngcontent-%COMP%]{margin-bottom:12px}.story-products[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.85rem;font-weight:600;margin-bottom:6px;color:#333}.tagged-products[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:6px}.product-tag[_ngcontent-%COMP%]{background:#f0f8ff;color:#007bff;padding:2px 8px;border-radius:12px;font-size:.75rem;font-weight:500}.story-meta[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;font-size:.8rem;color:#666}.story-status[_ngcontent-%COMP%]{padding:2px 8px;border-radius:12px;font-weight:500;text-transform:uppercase}.story-status.active[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.story-status.expired[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.story-actions[_ngcontent-%COMP%]{display:flex;gap:8px;padding:16px;border-top:1px solid #f0f0f0}.btn-view[_ngcontent-%COMP%], .btn-analytics[_ngcontent-%COMP%], .btn-delete[_ngcontent-%COMP%]{flex:1;padding:8px 12px;border:none;border-radius:6px;font-size:.8rem;cursor:pointer;transition:all .2s}.btn-view[_ngcontent-%COMP%]{background:#e7f3ff;color:#007bff}.btn-view[_ngcontent-%COMP%]:hover{background:#cce7ff}.btn-analytics[_ngcontent-%COMP%]{background:#f8f9fa;color:#495057}.btn-analytics[_ngcontent-%COMP%]:hover{background:#e9ecef}.btn-delete[_ngcontent-%COMP%]{background:#fee;color:#dc3545}.btn-delete[_ngcontent-%COMP%]:hover{background:#fdd}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff;padding:12px 24px;border-radius:6px;text-decoration:none;font-weight:500;display:inline-flex;align-items:center;gap:8px;transition:background .2s}.btn-primary[_ngcontent-%COMP%]:hover{background:#0056b3}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#ddd;margin-bottom:20px}.empty-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:10px}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:30px}@media (max-width: 768px){.header[_ngcontent-%COMP%]{flex-direction:column;gap:16px;align-items:stretch}.stories-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.story-actions[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return VendorStoriesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}