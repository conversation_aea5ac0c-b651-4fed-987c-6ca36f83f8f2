{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport LocalForage from 'localforage';\n/** @hidden */\nexport const Drivers = {\n  SecureStorage: 'ionicSecureStorage',\n  IndexedDB: LocalForage.INDEXEDDB,\n  LocalStorage: LocalForage.LOCALSTORAGE\n};\nconst defaultConfig = {\n  name: '_ionicstorage',\n  storeName: '_ionickv',\n  dbKey: '_ionickey',\n  driverOrder: [Drivers.SecureStorage, Drivers.IndexedDB, Drivers.LocalStorage]\n};\nexport class Storage {\n  /**\n   * Create a new Storage instance using the order of drivers and any additional config\n   * options to pass to LocalForage.\n   *\n   * Possible default driverOrder options are: ['indexeddb', 'localstorage'] and the\n   * default is that exact ordering.\n   *\n   * When using Ionic Secure Storage (enterprise only), use ['ionicSecureStorage', 'indexeddb', 'localstorage'] to ensure\n   * Secure Storage is used when available, or fall back to IndexedDB or LocalStorage on the web.\n   */\n  constructor(config = defaultConfig) {\n    this._db = null;\n    this._secureStorageDriver = null;\n    const actualConfig = Object.assign({}, defaultConfig, config || {});\n    this._config = actualConfig;\n  }\n  create() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const db = LocalForage.createInstance(_this._config);\n      _this._db = db;\n      yield db.setDriver(_this._config.driverOrder || []);\n      return _this;\n    })();\n  }\n  /**\n   * Define a new Driver. Must be called before\n   * initializing the database. Example:\n   *\n   * await storage.defineDriver(myDriver);\n   * await storage.create();\n   */\n  defineDriver(driver) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (driver._driver === Drivers.SecureStorage) {\n        _this2._secureStorageDriver = driver;\n      }\n      return LocalForage.defineDriver(driver);\n    })();\n  }\n  /**\n   * Get the name of the driver being used.\n   * @returns Name of the driver\n   */\n  get driver() {\n    var _a;\n    return ((_a = this._db) === null || _a === void 0 ? void 0 : _a.driver()) || null;\n  }\n  assertDb() {\n    if (!this._db) {\n      throw new Error('Database not created. Must call create() first');\n    }\n    return this._db;\n  }\n  /**\n   * Get the value associated with the given key.\n   * @param key the key to identify this value\n   * @returns Returns a promise with the value of the given key\n   */\n  get(key) {\n    const db = this.assertDb();\n    return db.getItem(key);\n  }\n  /**\n   * Set the value for the given key.\n   * @param key the key to identify this value\n   * @param value the value for this key\n   * @returns Returns a promise that resolves when the key and value are set\n   */\n  set(key, value) {\n    const db = this.assertDb();\n    return db.setItem(key, value);\n  }\n  /**\n   * Remove any value associated with this key.\n   * @param key the key to identify this value\n   * @returns Returns a promise that resolves when the value is removed\n   */\n  remove(key) {\n    const db = this.assertDb();\n    return db.removeItem(key);\n  }\n  /**\n   * Clear the entire key value store. WARNING: HOT!\n   * @returns Returns a promise that resolves when the store is cleared\n   */\n  clear() {\n    const db = this.assertDb();\n    return db.clear();\n  }\n  /**\n   * @returns Returns a promise that resolves with the number of keys stored.\n   */\n  length() {\n    const db = this.assertDb();\n    return db.length();\n  }\n  /**\n   * @returns Returns a promise that resolves with the keys in the store.\n   */\n  keys() {\n    const db = this.assertDb();\n    return db.keys();\n  }\n  /**\n   * Iterate through each key,value pair.\n   * @param iteratorCallback a callback of the form (value, key, iterationNumber)\n   * @returns Returns a promise that resolves when the iteration has finished.\n   */\n  forEach(iteratorCallback) {\n    const db = this.assertDb();\n    return db.iterate(iteratorCallback);\n  }\n  setEncryptionKey(key) {\n    var _a;\n    if (!this._secureStorageDriver) {\n      throw new Error('@ionic-enterprise/secure-storage not installed. Encryption support not available');\n    } else {\n      (_a = this._secureStorageDriver) === null || _a === void 0 ? void 0 : _a.setEncryptionKey(key);\n    }\n  }\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}