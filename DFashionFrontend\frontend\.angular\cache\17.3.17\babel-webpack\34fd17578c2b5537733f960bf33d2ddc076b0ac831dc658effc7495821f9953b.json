{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { k as inheritAttributes } from './helpers-be245865.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\nconst IonImgStyle0 = imgCss;\nconst Img = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n    this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n    this.ionError = createEvent(this, \"ionError\", 7);\n    this.inheritedAttributes = {};\n    this.onLoad = () => {\n      this.ionImgDidLoad.emit();\n    };\n    this.onError = () => {\n      this.ionError.emit();\n    };\n    this.loadSrc = undefined;\n    this.loadError = undefined;\n    this.alt = undefined;\n    this.src = undefined;\n  }\n  srcChanged() {\n    this.addIO();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n  }\n  componentDidLoad() {\n    this.addIO();\n  }\n  addIO() {\n    if (this.src === undefined) {\n      return;\n    }\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n      this.removeIO();\n      this.io = new IntersectionObserver(data => {\n        /**\n         * On slower devices, it is possible for an intersection observer entry to contain multiple\n         * objects in the array. This happens when quickly scrolling an image into view and then out of\n         * view. In this case, the last object represents the current state of the component.\n         */\n        if (data[data.length - 1].isIntersecting) {\n          this.load();\n          this.removeIO();\n        }\n      });\n      this.io.observe(this.el);\n    } else {\n      // fall back to setTimeout for Safari and IE\n      setTimeout(() => this.load(), 200);\n    }\n  }\n  load() {\n    this.loadError = this.onError;\n    this.loadSrc = this.src;\n    this.ionImgWillLoad.emit();\n  }\n  removeIO() {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n  render() {\n    const {\n      loadSrc,\n      alt,\n      onLoad,\n      loadError,\n      inheritedAttributes\n    } = this;\n    const {\n      draggable\n    } = inheritedAttributes;\n    return h(Host, {\n      key: 'efff4d1bd0e54dbeff140c137eb50b803a9f6f60',\n      class: getIonMode(this)\n    }, h(\"img\", {\n      key: '3a1e0276ae67a7e40ec8c4ecd0061634573b2094',\n      decoding: \"async\",\n      src: loadSrc,\n      alt: alt,\n      onLoad: onLoad,\n      onError: loadError,\n      part: \"image\",\n      draggable: isDraggable(draggable)\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"src\": [\"srcChanged\"]\n    };\n  }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = draggable => {\n  switch (draggable) {\n    case 'true':\n      return true;\n    case 'false':\n      return false;\n    default:\n      return undefined;\n  }\n};\nImg.style = IonImgStyle0;\nexport { Img as ion_img };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}