{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, catchError, tap, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class SearchService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.API_URL = 'http://********:5000/api/v1'; // Direct IP for testing\n    // Search state management\n    this.searchQuerySubject = new BehaviorSubject('');\n    this.searchFiltersSubject = new BehaviorSubject({});\n    this.searchResultsSubject = new BehaviorSubject(null);\n    this.searchLoadingSubject = new BehaviorSubject(false);\n    this.searchSuggestionsSubject = new BehaviorSubject([]);\n    // Public observables\n    this.searchQuery$ = this.searchQuerySubject.asObservable();\n    this.searchFilters$ = this.searchFiltersSubject.asObservable();\n    this.searchResults$ = this.searchResultsSubject.asObservable();\n    this.searchLoading$ = this.searchLoadingSubject.asObservable();\n    this.searchSuggestions$ = this.searchSuggestionsSubject.asObservable();\n    // Search input subject for debouncing\n    this.searchInputSubject = new Subject();\n    // Cache for suggestions and trending searches\n    this.suggestionsCache = new Map();\n    this.trendingCache = null;\n    this.initializeSearchDebouncing();\n  }\n  // Initialize search input debouncing\n  initializeSearchDebouncing() {\n    this.searchInputSubject.pipe(debounceTime(300), distinctUntilChanged(), switchMap(query => {\n      if (query.trim().length > 0) {\n        return this.performSearch(query, this.searchFiltersSubject.value);\n      } else {\n        this.searchResultsSubject.next(null);\n        return of(null);\n      }\n    })).subscribe();\n  }\n  // Set search query (triggers debounced search)\n  setSearchQuery(query) {\n    this.searchQuerySubject.next(query);\n    this.searchInputSubject.next(query);\n  }\n  // Set search filters\n  setSearchFilters(filters) {\n    this.searchFiltersSubject.next(filters);\n    // Re-search if there's an active query\n    const currentQuery = this.searchQuerySubject.value;\n    if (currentQuery.trim()) {\n      this.performSearch(currentQuery, filters).subscribe();\n    }\n  }\n  // Perform search with current query and filters\n  performSearch(query, filters = {}, options = {}) {\n    this.searchLoadingSubject.next(true);\n    let params = new HttpParams();\n    if (query) params = params.set('q', query);\n    // Add filters\n    Object.keys(filters).forEach(key => {\n      const value = filters[key];\n      if (value !== undefined && value !== null && value !== '') {\n        if (Array.isArray(value)) {\n          params = params.set(key, value.join(','));\n        } else {\n          params = params.set(key, value.toString());\n        }\n      }\n    });\n    // Add options\n    Object.keys(options).forEach(key => {\n      const value = options[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n    return this.http.get(`${this.API_URL}/search`, {\n      params\n    }).pipe(tap(result => {\n      this.searchResultsSubject.next(result);\n      this.searchLoadingSubject.next(false);\n    }), catchError(error => {\n      console.error('Search error:', error);\n      this.searchLoadingSubject.next(false);\n      this.searchResultsSubject.next({\n        success: false,\n        products: [],\n        pagination: {\n          current: 1,\n          pages: 0,\n          total: 0,\n          hasNext: false,\n          hasPrev: false\n        },\n        searchMeta: {\n          query,\n          filters,\n          resultsCount: 0,\n          searchTime: Date.now(),\n          suggestions: []\n        }\n      });\n      return of({\n        success: false,\n        products: [],\n        pagination: {\n          current: 1,\n          pages: 0,\n          total: 0,\n          hasNext: false,\n          hasPrev: false\n        },\n        searchMeta: {\n          query,\n          filters,\n          resultsCount: 0,\n          searchTime: Date.now(),\n          suggestions: []\n        }\n      });\n    }));\n  }\n  // Get search suggestions with caching\n  getSearchSuggestions(query, limit = 10, type = 'all') {\n    const cacheKey = `${query}_${limit}_${type}`;\n    const cached = this.suggestionsCache.get(cacheKey);\n    // Return cached data if less than 5 minutes old\n    if (cached && Date.now() - cached.timestamp < 300000) {\n      return of(cached.data);\n    }\n    let params = new HttpParams().set('limit', limit.toString()).set('type', type);\n    if (query) params = params.set('q', query);\n    return this.http.get(`${this.API_URL}/search/suggestions`, {\n      params\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.suggestionsCache.set(cacheKey, {\n          data: response.suggestions,\n          timestamp: Date.now()\n        });\n        this.searchSuggestionsSubject.next(response.suggestions);\n      }\n    }), switchMap(response => of(response.suggestions || [])), catchError(error => {\n      console.error('Get suggestions error:', error);\n      return of([]);\n    }));\n  }\n  // Get trending searches with caching\n  getTrendingSearches(limit = 10, timeframe = '24h') {\n    // Return cached data if less than 10 minutes old\n    if (this.trendingCache && Date.now() - this.trendingCache.timestamp < 600000) {\n      return of(this.trendingCache.data);\n    }\n    const params = new HttpParams().set('limit', limit.toString()).set('timeframe', timeframe);\n    return this.http.get(`${this.API_URL}/search/trending`, {\n      params\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.trendingCache = {\n          data: response.trending,\n          timestamp: Date.now()\n        };\n      }\n    }), switchMap(response => of(response.trending || [])), catchError(error => {\n      console.error('Get trending searches error:', error);\n      return of([]);\n    }));\n  }\n  // Get user's search history\n  getSearchHistory(limit = 20, type = 'recent') {\n    if (!this.authService.isAuthenticated) {\n      return of({\n        searches: [],\n        analytics: this.getDefaultAnalytics()\n      });\n    }\n    const params = new HttpParams().set('limit', limit.toString()).set('type', type);\n    return this.http.get(`${this.API_URL}/search/history`, {\n      params\n    }).pipe(switchMap(response => of({\n      searches: response.searches || [],\n      analytics: response.analytics || this.getDefaultAnalytics()\n    })), catchError(error => {\n      console.error('Get search history error:', error);\n      return of({\n        searches: [],\n        analytics: this.getDefaultAnalytics()\n      });\n    }));\n  }\n  // Clear search history\n  clearSearchHistory(type = 'all') {\n    if (!this.authService.isAuthenticated) {\n      return of(false);\n    }\n    const params = new HttpParams().set('type', type);\n    return this.http.delete(`${this.API_URL}/search/history`, {\n      params\n    }).pipe(switchMap(response => of(response.success)), catchError(error => {\n      console.error('Clear search history error:', error);\n      return of(false);\n    }));\n  }\n  // Track search interactions\n  trackSearchInteraction(searchQuery, productId, action, position, metadata) {\n    if (!this.authService.isAuthenticated) {\n      return of(false);\n    }\n    const body = {\n      searchQuery,\n      productId,\n      action,\n      position,\n      metadata\n    };\n    return this.http.post(`${this.API_URL}/search/track`, body).pipe(switchMap(response => of(response.success)), catchError(error => {\n      console.error('Track search interaction error:', error);\n      return of(false);\n    }));\n  }\n  // Convenience methods for common tracking actions\n  trackProductClick(searchQuery, productId, position) {\n    return this.trackSearchInteraction(searchQuery, productId, 'click', position);\n  }\n  trackProductPurchase(searchQuery, productId) {\n    return this.trackSearchInteraction(searchQuery, productId, 'purchase');\n  }\n  trackSearchDuration(searchQuery, duration) {\n    return this.trackSearchInteraction(searchQuery, '', 'view_duration', 0, {\n      duration\n    });\n  }\n  trackFilterChange(searchQuery) {\n    return this.trackSearchInteraction(searchQuery, '', 'filter_change');\n  }\n  // Get search analytics (for admin/vendor dashboards)\n  getSearchAnalytics(timeframe = '7d', limit = 50) {\n    const params = new HttpParams().set('timeframe', timeframe).set('limit', limit.toString());\n    return this.http.get(`${this.API_URL}/search/analytics`, {\n      params\n    }).pipe(catchError(error => {\n      console.error('Get search analytics error:', error);\n      return of({\n        success: false,\n        analytics: {\n          overview: this.getDefaultAnalytics(),\n          trendingSearches: []\n        }\n      });\n    }));\n  }\n  // Reset search state\n  resetSearch() {\n    this.searchQuerySubject.next('');\n    this.searchFiltersSubject.next({});\n    this.searchResultsSubject.next(null);\n    this.searchLoadingSubject.next(false);\n    this.searchSuggestionsSubject.next([]);\n  }\n  // Clear caches\n  clearCaches() {\n    this.suggestionsCache.clear();\n    this.trendingCache = null;\n  }\n  // Get current search state\n  getCurrentSearchState() {\n    return {\n      query: this.searchQuerySubject.value,\n      filters: this.searchFiltersSubject.value,\n      results: this.searchResultsSubject.value,\n      loading: this.searchLoadingSubject.value\n    };\n  }\n  // Enhanced search with AI-powered recommendations\n  getSmartSearchSuggestions(query, userContext) {\n    const params = new HttpParams().set('q', query).set('smart', 'true').set('context', JSON.stringify(userContext || {}));\n    return this.http.get(`${this.API_URL}/search/smart-suggestions`, {\n      params\n    }).pipe(map(response => response.suggestions || []), catchError(error => {\n      console.error('Error fetching smart suggestions:', error);\n      return this.getSearchSuggestions(query);\n    }));\n  }\n  // Visual search functionality\n  searchByImage(imageFile) {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n    this.searchLoadingSubject.next(true);\n    return this.http.post(`${this.API_URL}/search/visual`, formData).pipe(tap(result => {\n      this.searchResultsSubject.next(result);\n      this.searchLoadingSubject.next(false);\n    }), catchError(error => {\n      console.error('Visual search error:', error);\n      this.searchLoadingSubject.next(false);\n      return of({\n        success: false,\n        products: [],\n        pagination: {\n          current: 1,\n          pages: 0,\n          total: 0,\n          hasNext: false,\n          hasPrev: false\n        },\n        searchMeta: {\n          query: 'Visual Search',\n          filters: {},\n          resultsCount: 0,\n          searchTime: Date.now(),\n          suggestions: []\n        }\n      });\n    }));\n  }\n  // Barcode/QR code search\n  searchByBarcode(barcode) {\n    const params = new HttpParams().set('barcode', barcode);\n    this.searchLoadingSubject.next(true);\n    return this.http.get(`${this.API_URL}/search/barcode`, {\n      params\n    }).pipe(tap(result => {\n      this.searchResultsSubject.next(result);\n      this.searchLoadingSubject.next(false);\n    }), catchError(error => {\n      console.error('Barcode search error:', error);\n      this.searchLoadingSubject.next(false);\n      return of({\n        success: false,\n        products: [],\n        pagination: {\n          current: 1,\n          pages: 0,\n          total: 0,\n          hasNext: false,\n          hasPrev: false\n        },\n        searchMeta: {\n          query: `Barcode: ${barcode}`,\n          filters: {},\n          resultsCount: 0,\n          searchTime: Date.now(),\n          suggestions: []\n        }\n      });\n    }));\n  }\n  // Search by similar products\n  searchSimilarProducts(productId, limit = 12) {\n    const params = new HttpParams().set('productId', productId).set('limit', limit.toString());\n    this.searchLoadingSubject.next(true);\n    return this.http.get(`${this.API_URL}/search/similar`, {\n      params\n    }).pipe(tap(result => {\n      this.searchResultsSubject.next(result);\n      this.searchLoadingSubject.next(false);\n    }), catchError(error => {\n      console.error('Similar products search error:', error);\n      this.searchLoadingSubject.next(false);\n      return of({\n        success: false,\n        products: [],\n        pagination: {\n          current: 1,\n          pages: 0,\n          total: 0,\n          hasNext: false,\n          hasPrev: false\n        },\n        searchMeta: {\n          query: 'Similar Products',\n          filters: {},\n          resultsCount: 0,\n          searchTime: Date.now(),\n          suggestions: []\n        }\n      });\n    }));\n  }\n  // Get personalized search recommendations\n  getPersonalizedRecommendations(limit = 10) {\n    if (!this.authService.isAuthenticated) {\n      return this.getTrendingSearches(limit).pipe(map(trending => trending.map(t => ({\n        text: t.query,\n        type: 'trending',\n        popularity: t.searches\n      }))));\n    }\n    const params = new HttpParams().set('limit', limit.toString());\n    return this.http.get(`${this.API_URL}/search/personalized`, {\n      params\n    }).pipe(map(response => response.suggestions || []), catchError(error => {\n      console.error('Error fetching personalized recommendations:', error);\n      return this.getTrendingSearches(limit).pipe(map(trending => trending.map(t => ({\n        text: t.query,\n        type: 'trending',\n        popularity: t.searches\n      }))));\n    }));\n  }\n  // Search analytics and insights\n  getSearchInsights() {\n    if (!this.authService.isAuthenticated) {\n      return of({});\n    }\n    return this.http.get(`${this.API_URL}/search/insights`).pipe(catchError(error => {\n      console.error('Error fetching search insights:', error);\n      return of({});\n    }));\n  }\n  // Helper method for default analytics\n  getDefaultAnalytics() {\n    return {\n      totalSearches: 0,\n      uniqueQueries: 0,\n      clickThroughRate: 0,\n      conversionRate: 0,\n      preferences: {\n        preferredCategories: [],\n        preferredBrands: [],\n        priceRangePreference: {\n          min: 0,\n          max: 10000\n        }\n      }\n    };\n  }\n  static {\n    this.ɵfac = function SearchService_Factory(t) {\n      return new (t || SearchService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SearchService,\n      factory: SearchService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "Subject", "of", "debounceTime", "distinctUntilChanged", "switchMap", "catchError", "tap", "map", "SearchService", "constructor", "http", "authService", "API_URL", "searchQuerySubject", "searchFiltersSubject", "searchResultsSubject", "searchLoadingSubject", "searchSuggestionsSubject", "searchQuery$", "asObservable", "searchFilters$", "searchResults$", "searchLoading$", "searchSuggestions$", "searchInputSubject", "suggestions<PERSON>ache", "Map", "trendingCache", "initializeSearchDebouncing", "pipe", "query", "trim", "length", "performSearch", "value", "next", "subscribe", "setSearch<PERSON>uery", "setSearchFilters", "filters", "<PERSON><PERSON><PERSON><PERSON>", "options", "params", "set", "Object", "keys", "for<PERSON>ach", "key", "undefined", "Array", "isArray", "join", "toString", "get", "result", "error", "console", "success", "products", "pagination", "current", "pages", "total", "hasNext", "has<PERSON>rev", "searchMeta", "resultsCount", "searchTime", "Date", "now", "suggestions", "getSearchSuggestions", "limit", "type", "cache<PERSON>ey", "cached", "timestamp", "data", "response", "getTrendingSearches", "timeframe", "trending", "getSearchHistory", "isAuthenticated", "searches", "analytics", "getDefaultAnalytics", "clearSearchHistory", "delete", "trackSearchInteraction", "searchQuery", "productId", "action", "position", "metadata", "body", "post", "trackProductClick", "trackProductPurchase", "trackSearchDuration", "duration", "trackFilterChange", "getSearchAnalytics", "overview", "trendingSearches", "resetSearch", "clearCaches", "clear", "getCurrentSearchState", "results", "loading", "getSmartSearchSuggestions", "userContext", "JSON", "stringify", "searchByImage", "imageFile", "formData", "FormData", "append", "searchByBarcode", "barcode", "searchSimilarProducts", "getPersonalizedRecommendations", "t", "text", "popularity", "getSearchInsights", "totalSearches", "uniqueQueries", "clickThroughRate", "conversionRate", "preferences", "preferredCategories", "preferredB<PERSON>s", "priceRangePreference", "min", "max", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\search.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject, Subject, timer, of } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, catchError, tap, shareReplay, map } from 'rxjs/operators';\nimport { AuthService } from './auth.service';\nimport { environment } from '../../../environments/environment';\n\nexport interface SearchFilters {\n  category?: string;\n  subcategory?: string;\n  brand?: string;\n  minPrice?: number;\n  maxPrice?: number;\n  rating?: number;\n  inStock?: boolean;\n  onSale?: boolean;\n  colors?: string[];\n  sizes?: string[];\n  tags?: string[];\n}\n\nexport interface SearchOptions {\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  sortOrder?: string;\n}\n\nexport interface SearchSuggestion {\n  text: string;\n  type: 'completion' | 'product' | 'brand' | 'category' | 'trending' | 'personal';\n  popularity: number;\n  metadata?: any;\n}\n\nexport interface SearchResult {\n  success: boolean;\n  products: any[];\n  pagination: {\n    current: number;\n    pages: number;\n    total: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n  searchMeta: {\n    query: string;\n    filters: SearchFilters;\n    resultsCount: number;\n    searchTime: number;\n    suggestions: SearchSuggestion[];\n  };\n}\n\nexport interface TrendingSearch {\n  query: string;\n  searches: number;\n  trendingScore: number;\n  growth?: number;\n}\n\nexport interface SearchHistory {\n  query: string;\n  timestamp: string;\n  resultsCount: number;\n  filters?: SearchFilters;\n}\n\nexport interface SearchAnalytics {\n  totalSearches: number;\n  uniqueQueries: number;\n  clickThroughRate: number;\n  conversionRate: number;\n  preferences: {\n    preferredCategories: Array<{ category: string; score: number }>;\n    preferredBrands: Array<{ brand: string; score: number }>;\n    priceRangePreference: { min: number; max: number };\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SearchService {\n  private readonly API_URL = 'http://********:5000/api/v1'; // Direct IP for testing\n  \n  // Search state management\n  private searchQuerySubject = new BehaviorSubject<string>('');\n  private searchFiltersSubject = new BehaviorSubject<SearchFilters>({});\n  private searchResultsSubject = new BehaviorSubject<SearchResult | null>(null);\n  private searchLoadingSubject = new BehaviorSubject<boolean>(false);\n  private searchSuggestionsSubject = new BehaviorSubject<SearchSuggestion[]>([]);\n  \n  // Public observables\n  public searchQuery$ = this.searchQuerySubject.asObservable();\n  public searchFilters$ = this.searchFiltersSubject.asObservable();\n  public searchResults$ = this.searchResultsSubject.asObservable();\n  public searchLoading$ = this.searchLoadingSubject.asObservable();\n  public searchSuggestions$ = this.searchSuggestionsSubject.asObservable();\n  \n  // Search input subject for debouncing\n  private searchInputSubject = new Subject<string>();\n  \n  // Cache for suggestions and trending searches\n  private suggestionsCache = new Map<string, { data: SearchSuggestion[]; timestamp: number }>();\n  private trendingCache: { data: TrendingSearch[]; timestamp: number } | null = null;\n  \n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {\n    this.initializeSearchDebouncing();\n  }\n\n  // Initialize search input debouncing\n  private initializeSearchDebouncing(): void {\n    this.searchInputSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      switchMap(query => {\n        if (query.trim().length > 0) {\n          return this.performSearch(query, this.searchFiltersSubject.value);\n        } else {\n          this.searchResultsSubject.next(null);\n          return of(null);\n        }\n      })\n    ).subscribe();\n  }\n\n  // Set search query (triggers debounced search)\n  setSearchQuery(query: string): void {\n    this.searchQuerySubject.next(query);\n    this.searchInputSubject.next(query);\n  }\n\n  // Set search filters\n  setSearchFilters(filters: SearchFilters): void {\n    this.searchFiltersSubject.next(filters);\n    \n    // Re-search if there's an active query\n    const currentQuery = this.searchQuerySubject.value;\n    if (currentQuery.trim()) {\n      this.performSearch(currentQuery, filters).subscribe();\n    }\n  }\n\n  // Perform search with current query and filters\n  performSearch(query: string, filters: SearchFilters = {}, options: SearchOptions = {}): Observable<SearchResult> {\n    this.searchLoadingSubject.next(true);\n    \n    let params = new HttpParams();\n    \n    if (query) params = params.set('q', query);\n    \n    // Add filters\n    Object.keys(filters).forEach(key => {\n      const value = (filters as any)[key];\n      if (value !== undefined && value !== null && value !== '') {\n        if (Array.isArray(value)) {\n          params = params.set(key, value.join(','));\n        } else {\n          params = params.set(key, value.toString());\n        }\n      }\n    });\n    \n    // Add options\n    Object.keys(options).forEach(key => {\n      const value = (options as any)[key];\n      if (value !== undefined && value !== null && value !== '') {\n        params = params.set(key, value.toString());\n      }\n    });\n\n    return this.http.get<SearchResult>(`${this.API_URL}/search`, { params }).pipe(\n      tap(result => {\n        this.searchResultsSubject.next(result);\n        this.searchLoadingSubject.next(false);\n      }),\n      catchError(error => {\n        console.error('Search error:', error);\n        this.searchLoadingSubject.next(false);\n        this.searchResultsSubject.next({\n          success: false,\n          products: [],\n          pagination: { current: 1, pages: 0, total: 0, hasNext: false, hasPrev: false },\n          searchMeta: { query, filters, resultsCount: 0, searchTime: Date.now(), suggestions: [] }\n        });\n        return of({\n          success: false,\n          products: [],\n          pagination: { current: 1, pages: 0, total: 0, hasNext: false, hasPrev: false },\n          searchMeta: { query, filters, resultsCount: 0, searchTime: Date.now(), suggestions: [] }\n        });\n      })\n    );\n  }\n\n  // Get search suggestions with caching\n  getSearchSuggestions(query: string, limit: number = 10, type: string = 'all'): Observable<SearchSuggestion[]> {\n    const cacheKey = `${query}_${limit}_${type}`;\n    const cached = this.suggestionsCache.get(cacheKey);\n    \n    // Return cached data if less than 5 minutes old\n    if (cached && Date.now() - cached.timestamp < 300000) {\n      return of(cached.data);\n    }\n    \n    let params = new HttpParams()\n      .set('limit', limit.toString())\n      .set('type', type);\n    \n    if (query) params = params.set('q', query);\n\n    return this.http.get<{ success: boolean; suggestions: SearchSuggestion[] }>(`${this.API_URL}/search/suggestions`, { params }).pipe(\n      tap(response => {\n        if (response.success) {\n          this.suggestionsCache.set(cacheKey, {\n            data: response.suggestions,\n            timestamp: Date.now()\n          });\n          this.searchSuggestionsSubject.next(response.suggestions);\n        }\n      }),\n      switchMap(response => of(response.suggestions || [])),\n      catchError(error => {\n        console.error('Get suggestions error:', error);\n        return of([]);\n      })\n    );\n  }\n\n  // Get trending searches with caching\n  getTrendingSearches(limit: number = 10, timeframe: string = '24h'): Observable<TrendingSearch[]> {\n    // Return cached data if less than 10 minutes old\n    if (this.trendingCache && Date.now() - this.trendingCache.timestamp < 600000) {\n      return of(this.trendingCache.data);\n    }\n    \n    const params = new HttpParams()\n      .set('limit', limit.toString())\n      .set('timeframe', timeframe);\n\n    return this.http.get<{ success: boolean; trending: TrendingSearch[] }>(`${this.API_URL}/search/trending`, { params }).pipe(\n      tap(response => {\n        if (response.success) {\n          this.trendingCache = {\n            data: response.trending,\n            timestamp: Date.now()\n          };\n        }\n      }),\n      switchMap(response => of(response.trending || [])),\n      catchError(error => {\n        console.error('Get trending searches error:', error);\n        return of([]);\n      })\n    );\n  }\n\n  // Get user's search history\n  getSearchHistory(limit: number = 20, type: string = 'recent'): Observable<{ searches: SearchHistory[]; analytics: SearchAnalytics }> {\n    if (!this.authService.isAuthenticated) {\n      return of({ searches: [], analytics: this.getDefaultAnalytics() });\n    }\n    \n    const params = new HttpParams()\n      .set('limit', limit.toString())\n      .set('type', type);\n\n    return this.http.get<any>(`${this.API_URL}/search/history`, { params }).pipe(\n      switchMap(response => of({\n        searches: response.searches || [],\n        analytics: response.analytics || this.getDefaultAnalytics()\n      })),\n      catchError(error => {\n        console.error('Get search history error:', error);\n        return of({ searches: [], analytics: this.getDefaultAnalytics() });\n      })\n    );\n  }\n\n  // Clear search history\n  clearSearchHistory(type: string = 'all'): Observable<boolean> {\n    if (!this.authService.isAuthenticated) {\n      return of(false);\n    }\n    \n    const params = new HttpParams().set('type', type);\n\n    return this.http.delete<{ success: boolean }>(`${this.API_URL}/search/history`, { params }).pipe(\n      switchMap(response => of(response.success)),\n      catchError(error => {\n        console.error('Clear search history error:', error);\n        return of(false);\n      })\n    );\n  }\n\n  // Track search interactions\n  trackSearchInteraction(searchQuery: string, productId: string, action: string, position?: number, metadata?: any): Observable<boolean> {\n    if (!this.authService.isAuthenticated) {\n      return of(false);\n    }\n    \n    const body = {\n      searchQuery,\n      productId,\n      action,\n      position,\n      metadata\n    };\n\n    return this.http.post<{ success: boolean }>(`${this.API_URL}/search/track`, body).pipe(\n      switchMap(response => of(response.success)),\n      catchError(error => {\n        console.error('Track search interaction error:', error);\n        return of(false);\n      })\n    );\n  }\n\n  // Convenience methods for common tracking actions\n  trackProductClick(searchQuery: string, productId: string, position: number): Observable<boolean> {\n    return this.trackSearchInteraction(searchQuery, productId, 'click', position);\n  }\n\n  trackProductPurchase(searchQuery: string, productId: string): Observable<boolean> {\n    return this.trackSearchInteraction(searchQuery, productId, 'purchase');\n  }\n\n  trackSearchDuration(searchQuery: string, duration: number): Observable<boolean> {\n    return this.trackSearchInteraction(searchQuery, '', 'view_duration', 0, { duration });\n  }\n\n  trackFilterChange(searchQuery: string): Observable<boolean> {\n    return this.trackSearchInteraction(searchQuery, '', 'filter_change');\n  }\n\n  // Get search analytics (for admin/vendor dashboards)\n  getSearchAnalytics(timeframe: string = '7d', limit: number = 50): Observable<any> {\n    const params = new HttpParams()\n      .set('timeframe', timeframe)\n      .set('limit', limit.toString());\n\n    return this.http.get<any>(`${this.API_URL}/search/analytics`, { params }).pipe(\n      catchError(error => {\n        console.error('Get search analytics error:', error);\n        return of({\n          success: false,\n          analytics: {\n            overview: this.getDefaultAnalytics(),\n            trendingSearches: []\n          }\n        });\n      })\n    );\n  }\n\n  // Reset search state\n  resetSearch(): void {\n    this.searchQuerySubject.next('');\n    this.searchFiltersSubject.next({});\n    this.searchResultsSubject.next(null);\n    this.searchLoadingSubject.next(false);\n    this.searchSuggestionsSubject.next([]);\n  }\n\n  // Clear caches\n  clearCaches(): void {\n    this.suggestionsCache.clear();\n    this.trendingCache = null;\n  }\n\n  // Get current search state\n  getCurrentSearchState(): {\n    query: string;\n    filters: SearchFilters;\n    results: SearchResult | null;\n    loading: boolean;\n  } {\n    return {\n      query: this.searchQuerySubject.value,\n      filters: this.searchFiltersSubject.value,\n      results: this.searchResultsSubject.value,\n      loading: this.searchLoadingSubject.value\n    };\n  }\n\n  // Enhanced search with AI-powered recommendations\n  getSmartSearchSuggestions(query: string, userContext?: any): Observable<SearchSuggestion[]> {\n    const params = new HttpParams()\n      .set('q', query)\n      .set('smart', 'true')\n      .set('context', JSON.stringify(userContext || {}));\n\n    return this.http.get<{ suggestions: SearchSuggestion[] }>(`${this.API_URL}/search/smart-suggestions`, { params })\n      .pipe(\n        map(response => response.suggestions || []),\n        catchError(error => {\n          console.error('Error fetching smart suggestions:', error);\n          return this.getSearchSuggestions(query);\n        })\n      );\n  }\n\n  // Visual search functionality\n  searchByImage(imageFile: File): Observable<SearchResult> {\n    const formData = new FormData();\n    formData.append('image', imageFile);\n\n    this.searchLoadingSubject.next(true);\n\n    return this.http.post<SearchResult>(`${this.API_URL}/search/visual`, formData).pipe(\n      tap(result => {\n        this.searchResultsSubject.next(result);\n        this.searchLoadingSubject.next(false);\n      }),\n      catchError(error => {\n        console.error('Visual search error:', error);\n        this.searchLoadingSubject.next(false);\n        return of({\n          success: false,\n          products: [],\n          pagination: { current: 1, pages: 0, total: 0, hasNext: false, hasPrev: false },\n          searchMeta: {\n            query: 'Visual Search',\n            filters: {},\n            resultsCount: 0,\n            searchTime: Date.now(),\n            suggestions: []\n          }\n        });\n      })\n    );\n  }\n\n  // Barcode/QR code search\n  searchByBarcode(barcode: string): Observable<SearchResult> {\n    const params = new HttpParams().set('barcode', barcode);\n\n    this.searchLoadingSubject.next(true);\n\n    return this.http.get<SearchResult>(`${this.API_URL}/search/barcode`, { params }).pipe(\n      tap(result => {\n        this.searchResultsSubject.next(result);\n        this.searchLoadingSubject.next(false);\n      }),\n      catchError(error => {\n        console.error('Barcode search error:', error);\n        this.searchLoadingSubject.next(false);\n        return of({\n          success: false,\n          products: [],\n          pagination: { current: 1, pages: 0, total: 0, hasNext: false, hasPrev: false },\n          searchMeta: {\n            query: `Barcode: ${barcode}`,\n            filters: {},\n            resultsCount: 0,\n            searchTime: Date.now(),\n            suggestions: []\n          }\n        });\n      })\n    );\n  }\n\n  // Search by similar products\n  searchSimilarProducts(productId: string, limit: number = 12): Observable<SearchResult> {\n    const params = new HttpParams()\n      .set('productId', productId)\n      .set('limit', limit.toString());\n\n    this.searchLoadingSubject.next(true);\n\n    return this.http.get<SearchResult>(`${this.API_URL}/search/similar`, { params }).pipe(\n      tap(result => {\n        this.searchResultsSubject.next(result);\n        this.searchLoadingSubject.next(false);\n      }),\n      catchError(error => {\n        console.error('Similar products search error:', error);\n        this.searchLoadingSubject.next(false);\n        return of({\n          success: false,\n          products: [],\n          pagination: { current: 1, pages: 0, total: 0, hasNext: false, hasPrev: false },\n          searchMeta: {\n            query: 'Similar Products',\n            filters: {},\n            resultsCount: 0,\n            searchTime: Date.now(),\n            suggestions: []\n          }\n        });\n      })\n    );\n  }\n\n  // Get personalized search recommendations\n  getPersonalizedRecommendations(limit: number = 10): Observable<SearchSuggestion[]> {\n    if (!this.authService.isAuthenticated) {\n      return this.getTrendingSearches(limit).pipe(\n        map(trending => trending.map(t => ({\n          text: t.query,\n          type: 'trending' as const,\n          popularity: t.searches\n        })))\n      );\n    }\n\n    const params = new HttpParams().set('limit', limit.toString());\n\n    return this.http.get<{ suggestions: SearchSuggestion[] }>(`${this.API_URL}/search/personalized`, { params })\n      .pipe(\n        map(response => response.suggestions || []),\n        catchError(error => {\n          console.error('Error fetching personalized recommendations:', error);\n          return this.getTrendingSearches(limit).pipe(\n            map(trending => trending.map(t => ({\n              text: t.query,\n              type: 'trending' as const,\n              popularity: t.searches\n            })))\n          );\n        })\n      );\n  }\n\n  // Search analytics and insights\n  getSearchInsights(): Observable<any> {\n    if (!this.authService.isAuthenticated) {\n      return of({});\n    }\n\n    return this.http.get<any>(`${this.API_URL}/search/insights`).pipe(\n      catchError(error => {\n        console.error('Error fetching search insights:', error);\n        return of({});\n      })\n    );\n  }\n\n  // Helper method for default analytics\n  private getDefaultAnalytics(): SearchAnalytics {\n    return {\n      totalSearches: 0,\n      uniqueQueries: 0,\n      clickThroughRate: 0,\n      conversionRate: 0,\n      preferences: {\n        preferredCategories: [],\n        preferredBrands: [],\n        priceRangePreference: { min: 0, max: 10000 }\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAAqBC,eAAe,EAAEC,OAAO,EAASC,EAAE,QAAQ,MAAM;AACtE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,GAAG,EAAeC,GAAG,QAAQ,gBAAgB;;;;AAgFjH,OAAM,MAAOC,aAAa;EAwBxBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAzBJ,KAAAC,OAAO,GAAG,6BAA6B,CAAC,CAAC;IAE1D;IACQ,KAAAC,kBAAkB,GAAG,IAAId,eAAe,CAAS,EAAE,CAAC;IACpD,KAAAe,oBAAoB,GAAG,IAAIf,eAAe,CAAgB,EAAE,CAAC;IAC7D,KAAAgB,oBAAoB,GAAG,IAAIhB,eAAe,CAAsB,IAAI,CAAC;IACrE,KAAAiB,oBAAoB,GAAG,IAAIjB,eAAe,CAAU,KAAK,CAAC;IAC1D,KAAAkB,wBAAwB,GAAG,IAAIlB,eAAe,CAAqB,EAAE,CAAC;IAE9E;IACO,KAAAmB,YAAY,GAAG,IAAI,CAACL,kBAAkB,CAACM,YAAY,EAAE;IACrD,KAAAC,cAAc,GAAG,IAAI,CAACN,oBAAoB,CAACK,YAAY,EAAE;IACzD,KAAAE,cAAc,GAAG,IAAI,CAACN,oBAAoB,CAACI,YAAY,EAAE;IACzD,KAAAG,cAAc,GAAG,IAAI,CAACN,oBAAoB,CAACG,YAAY,EAAE;IACzD,KAAAI,kBAAkB,GAAG,IAAI,CAACN,wBAAwB,CAACE,YAAY,EAAE;IAExE;IACQ,KAAAK,kBAAkB,GAAG,IAAIxB,OAAO,EAAU;IAElD;IACQ,KAAAyB,gBAAgB,GAAG,IAAIC,GAAG,EAA2D;IACrF,KAAAC,aAAa,GAAyD,IAAI;IAMhF,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEA;EACQA,0BAA0BA,CAAA;IAChC,IAAI,CAACJ,kBAAkB,CAACK,IAAI,CAC1B3B,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC0B,KAAK,IAAG;MAChB,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3B,OAAO,IAAI,CAACC,aAAa,CAACH,KAAK,EAAE,IAAI,CAAChB,oBAAoB,CAACoB,KAAK,CAAC;OAClE,MAAM;QACL,IAAI,CAACnB,oBAAoB,CAACoB,IAAI,CAAC,IAAI,CAAC;QACpC,OAAOlC,EAAE,CAAC,IAAI,CAAC;;IAEnB,CAAC,CAAC,CACH,CAACmC,SAAS,EAAE;EACf;EAEA;EACAC,cAAcA,CAACP,KAAa;IAC1B,IAAI,CAACjB,kBAAkB,CAACsB,IAAI,CAACL,KAAK,CAAC;IACnC,IAAI,CAACN,kBAAkB,CAACW,IAAI,CAACL,KAAK,CAAC;EACrC;EAEA;EACAQ,gBAAgBA,CAACC,OAAsB;IACrC,IAAI,CAACzB,oBAAoB,CAACqB,IAAI,CAACI,OAAO,CAAC;IAEvC;IACA,MAAMC,YAAY,GAAG,IAAI,CAAC3B,kBAAkB,CAACqB,KAAK;IAClD,IAAIM,YAAY,CAACT,IAAI,EAAE,EAAE;MACvB,IAAI,CAACE,aAAa,CAACO,YAAY,EAAED,OAAO,CAAC,CAACH,SAAS,EAAE;;EAEzD;EAEA;EACAH,aAAaA,CAACH,KAAa,EAAES,OAAA,GAAyB,EAAE,EAAEE,OAAA,GAAyB,EAAE;IACnF,IAAI,CAACzB,oBAAoB,CAACmB,IAAI,CAAC,IAAI,CAAC;IAEpC,IAAIO,MAAM,GAAG,IAAI5C,UAAU,EAAE;IAE7B,IAAIgC,KAAK,EAAEY,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,GAAG,EAAEb,KAAK,CAAC;IAE1C;IACAc,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAACO,OAAO,CAACC,GAAG,IAAG;MACjC,MAAMb,KAAK,GAAIK,OAAe,CAACQ,GAAG,CAAC;MACnC,IAAIb,KAAK,KAAKc,SAAS,IAAId,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzD,IAAIe,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,EAAE;UACxBQ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACI,GAAG,EAAEb,KAAK,CAACiB,IAAI,CAAC,GAAG,CAAC,CAAC;SAC1C,MAAM;UACLT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACI,GAAG,EAAEb,KAAK,CAACkB,QAAQ,EAAE,CAAC;;;IAGhD,CAAC,CAAC;IAEF;IACAR,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,OAAO,CAACC,GAAG,IAAG;MACjC,MAAMb,KAAK,GAAIO,OAAe,CAACM,GAAG,CAAC;MACnC,IAAIb,KAAK,KAAKc,SAAS,IAAId,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDQ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACI,GAAG,EAAEb,KAAK,CAACkB,QAAQ,EAAE,CAAC;;IAE9C,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC1C,IAAI,CAAC2C,GAAG,CAAe,GAAG,IAAI,CAACzC,OAAO,SAAS,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CAC3EvB,GAAG,CAACgD,MAAM,IAAG;MACX,IAAI,CAACvC,oBAAoB,CAACoB,IAAI,CAACmB,MAAM,CAAC;MACtC,IAAI,CAACtC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;IACvC,CAAC,CAAC,EACF9B,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAACvC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;MACrC,IAAI,CAACpB,oBAAoB,CAACoB,IAAI,CAAC;QAC7BsB,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAK,CAAE;QAC9EC,UAAU,EAAE;UAAEnC,KAAK;UAAES,OAAO;UAAE2B,YAAY,EAAE,CAAC;UAAEC,UAAU,EAAEC,IAAI,CAACC,GAAG,EAAE;UAAEC,WAAW,EAAE;QAAE;OACvF,CAAC;MACF,OAAOrE,EAAE,CAAC;QACRwD,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAK,CAAE;QAC9EC,UAAU,EAAE;UAAEnC,KAAK;UAAES,OAAO;UAAE2B,YAAY,EAAE,CAAC;UAAEC,UAAU,EAAEC,IAAI,CAACC,GAAG,EAAE;UAAEC,WAAW,EAAE;QAAE;OACvF,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;EACAC,oBAAoBA,CAACzC,KAAa,EAAE0C,KAAA,GAAgB,EAAE,EAAEC,IAAA,GAAe,KAAK;IAC1E,MAAMC,QAAQ,GAAG,GAAG5C,KAAK,IAAI0C,KAAK,IAAIC,IAAI,EAAE;IAC5C,MAAME,MAAM,GAAG,IAAI,CAAClD,gBAAgB,CAAC4B,GAAG,CAACqB,QAAQ,CAAC;IAElD;IACA,IAAIC,MAAM,IAAIP,IAAI,CAACC,GAAG,EAAE,GAAGM,MAAM,CAACC,SAAS,GAAG,MAAM,EAAE;MACpD,OAAO3E,EAAE,CAAC0E,MAAM,CAACE,IAAI,CAAC;;IAGxB,IAAInC,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAC1B6C,GAAG,CAAC,OAAO,EAAE6B,KAAK,CAACpB,QAAQ,EAAE,CAAC,CAC9BT,GAAG,CAAC,MAAM,EAAE8B,IAAI,CAAC;IAEpB,IAAI3C,KAAK,EAAEY,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,GAAG,EAAEb,KAAK,CAAC;IAE1C,OAAO,IAAI,CAACpB,IAAI,CAAC2C,GAAG,CAAwD,GAAG,IAAI,CAACzC,OAAO,qBAAqB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CAChIvB,GAAG,CAACwE,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACrB,OAAO,EAAE;QACpB,IAAI,CAAChC,gBAAgB,CAACkB,GAAG,CAAC+B,QAAQ,EAAE;UAClCG,IAAI,EAAEC,QAAQ,CAACR,WAAW;UAC1BM,SAAS,EAAER,IAAI,CAACC,GAAG;SACpB,CAAC;QACF,IAAI,CAACpD,wBAAwB,CAACkB,IAAI,CAAC2C,QAAQ,CAACR,WAAW,CAAC;;IAE5D,CAAC,CAAC,EACFlE,SAAS,CAAC0E,QAAQ,IAAI7E,EAAE,CAAC6E,QAAQ,CAACR,WAAW,IAAI,EAAE,CAAC,CAAC,EACrDjE,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOtD,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA;EACA8E,mBAAmBA,CAACP,KAAA,GAAgB,EAAE,EAAEQ,SAAA,GAAoB,KAAK;IAC/D;IACA,IAAI,IAAI,CAACrD,aAAa,IAAIyC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC1C,aAAa,CAACiD,SAAS,GAAG,MAAM,EAAE;MAC5E,OAAO3E,EAAE,CAAC,IAAI,CAAC0B,aAAa,CAACkD,IAAI,CAAC;;IAGpC,MAAMnC,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAC5B6C,GAAG,CAAC,OAAO,EAAE6B,KAAK,CAACpB,QAAQ,EAAE,CAAC,CAC9BT,GAAG,CAAC,WAAW,EAAEqC,SAAS,CAAC;IAE9B,OAAO,IAAI,CAACtE,IAAI,CAAC2C,GAAG,CAAmD,GAAG,IAAI,CAACzC,OAAO,kBAAkB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CACxHvB,GAAG,CAACwE,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACrB,OAAO,EAAE;QACpB,IAAI,CAAC9B,aAAa,GAAG;UACnBkD,IAAI,EAAEC,QAAQ,CAACG,QAAQ;UACvBL,SAAS,EAAER,IAAI,CAACC,GAAG;SACpB;;IAEL,CAAC,CAAC,EACFjE,SAAS,CAAC0E,QAAQ,IAAI7E,EAAE,CAAC6E,QAAQ,CAACG,QAAQ,IAAI,EAAE,CAAC,CAAC,EAClD5E,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAOtD,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA;EACAiF,gBAAgBA,CAACV,KAAA,GAAgB,EAAE,EAAEC,IAAA,GAAe,QAAQ;IAC1D,IAAI,CAAC,IAAI,CAAC9D,WAAW,CAACwE,eAAe,EAAE;MACrC,OAAOlF,EAAE,CAAC;QAAEmF,QAAQ,EAAE,EAAE;QAAEC,SAAS,EAAE,IAAI,CAACC,mBAAmB;MAAE,CAAE,CAAC;;IAGpE,MAAM5C,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAC5B6C,GAAG,CAAC,OAAO,EAAE6B,KAAK,CAACpB,QAAQ,EAAE,CAAC,CAC9BT,GAAG,CAAC,MAAM,EAAE8B,IAAI,CAAC;IAEpB,OAAO,IAAI,CAAC/D,IAAI,CAAC2C,GAAG,CAAM,GAAG,IAAI,CAACzC,OAAO,iBAAiB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CAC1EzB,SAAS,CAAC0E,QAAQ,IAAI7E,EAAE,CAAC;MACvBmF,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,IAAI,EAAE;MACjCC,SAAS,EAAEP,QAAQ,CAACO,SAAS,IAAI,IAAI,CAACC,mBAAmB;KAC1D,CAAC,CAAC,EACHjF,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOtD,EAAE,CAAC;QAAEmF,QAAQ,EAAE,EAAE;QAAEC,SAAS,EAAE,IAAI,CAACC,mBAAmB;MAAE,CAAE,CAAC;IACpE,CAAC,CAAC,CACH;EACH;EAEA;EACAC,kBAAkBA,CAACd,IAAA,GAAe,KAAK;IACrC,IAAI,CAAC,IAAI,CAAC9D,WAAW,CAACwE,eAAe,EAAE;MACrC,OAAOlF,EAAE,CAAC,KAAK,CAAC;;IAGlB,MAAMyC,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAAC6C,GAAG,CAAC,MAAM,EAAE8B,IAAI,CAAC;IAEjD,OAAO,IAAI,CAAC/D,IAAI,CAAC8E,MAAM,CAAuB,GAAG,IAAI,CAAC5E,OAAO,iBAAiB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CAC9FzB,SAAS,CAAC0E,QAAQ,IAAI7E,EAAE,CAAC6E,QAAQ,CAACrB,OAAO,CAAC,CAAC,EAC3CpD,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAOtD,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACH;EAEA;EACAwF,sBAAsBA,CAACC,WAAmB,EAAEC,SAAiB,EAAEC,MAAc,EAAEC,QAAiB,EAAEC,QAAc;IAC9G,IAAI,CAAC,IAAI,CAACnF,WAAW,CAACwE,eAAe,EAAE;MACrC,OAAOlF,EAAE,CAAC,KAAK,CAAC;;IAGlB,MAAM8F,IAAI,GAAG;MACXL,WAAW;MACXC,SAAS;MACTC,MAAM;MACNC,QAAQ;MACRC;KACD;IAED,OAAO,IAAI,CAACpF,IAAI,CAACsF,IAAI,CAAuB,GAAG,IAAI,CAACpF,OAAO,eAAe,EAAEmF,IAAI,CAAC,CAAClE,IAAI,CACpFzB,SAAS,CAAC0E,QAAQ,IAAI7E,EAAE,CAAC6E,QAAQ,CAACrB,OAAO,CAAC,CAAC,EAC3CpD,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOtD,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACH;EAEA;EACAgG,iBAAiBA,CAACP,WAAmB,EAAEC,SAAiB,EAAEE,QAAgB;IACxE,OAAO,IAAI,CAACJ,sBAAsB,CAACC,WAAW,EAAEC,SAAS,EAAE,OAAO,EAAEE,QAAQ,CAAC;EAC/E;EAEAK,oBAAoBA,CAACR,WAAmB,EAAEC,SAAiB;IACzD,OAAO,IAAI,CAACF,sBAAsB,CAACC,WAAW,EAAEC,SAAS,EAAE,UAAU,CAAC;EACxE;EAEAQ,mBAAmBA,CAACT,WAAmB,EAAEU,QAAgB;IACvD,OAAO,IAAI,CAACX,sBAAsB,CAACC,WAAW,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;MAAEU;IAAQ,CAAE,CAAC;EACvF;EAEAC,iBAAiBA,CAACX,WAAmB;IACnC,OAAO,IAAI,CAACD,sBAAsB,CAACC,WAAW,EAAE,EAAE,EAAE,eAAe,CAAC;EACtE;EAEA;EACAY,kBAAkBA,CAACtB,SAAA,GAAoB,IAAI,EAAER,KAAA,GAAgB,EAAE;IAC7D,MAAM9B,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAC5B6C,GAAG,CAAC,WAAW,EAAEqC,SAAS,CAAC,CAC3BrC,GAAG,CAAC,OAAO,EAAE6B,KAAK,CAACpB,QAAQ,EAAE,CAAC;IAEjC,OAAO,IAAI,CAAC1C,IAAI,CAAC2C,GAAG,CAAM,GAAG,IAAI,CAACzC,OAAO,mBAAmB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CAC5ExB,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAOtD,EAAE,CAAC;QACRwD,OAAO,EAAE,KAAK;QACd4B,SAAS,EAAE;UACTkB,QAAQ,EAAE,IAAI,CAACjB,mBAAmB,EAAE;UACpCkB,gBAAgB,EAAE;;OAErB,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAAC5F,kBAAkB,CAACsB,IAAI,CAAC,EAAE,CAAC;IAChC,IAAI,CAACrB,oBAAoB,CAACqB,IAAI,CAAC,EAAE,CAAC;IAClC,IAAI,CAACpB,oBAAoB,CAACoB,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACnB,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;IACrC,IAAI,CAAClB,wBAAwB,CAACkB,IAAI,CAAC,EAAE,CAAC;EACxC;EAEA;EACAuE,WAAWA,CAAA;IACT,IAAI,CAACjF,gBAAgB,CAACkF,KAAK,EAAE;IAC7B,IAAI,CAAChF,aAAa,GAAG,IAAI;EAC3B;EAEA;EACAiF,qBAAqBA,CAAA;IAMnB,OAAO;MACL9E,KAAK,EAAE,IAAI,CAACjB,kBAAkB,CAACqB,KAAK;MACpCK,OAAO,EAAE,IAAI,CAACzB,oBAAoB,CAACoB,KAAK;MACxC2E,OAAO,EAAE,IAAI,CAAC9F,oBAAoB,CAACmB,KAAK;MACxC4E,OAAO,EAAE,IAAI,CAAC9F,oBAAoB,CAACkB;KACpC;EACH;EAEA;EACA6E,yBAAyBA,CAACjF,KAAa,EAAEkF,WAAiB;IACxD,MAAMtE,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAC5B6C,GAAG,CAAC,GAAG,EAAEb,KAAK,CAAC,CACfa,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CACpBA,GAAG,CAAC,SAAS,EAAEsE,IAAI,CAACC,SAAS,CAACF,WAAW,IAAI,EAAE,CAAC,CAAC;IAEpD,OAAO,IAAI,CAACtG,IAAI,CAAC2C,GAAG,CAAsC,GAAG,IAAI,CAACzC,OAAO,2BAA2B,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAC9Gb,IAAI,CACHtB,GAAG,CAACuE,QAAQ,IAAIA,QAAQ,CAACR,WAAW,IAAI,EAAE,CAAC,EAC3CjE,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI,CAACgB,oBAAoB,CAACzC,KAAK,CAAC;IACzC,CAAC,CAAC,CACH;EACL;EAEA;EACAqF,aAAaA,CAACC,SAAe;IAC3B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,SAAS,CAAC;IAEnC,IAAI,CAACpG,oBAAoB,CAACmB,IAAI,CAAC,IAAI,CAAC;IAEpC,OAAO,IAAI,CAACzB,IAAI,CAACsF,IAAI,CAAe,GAAG,IAAI,CAACpF,OAAO,gBAAgB,EAAEyG,QAAQ,CAAC,CAACxF,IAAI,CACjFvB,GAAG,CAACgD,MAAM,IAAG;MACX,IAAI,CAACvC,oBAAoB,CAACoB,IAAI,CAACmB,MAAM,CAAC;MACtC,IAAI,CAACtC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;IACvC,CAAC,CAAC,EACF9B,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,IAAI,CAACvC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;MACrC,OAAOlC,EAAE,CAAC;QACRwD,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAK,CAAE;QAC9EC,UAAU,EAAE;UACVnC,KAAK,EAAE,eAAe;UACtBS,OAAO,EAAE,EAAE;UACX2B,YAAY,EAAE,CAAC;UACfC,UAAU,EAAEC,IAAI,CAACC,GAAG,EAAE;UACtBC,WAAW,EAAE;;OAEhB,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;EACAkD,eAAeA,CAACC,OAAe;IAC7B,MAAM/E,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAAC6C,GAAG,CAAC,SAAS,EAAE8E,OAAO,CAAC;IAEvD,IAAI,CAACzG,oBAAoB,CAACmB,IAAI,CAAC,IAAI,CAAC;IAEpC,OAAO,IAAI,CAACzB,IAAI,CAAC2C,GAAG,CAAe,GAAG,IAAI,CAACzC,OAAO,iBAAiB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CACnFvB,GAAG,CAACgD,MAAM,IAAG;MACX,IAAI,CAACvC,oBAAoB,CAACoB,IAAI,CAACmB,MAAM,CAAC;MACtC,IAAI,CAACtC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;IACvC,CAAC,CAAC,EACF9B,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACvC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;MACrC,OAAOlC,EAAE,CAAC;QACRwD,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAK,CAAE;QAC9EC,UAAU,EAAE;UACVnC,KAAK,EAAE,YAAY2F,OAAO,EAAE;UAC5BlF,OAAO,EAAE,EAAE;UACX2B,YAAY,EAAE,CAAC;UACfC,UAAU,EAAEC,IAAI,CAACC,GAAG,EAAE;UACtBC,WAAW,EAAE;;OAEhB,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;EACAoD,qBAAqBA,CAAC/B,SAAiB,EAAEnB,KAAA,GAAgB,EAAE;IACzD,MAAM9B,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAC5B6C,GAAG,CAAC,WAAW,EAAEgD,SAAS,CAAC,CAC3BhD,GAAG,CAAC,OAAO,EAAE6B,KAAK,CAACpB,QAAQ,EAAE,CAAC;IAEjC,IAAI,CAACpC,oBAAoB,CAACmB,IAAI,CAAC,IAAI,CAAC;IAEpC,OAAO,IAAI,CAACzB,IAAI,CAAC2C,GAAG,CAAe,GAAG,IAAI,CAACzC,OAAO,iBAAiB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CAACb,IAAI,CACnFvB,GAAG,CAACgD,MAAM,IAAG;MACX,IAAI,CAACvC,oBAAoB,CAACoB,IAAI,CAACmB,MAAM,CAAC;MACtC,IAAI,CAACtC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;IACvC,CAAC,CAAC,EACF9B,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAACvC,oBAAoB,CAACmB,IAAI,CAAC,KAAK,CAAC;MACrC,OAAOlC,EAAE,CAAC;QACRwD,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAK,CAAE;QAC9EC,UAAU,EAAE;UACVnC,KAAK,EAAE,kBAAkB;UACzBS,OAAO,EAAE,EAAE;UACX2B,YAAY,EAAE,CAAC;UACfC,UAAU,EAAEC,IAAI,CAACC,GAAG,EAAE;UACtBC,WAAW,EAAE;;OAEhB,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;EACAqD,8BAA8BA,CAACnD,KAAA,GAAgB,EAAE;IAC/C,IAAI,CAAC,IAAI,CAAC7D,WAAW,CAACwE,eAAe,EAAE;MACrC,OAAO,IAAI,CAACJ,mBAAmB,CAACP,KAAK,CAAC,CAAC3C,IAAI,CACzCtB,GAAG,CAAC0E,QAAQ,IAAIA,QAAQ,CAAC1E,GAAG,CAACqH,CAAC,KAAK;QACjCC,IAAI,EAAED,CAAC,CAAC9F,KAAK;QACb2C,IAAI,EAAE,UAAmB;QACzBqD,UAAU,EAAEF,CAAC,CAACxC;OACf,CAAC,CAAC,CAAC,CACL;;IAGH,MAAM1C,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAAC6C,GAAG,CAAC,OAAO,EAAE6B,KAAK,CAACpB,QAAQ,EAAE,CAAC;IAE9D,OAAO,IAAI,CAAC1C,IAAI,CAAC2C,GAAG,CAAsC,GAAG,IAAI,CAACzC,OAAO,sBAAsB,EAAE;MAAE8B;IAAM,CAAE,CAAC,CACzGb,IAAI,CACHtB,GAAG,CAACuE,QAAQ,IAAIA,QAAQ,CAACR,WAAW,IAAI,EAAE,CAAC,EAC3CjE,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,OAAO,IAAI,CAACwB,mBAAmB,CAACP,KAAK,CAAC,CAAC3C,IAAI,CACzCtB,GAAG,CAAC0E,QAAQ,IAAIA,QAAQ,CAAC1E,GAAG,CAACqH,CAAC,KAAK;QACjCC,IAAI,EAAED,CAAC,CAAC9F,KAAK;QACb2C,IAAI,EAAE,UAAmB;QACzBqD,UAAU,EAAEF,CAAC,CAACxC;OACf,CAAC,CAAC,CAAC,CACL;IACH,CAAC,CAAC,CACH;EACL;EAEA;EACA2C,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACpH,WAAW,CAACwE,eAAe,EAAE;MACrC,OAAOlF,EAAE,CAAC,EAAE,CAAC;;IAGf,OAAO,IAAI,CAACS,IAAI,CAAC2C,GAAG,CAAM,GAAG,IAAI,CAACzC,OAAO,kBAAkB,CAAC,CAACiB,IAAI,CAC/DxB,UAAU,CAACkD,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOtD,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA;EACQqF,mBAAmBA,CAAA;IACzB,OAAO;MACL0C,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE,CAAC;MACnBC,cAAc,EAAE,CAAC;MACjBC,WAAW,EAAE;QACXC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,oBAAoB,EAAE;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE;QAAK;;KAE7C;EACH;;;uBA1dWjI,aAAa,EAAAkI,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAbvI,aAAa;MAAAwI,OAAA,EAAbxI,aAAa,CAAAyI,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}