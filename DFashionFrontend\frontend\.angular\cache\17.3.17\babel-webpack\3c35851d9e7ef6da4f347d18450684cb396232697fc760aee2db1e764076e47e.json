{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"@angular/common\";\nfunction CategoryComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_button_9_Template_button_click_0_listener() {\n      const filter_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeFilter(filter_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"i\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filter_r2.label, \" \");\n  }\n}\nfunction CategoryComponent_div_10_div_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(2, 1, product_r5.originalPrice), \"\");\n  }\n}\nfunction CategoryComponent_div_10_div_1_div_15_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    i0.ɵɵclassMap(star_r6);\n  }\n}\nfunction CategoryComponent_div_10_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtemplate(2, CategoryComponent_div_10_div_1_div_15_i_2_Template, 1, 2, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStars(product_r5.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n  }\n}\nfunction CategoryComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_10_div_1_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r5._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementStart(3, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function CategoryComponent_div_10_div_1_Template_button_click_3_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleWishlist(product_r5._id, $event));\n    });\n    i0.ɵɵelement(4, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 17)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 19)(11, \"span\", 20);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CategoryComponent_div_10_div_1_span_14_Template, 3, 3, \"span\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, CategoryComponent_div_10_div_1_div_15_Template, 5, 2, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r5.images[0] == null ? null : product_r5.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r5.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(13, 7, product_r5.price), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r5.originalPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.rating);\n  }\n}\nfunction CategoryComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, CategoryComponent_div_10_div_1_Template, 16, 9, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.products);\n  }\n}\nfunction CategoryComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading products...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CategoryComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"i\", 30);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No products found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Try adjusting your filters or search terms\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CategoryComponent = /*#__PURE__*/(() => {\n  class CategoryComponent {\n    constructor(route, router, productService) {\n      this.route = route;\n      this.router = router;\n      this.productService = productService;\n      this.categoryName = '';\n      this.products = [];\n      this.productCount = 0;\n      this.isLoading = true;\n      this.activeFilters = [];\n    }\n    ngOnInit() {\n      this.route.params.subscribe(params => {\n        this.categoryName = params['category'];\n        this.loadProducts();\n      });\n    }\n    loadProducts() {\n      this.isLoading = true;\n      // Load from real API\n      this.products = [];\n      this.productCount = 0;\n      this.isLoading = false;\n    }\n    viewProduct(productId) {\n      this.router.navigate(['/product', productId]);\n    }\n    toggleWishlist(productId, event) {\n      event.stopPropagation();\n      console.log('Toggle wishlist for:', productId);\n    }\n    removeFilter(filter) {\n      this.activeFilters = this.activeFilters.filter(f => f !== filter);\n      this.loadProducts();\n    }\n    getStars(rating) {\n      const stars = [];\n      for (let i = 1; i <= 5; i++) {\n        if (i <= rating) {\n          stars.push('fas fa-star');\n        } else if (i - 0.5 <= rating) {\n          stars.push('fas fa-star-half-alt');\n        } else {\n          stars.push('far fa-star');\n        }\n      }\n      return stars;\n    }\n    static {\n      this.ɵfac = function CategoryComponent_Factory(t) {\n        return new (t || CategoryComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CategoryComponent,\n        selectors: [[\"app-category\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 8,\n        consts: [[1, \"category-page\"], [1, \"category-header\"], [1, \"filters-section\"], [1, \"filter-chips\"], [\"class\", \"filter-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"filter-chip\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [1, \"wishlist-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"rating\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"rating\"], [1, \"stars\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-container\"], [1, \"spinner\"], [1, \"empty-state\"], [1, \"fas\", \"fa-search\"]],\n        template: function CategoryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3);\n            i0.ɵɵpipe(4, \"titlecase\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 2)(8, \"div\", 3);\n            i0.ɵɵtemplate(9, CategoryComponent_button_9_Template, 3, 1, \"button\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(10, CategoryComponent_div_10_Template, 2, 1, \"div\", 5)(11, CategoryComponent_div_11_Template, 4, 0, \"div\", 6)(12, CategoryComponent_div_12_Template, 6, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, ctx.categoryName));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", ctx.productCount, \" products found\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.activeFilters);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe, i3.TitleCasePipe],\n        styles: [\".category-page[_ngcontent-%COMP%]{padding:2rem;max-width:1200px;margin:0 auto}.category-header[_ngcontent-%COMP%]{margin-bottom:2rem;text-align:center}.category-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin-bottom:.5rem;color:#333}.category-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:1.1rem}.filters-section[_ngcontent-%COMP%]{margin-bottom:2rem}.filter-chips[_ngcontent-%COMP%]{display:flex;gap:.5rem;flex-wrap:wrap}.filter-chip[_ngcontent-%COMP%]{background:#f0f0f0;border:none;padding:.5rem 1rem;border-radius:20px;display:flex;align-items:center;gap:.5rem;cursor:pointer;transition:background .2s}.filter-chip[_ngcontent-%COMP%]:hover{background:#e0e0e0}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:2rem}.product-card[_ngcontent-%COMP%]{border:1px solid #eee;border-radius:12px;overflow:hidden;cursor:pointer;transition:transform .2s,box-shadow .2s}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 25px #0000001a}.product-image[_ngcontent-%COMP%]{position:relative;aspect-ratio:1;overflow:hidden}.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.wishlist-btn[_ngcontent-%COMP%]{position:absolute;top:1rem;right:1rem;width:40px;height:40px;border-radius:50%;background:#ffffffe6;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s}.wishlist-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-info[_ngcontent-%COMP%]{padding:1rem}.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:.5rem;color:#333}.brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:.5rem}.price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem}.current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#e91e63}.original-price[_ngcontent-%COMP%]{font-size:1rem;color:#999;text-decoration:line-through}.rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.stars[_ngcontent-%COMP%]{display:flex;gap:2px}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107;font-size:.9rem}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem;color:#666}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem;color:#ddd}@media (max-width: 768px){.category-page[_ngcontent-%COMP%]{padding:1rem}.products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}}\"]\n      });\n    }\n  }\n  return CategoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}