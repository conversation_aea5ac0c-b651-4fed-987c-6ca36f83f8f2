{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/storage-angular\";\nexport let StorageService = /*#__PURE__*/(() => {\n  class StorageService {\n    constructor(storage) {\n      this.storage = storage;\n      this._storage = null;\n      this.init();\n    }\n    init() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        // If running on the web, Storage will only use IndexedDB, LocalStorage and SessionStorage\n        const storage = yield _this.storage.create();\n        _this._storage = storage;\n      })();\n    }\n    // Create and expose methods that users of this service can call\n    set(key, value) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        return _this2._storage?.set(key, value);\n      })();\n    }\n    get(key) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        return _this3._storage?.get(key);\n      })();\n    }\n    remove(key) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        return _this4._storage?.remove(key);\n      })();\n    }\n    clear() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        return _this5._storage?.clear();\n      })();\n    }\n    keys() {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        return _this6._storage?.keys() || [];\n      })();\n    }\n    length() {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        return _this7._storage?.length() || 0;\n      })();\n    }\n    // Convenience methods for common operations\n    setUser(user) {\n      var _this8 = this;\n      return _asyncToGenerator(function* () {\n        return _this8.set('user', user);\n      })();\n    }\n    getUser() {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        return _this9.get('user');\n      })();\n    }\n    removeUser() {\n      var _this0 = this;\n      return _asyncToGenerator(function* () {\n        return _this0.remove('user');\n      })();\n    }\n    setToken(token) {\n      var _this1 = this;\n      return _asyncToGenerator(function* () {\n        return _this1.set('auth_token', token);\n      })();\n    }\n    getToken() {\n      var _this10 = this;\n      return _asyncToGenerator(function* () {\n        return _this10.get('auth_token');\n      })();\n    }\n    removeToken() {\n      var _this11 = this;\n      return _asyncToGenerator(function* () {\n        return _this11.remove('auth_token');\n      })();\n    }\n    setCart(cart) {\n      var _this12 = this;\n      return _asyncToGenerator(function* () {\n        return _this12.set('cart', cart);\n      })();\n    }\n    getCart() {\n      var _this13 = this;\n      return _asyncToGenerator(function* () {\n        return _this13.get('cart') || [];\n      })();\n    }\n    clearCart() {\n      var _this14 = this;\n      return _asyncToGenerator(function* () {\n        return _this14.remove('cart');\n      })();\n    }\n    setWishlist(wishlist) {\n      var _this15 = this;\n      return _asyncToGenerator(function* () {\n        return _this15.set('wishlist', wishlist);\n      })();\n    }\n    getWishlist() {\n      var _this16 = this;\n      return _asyncToGenerator(function* () {\n        return _this16.get('wishlist') || [];\n      })();\n    }\n    clearWishlist() {\n      var _this17 = this;\n      return _asyncToGenerator(function* () {\n        return _this17.remove('wishlist');\n      })();\n    }\n    setSettings(settings) {\n      var _this18 = this;\n      return _asyncToGenerator(function* () {\n        return _this18.set('app_settings', settings);\n      })();\n    }\n    getSettings() {\n      var _this19 = this;\n      return _asyncToGenerator(function* () {\n        return _this19.get('app_settings') || {};\n      })();\n    }\n    setOnboardingCompleted(completed) {\n      var _this20 = this;\n      return _asyncToGenerator(function* () {\n        return _this20.set('onboarding_completed', completed);\n      })();\n    }\n    isOnboardingCompleted() {\n      var _this21 = this;\n      return _asyncToGenerator(function* () {\n        return _this21.get('onboarding_completed') || false;\n      })();\n    }\n    static {\n      this.ɵfac = function StorageService_Factory(t) {\n        return new (t || StorageService)(i0.ɵɵinject(i1.Storage));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: StorageService,\n        factory: StorageService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return StorageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}