{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, createComponent, EnvironmentInjector, ANIMATION_MODULE_TYPE, booleanAttribute, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { InteractivityChecker, A11yModule } from '@angular/cdk/a11y';\nimport { DOCUMENT } from '@angular/common';\nlet nextId = 0;\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/** Keeps track of the apps currently containing badges. */\nconst badgeApps = /*#__PURE__*/new Set();\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\nlet _MatBadgeStyleLoader = /*#__PURE__*/(() => {\n  class _MatBadgeStyleLoader {\n    static {\n      this.ɵfac = function _MatBadgeStyleLoader_Factory(t) {\n        return new (t || _MatBadgeStyleLoader)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: _MatBadgeStyleLoader,\n        selectors: [[\"ng-component\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 0,\n        vars: 0,\n        template: function _MatBadgeStyleLoader_Template(rf, ctx) {},\n        styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:var(--mat-badge-text-font);font-weight:var(--mat-badge-text-weight);border-radius:var(--mat-badge-container-shape)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, unset);min-height:var(--mat-badge-small-size-container-size, unset);line-height:var(--mat-badge-legacy-small-size-container-size, var(--mat-badge-small-size-container-size));padding:var(--mat-badge-small-size-container-padding);font-size:var(--mat-badge-small-size-text-size);margin:var(--mat-badge-small-size-container-offset)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, unset);min-height:var(--mat-badge-container-size, unset);line-height:var(--mat-badge-legacy-container-size, var(--mat-badge-container-size));padding:var(--mat-badge-container-padding);font-size:var(--mat-badge-text-size);margin:var(--mat-badge-container-offset)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, unset);min-height:var(--mat-badge-large-size-container-size, unset);line-height:var(--mat-badge-legacy-large-size-container-size, var(--mat-badge-large-size-container-size));padding:var(--mat-badge-large-size-container-padding);font-size:var(--mat-badge-large-size-text-size);margin:var(--mat-badge-large-size-container-offset)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset)}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return _MatBadgeStyleLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive to display a text badge. */\nlet MatBadge = /*#__PURE__*/(() => {\n  class MatBadge {\n    /** The color of the badge. Can be `primary`, `accent`, or `warn`. */\n    get color() {\n      return this._color;\n    }\n    set color(value) {\n      this._setColor(value);\n      this._color = value;\n    }\n    /** The content for the badge */\n    get content() {\n      return this._content;\n    }\n    set content(newContent) {\n      this._updateRenderedContent(newContent);\n    }\n    /** Message used to describe the decorated element via aria-describedby */\n    get description() {\n      return this._description;\n    }\n    set description(newDescription) {\n      this._updateDescription(newDescription);\n    }\n    constructor(_ngZone, _elementRef, _ariaDescriber, _renderer, _animationMode) {\n      this._ngZone = _ngZone;\n      this._elementRef = _elementRef;\n      this._ariaDescriber = _ariaDescriber;\n      this._renderer = _renderer;\n      this._animationMode = _animationMode;\n      this._color = 'primary';\n      /** Whether the badge should overlap its contents or not */\n      this.overlap = true;\n      /**\n       * Position the badge should reside.\n       * Accepts any combination of 'above'|'below' and 'before'|'after'\n       */\n      this.position = 'above after';\n      /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n      this.size = 'medium';\n      /** Unique id for the badge */\n      this._id = nextId++;\n      /** Whether the OnInit lifecycle hook has run yet */\n      this._isInitialized = false;\n      /** InteractivityChecker to determine if the badge host is focusable. */\n      this._interactivityChecker = inject(InteractivityChecker);\n      this._document = inject(DOCUMENT);\n      const appRef = inject(ApplicationRef);\n      if (!badgeApps.has(appRef)) {\n        badgeApps.add(appRef);\n        const componentRef = createComponent(_MatBadgeStyleLoader, {\n          environmentInjector: inject(EnvironmentInjector)\n        });\n        appRef.onDestroy(() => {\n          badgeApps.delete(appRef);\n          if (badgeApps.size === 0) {\n            componentRef.destroy();\n          }\n        });\n      }\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        const nativeElement = _elementRef.nativeElement;\n        if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n          throw Error('matBadge must be attached to an element node.');\n        }\n        const matIconTagName = 'mat-icon';\n        // Heads-up for developers to avoid putting matBadge on <mat-icon>\n        // as it is aria-hidden by default docs mention this at:\n        // https://material.angular.io/components/badge/overview#accessibility\n        if (nativeElement.tagName.toLowerCase() === matIconTagName && nativeElement.getAttribute('aria-hidden') === 'true') {\n          console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` + `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` + `\\n${nativeElement.outerHTML}`);\n        }\n      }\n    }\n    /** Whether the badge is above the host or not */\n    isAbove() {\n      return this.position.indexOf('below') === -1;\n    }\n    /** Whether the badge is after the host or not */\n    isAfter() {\n      return this.position.indexOf('before') === -1;\n    }\n    /**\n     * Gets the element into which the badge's content is being rendered. Undefined if the element\n     * hasn't been created (e.g. if the badge doesn't have content).\n     */\n    getBadgeElement() {\n      return this._badgeElement;\n    }\n    ngOnInit() {\n      // We may have server-side rendered badge that we need to clear.\n      // We need to do this in ngOnInit because the full content of the component\n      // on which the badge is attached won't necessarily be in the DOM until this point.\n      this._clearExistingBadges();\n      if (this.content && !this._badgeElement) {\n        this._badgeElement = this._createBadgeElement();\n        this._updateRenderedContent(this.content);\n      }\n      this._isInitialized = true;\n    }\n    ngOnDestroy() {\n      // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n      // We have to destroy it ourselves, otherwise it'll be retained in memory.\n      if (this._renderer.destroyNode) {\n        this._renderer.destroyNode(this._badgeElement);\n        this._inlineBadgeDescription?.remove();\n      }\n      this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    }\n    /** Gets whether the badge's host element is interactive. */\n    _isHostInteractive() {\n      // Ignore visibility since it requires an expensive style caluclation.\n      return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n        ignoreVisibility: true\n      });\n    }\n    /** Creates the badge element */\n    _createBadgeElement() {\n      const badgeElement = this._renderer.createElement('span');\n      const activeClass = 'mat-badge-active';\n      badgeElement.setAttribute('id', `mat-badge-content-${this._id}`);\n      // The badge is aria-hidden because we don't want it to appear in the page's navigation\n      // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n      badgeElement.setAttribute('aria-hidden', 'true');\n      badgeElement.classList.add(BADGE_CONTENT_CLASS);\n      if (this._animationMode === 'NoopAnimations') {\n        badgeElement.classList.add('_mat-animation-noopable');\n      }\n      this._elementRef.nativeElement.appendChild(badgeElement);\n      // animate in after insertion\n      if (typeof requestAnimationFrame === 'function' && this._animationMode !== 'NoopAnimations') {\n        this._ngZone.runOutsideAngular(() => {\n          requestAnimationFrame(() => {\n            badgeElement.classList.add(activeClass);\n          });\n        });\n      } else {\n        badgeElement.classList.add(activeClass);\n      }\n      return badgeElement;\n    }\n    /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n    _updateRenderedContent(newContent) {\n      const newContentNormalized = `${newContent ?? ''}`.trim();\n      // Don't create the badge element if the directive isn't initialized because we want to\n      // append the badge element to the *end* of the host element's content for backwards\n      // compatibility.\n      if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n        this._badgeElement = this._createBadgeElement();\n      }\n      if (this._badgeElement) {\n        this._badgeElement.textContent = newContentNormalized;\n      }\n      this._content = newContentNormalized;\n    }\n    /** Updates the host element's aria description via AriaDescriber. */\n    _updateDescription(newDescription) {\n      // Always start by removing the aria-describedby; we will add a new one if necessary.\n      this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n      // NOTE: We only check whether the host is interactive here, which happens during\n      // when then badge content changes. It is possible that the host changes\n      // interactivity status separate from one of these. However, watching the interactivity\n      // status of the host would require a `MutationObserver`, which is likely more code + overhead\n      // than it's worth; from usages inside Google, we see that the vats majority of badges either\n      // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n      if (!newDescription || this._isHostInteractive()) {\n        this._removeInlineDescription();\n      }\n      this._description = newDescription;\n      // We don't add `aria-describedby` for non-interactive hosts elements because we\n      // instead insert the description inline.\n      if (this._isHostInteractive()) {\n        this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n      } else {\n        this._updateInlineDescription();\n      }\n    }\n    _updateInlineDescription() {\n      // Create the inline description element if it doesn't exist\n      if (!this._inlineBadgeDescription) {\n        this._inlineBadgeDescription = this._document.createElement('span');\n        this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n      }\n      this._inlineBadgeDescription.textContent = this.description;\n      this._badgeElement?.appendChild(this._inlineBadgeDescription);\n    }\n    _removeInlineDescription() {\n      this._inlineBadgeDescription?.remove();\n      this._inlineBadgeDescription = undefined;\n    }\n    /** Adds css theme class given the color to the component host */\n    _setColor(colorPalette) {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove(`mat-badge-${this._color}`);\n      if (colorPalette) {\n        classList.add(`mat-badge-${colorPalette}`);\n      }\n    }\n    /** Clears any existing badges that might be left over from server-side rendering. */\n    _clearExistingBadges() {\n      // Only check direct children of this host element in order to avoid deleting\n      // any badges that might exist in descendant elements.\n      const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n      for (const badgeElement of Array.from(badges)) {\n        if (badgeElement !== this._badgeElement) {\n          badgeElement.remove();\n        }\n      }\n    }\n    static {\n      this.ɵfac = function MatBadge_Factory(t) {\n        return new (t || MatBadge)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.AriaDescriber), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatBadge,\n        selectors: [[\"\", \"matBadge\", \"\"]],\n        hostAttrs: [1, \"mat-badge\"],\n        hostVars: 20,\n        hostBindings: function MatBadge_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-badge-overlap\", ctx.overlap)(\"mat-badge-above\", ctx.isAbove())(\"mat-badge-below\", !ctx.isAbove())(\"mat-badge-before\", !ctx.isAfter())(\"mat-badge-after\", ctx.isAfter())(\"mat-badge-small\", ctx.size === \"small\")(\"mat-badge-medium\", ctx.size === \"medium\")(\"mat-badge-large\", ctx.size === \"large\")(\"mat-badge-hidden\", ctx.hidden || !ctx.content)(\"mat-badge-disabled\", ctx.disabled);\n          }\n        },\n        inputs: {\n          color: [i0.ɵɵInputFlags.None, \"matBadgeColor\", \"color\"],\n          overlap: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matBadgeOverlap\", \"overlap\", booleanAttribute],\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matBadgeDisabled\", \"disabled\", booleanAttribute],\n          position: [i0.ɵɵInputFlags.None, \"matBadgePosition\", \"position\"],\n          content: [i0.ɵɵInputFlags.None, \"matBadge\", \"content\"],\n          description: [i0.ɵɵInputFlags.None, \"matBadgeDescription\", \"description\"],\n          size: [i0.ɵɵInputFlags.None, \"matBadgeSize\", \"size\"],\n          hidden: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matBadgeHidden\", \"hidden\", booleanAttribute]\n        },\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return MatBadge;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatBadgeModule = /*#__PURE__*/(() => {\n  class MatBadgeModule {\n    static {\n      this.ɵfac = function MatBadgeModule_Factory(t) {\n        return new (t || MatBadgeModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatBadgeModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [A11yModule, MatCommonModule, MatCommonModule]\n      });\n    }\n  }\n  return MatBadgeModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatBadge, MatBadgeModule };\n//# sourceMappingURL=badge.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}