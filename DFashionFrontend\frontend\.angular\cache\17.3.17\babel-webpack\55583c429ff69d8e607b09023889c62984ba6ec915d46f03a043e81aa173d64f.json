{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/media.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = [\"*\"];\nfunction OptimizedImageComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"ion-spinner\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r0.loaderColor);\n  }\n}\nfunction OptimizedImageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"ion-icon\", 8);\n    i0.ɵɵelementStart(2, \"p\", 9);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction OptimizedImageComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class OptimizedImageComponent {\n  constructor(mediaService) {\n    this.mediaService = mediaService;\n    this.src = '';\n    this.alt = '';\n    this.fallbackType = 'post';\n    this.quality = 80;\n    this.lazyLoad = true;\n    this.showLoader = true;\n    this.showErrorMessage = false;\n    this.loaderColor = 'primary';\n    this.errorMessage = 'Failed to load image';\n    this.objectFit = 'cover';\n    this.containerClass = '';\n    this.imageClass = '';\n    this.showOverlay = false;\n    this.preload = false;\n    this.imageLoad = new EventEmitter();\n    this.imageError = new EventEmitter();\n    this.currentSrc = '';\n    this.isLoading = true;\n    this.imageLoaded = false;\n    this.hasError = false;\n    this.retryCount = 0;\n    this.maxRetries = 2;\n  }\n  ngOnInit() {\n    this.loadImage();\n  }\n  ngOnDestroy() {\n    // Cleanup if needed\n  }\n  loadImage() {\n    if (!this.src) {\n      this.setFallbackImage();\n      return;\n    }\n    // Get optimized image URL\n    let optimizedSrc = this.src;\n    if (this.width || this.height) {\n      optimizedSrc = this.mediaService.optimizeImageUrl(this.src, this.width, this.height, this.quality);\n    }\n    // Use media service to get safe URL\n    this.currentSrc = this.mediaService.getSafeImageUrl(optimizedSrc, this.fallbackType);\n    // Preload if requested\n    if (this.preload) {\n      this.preloadImage();\n    }\n  }\n  preloadImage() {\n    const img = new Image();\n    img.onload = () => {\n      this.isLoading = false;\n      this.imageLoaded = true;\n    };\n    img.onerror = () => {\n      this.handleLoadError();\n    };\n    img.src = this.currentSrc;\n  }\n  onImageLoad() {\n    this.isLoading = false;\n    this.imageLoaded = true;\n    this.hasError = false;\n    this.retryCount = 0;\n    this.imageLoad.emit();\n  }\n  onImageError(event) {\n    this.handleLoadError();\n    this.imageError.emit(event);\n  }\n  handleLoadError() {\n    if (this.retryCount < this.maxRetries) {\n      this.retryCount++;\n      // Try with media service error handling\n      this.mediaService.handleImageError({\n        target: {\n          src: this.currentSrc\n        }\n      }, this.fallbackType);\n      // Get fallback URL\n      const fallbackUrl = this.mediaService.getReliableFallback(this.fallbackType);\n      if (this.currentSrc !== fallbackUrl) {\n        this.currentSrc = fallbackUrl;\n        return;\n      }\n    }\n    this.setFallbackImage();\n  }\n  setFallbackImage() {\n    this.isLoading = false;\n    this.hasError = true;\n    this.currentSrc = this.mediaService.getReliableFallback(this.fallbackType);\n  }\n  // Public methods for external control\n  reload() {\n    this.retryCount = 0;\n    this.hasError = false;\n    this.isLoading = true;\n    this.imageLoaded = false;\n    this.loadImage();\n  }\n  getResponsiveUrls() {\n    return this.mediaService.getResponsiveImageUrls(this.src);\n  }\n  static {\n    this.ɵfac = function OptimizedImageComponent_Factory(t) {\n      return new (t || OptimizedImageComponent)(i0.ɵɵdirectiveInject(i1.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OptimizedImageComponent,\n      selectors: [[\"app-optimized-image\"]],\n      inputs: {\n        src: \"src\",\n        alt: \"alt\",\n        fallbackType: \"fallbackType\",\n        width: \"width\",\n        height: \"height\",\n        quality: \"quality\",\n        lazyLoad: \"lazyLoad\",\n        showLoader: \"showLoader\",\n        showErrorMessage: \"showErrorMessage\",\n        loaderColor: \"loaderColor\",\n        errorMessage: \"errorMessage\",\n        objectFit: \"objectFit\",\n        containerClass: \"containerClass\",\n        imageClass: \"imageClass\",\n        showOverlay: \"showOverlay\",\n        preload: \"preload\"\n      },\n      outputs: {\n        imageLoad: \"imageLoad\",\n        imageError: \"imageError\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 16,\n      consts: [[1, \"optimized-image-container\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [3, \"load\", \"error\", \"src\", \"alt\", \"loading\"], [\"class\", \"error-overlay\", 4, \"ngIf\"], [\"class\", \"image-overlay\", 4, \"ngIf\"], [1, \"loading-overlay\"], [\"name\", \"crescent\", 3, \"color\"], [1, \"error-overlay\"], [\"name\", \"image-outline\", 1, \"error-icon\"], [1, \"error-text\"], [1, \"image-overlay\"]],\n      template: function OptimizedImageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, OptimizedImageComponent_div_1_Template, 2, 1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"img\", 2);\n          i0.ɵɵlistener(\"load\", function OptimizedImageComponent_Template_img_load_2_listener() {\n            return ctx.onImageLoad();\n          })(\"error\", function OptimizedImageComponent_Template_img_error_2_listener($event) {\n            return ctx.onImageError($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, OptimizedImageComponent_div_3_Template, 4, 1, \"div\", 3)(4, OptimizedImageComponent_div_4_Template, 2, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.containerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading && ctx.showLoader);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.imageClass);\n          i0.ɵɵstyleProp(\"object-fit\", ctx.objectFit);\n          i0.ɵɵclassProp(\"loaded\", ctx.imageLoaded)(\"error\", ctx.hasError);\n          i0.ɵɵproperty(\"src\", ctx.currentSrc, i0.ɵɵsanitizeUrl)(\"alt\", ctx.alt)(\"loading\", ctx.lazyLoad ? \"lazy\" : \"eager\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError && ctx.showErrorMessage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showOverlay);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, IonicModule, i3.IonIcon, i3.IonSpinner],\n      styles: [\".optimized-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\nimg[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\nimg.loaded[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\nimg.error[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 2;\\n}\\n\\n.error-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  z-index: 2;\\n  color: #6b7280;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 8px;\\n}\\n\\n.error-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n\\n.image-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 3;\\n}\\n\\n\\n\\n.responsive[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n.cover[_ngcontent-%COMP%] {\\n  object-fit: cover;\\n}\\n\\n.contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n\\n.rounded[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "IonicModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "loaderColor", "ɵɵtext", "ɵɵtextInterpolate", "errorMessage", "ɵɵprojection", "OptimizedImageComponent", "constructor", "mediaService", "src", "alt", "fallbackType", "quality", "lazyLoad", "<PERSON><PERSON><PERSON><PERSON>", "showErrorMessage", "objectFit", "containerClass", "imageClass", "showOverlay", "preload", "imageLoad", "imageError", "currentSrc", "isLoading", "imageLoaded", "<PERSON><PERSON><PERSON><PERSON>", "retryCount", "maxRetries", "ngOnInit", "loadImage", "ngOnDestroy", "setFallbackImage", "optimizedSrc", "width", "height", "optimizeImageUrl", "getSafeImageUrl", "preloadImage", "img", "Image", "onload", "onerror", "handleLoadError", "onImageLoad", "emit", "onImageError", "event", "handleImageError", "target", "fallbackUrl", "getReliable<PERSON><PERSON>back", "reload", "getResponsiveUrls", "getResponsiveImageUrls", "ɵɵdirectiveInject", "i1", "MediaService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "OptimizedImageComponent_Template", "rf", "ctx", "ɵɵtemplate", "OptimizedImageComponent_div_1_Template", "ɵɵlistener", "OptimizedImageComponent_Template_img_load_2_listener", "OptimizedImageComponent_Template_img_error_2_listener", "$event", "OptimizedImageComponent_div_3_Template", "OptimizedImageComponent_div_4_Template", "ɵɵclassMap", "ɵɵstyleProp", "ɵɵclassProp", "ɵɵsanitizeUrl", "i2", "NgIf", "i3", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\shared\\components\\optimized-image\\optimized-image.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\n\nimport { MediaService } from '../../../core/services/media.service';\n\n@Component({\n  selector: 'app-optimized-image',\n  standalone: true,\n  imports: [CommonModule, IonicModule],\n  template: `\n    <div class=\"optimized-image-container\" [class]=\"containerClass\">\n      <!-- Loading State -->\n      <div class=\"loading-overlay\" *ngIf=\"isLoading && showLoader\">\n        <ion-spinner name=\"crescent\" [color]=\"loaderColor\"></ion-spinner>\n      </div>\n\n      <!-- Main Image -->\n      <img\n        [src]=\"currentSrc\"\n        [alt]=\"alt\"\n        [class]=\"imageClass\"\n        [style.object-fit]=\"objectFit\"\n        (load)=\"onImageLoad()\"\n        (error)=\"onImageError($event)\"\n        [class.loaded]=\"imageLoaded\"\n        [class.error]=\"hasError\"\n        [loading]=\"lazyLoad ? 'lazy' : 'eager'\"\n      />\n\n      <!-- Error State -->\n      <div class=\"error-overlay\" *ngIf=\"hasError && showErrorMessage\">\n        <ion-icon name=\"image-outline\" class=\"error-icon\"></ion-icon>\n        <p class=\"error-text\">{{ errorMessage }}</p>\n      </div>\n\n      <!-- Overlay Content -->\n      <div class=\"image-overlay\" *ngIf=\"showOverlay\">\n        <ng-content></ng-content>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .optimized-image-container {\n      position: relative;\n      display: inline-block;\n      width: 100%;\n      height: 100%;\n      overflow: hidden;\n    }\n\n    img {\n      width: 100%;\n      height: 100%;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    img.loaded {\n      opacity: 1;\n    }\n\n    img.error {\n      opacity: 0.5;\n    }\n\n    .loading-overlay {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      z-index: 2;\n    }\n\n    .error-overlay {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      z-index: 2;\n      color: #6b7280;\n    }\n\n    .error-icon {\n      font-size: 2rem;\n      margin-bottom: 8px;\n    }\n\n    .error-text {\n      font-size: 0.875rem;\n      margin: 0;\n    }\n\n    .image-overlay {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      z-index: 3;\n    }\n\n    /* Responsive image styles */\n    .responsive {\n      max-width: 100%;\n      height: auto;\n    }\n\n    .cover {\n      object-fit: cover;\n    }\n\n    .contain {\n      object-fit: contain;\n    }\n\n    .rounded {\n      border-radius: 8px;\n    }\n\n    .circle {\n      border-radius: 50%;\n    }\n  `]\n})\nexport class OptimizedImageComponent implements OnInit, OnDestroy {\n  @Input() src: string = '';\n  @Input() alt: string = '';\n  @Input() fallbackType: 'user' | 'product' | 'post' | 'story' = 'post';\n  @Input() width?: number;\n  @Input() height?: number;\n  @Input() quality: number = 80;\n  @Input() lazyLoad: boolean = true;\n  @Input() showLoader: boolean = true;\n  @Input() showErrorMessage: boolean = false;\n  @Input() loaderColor: string = 'primary';\n  @Input() errorMessage: string = 'Failed to load image';\n  @Input() objectFit: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none' = 'cover';\n  @Input() containerClass: string = '';\n  @Input() imageClass: string = '';\n  @Input() showOverlay: boolean = false;\n  @Input() preload: boolean = false;\n\n  @Output() imageLoad = new EventEmitter<Event>();\n  @Output() imageError = new EventEmitter<Event>();\n\n  currentSrc: string = '';\n  isLoading: boolean = true;\n  imageLoaded: boolean = false;\n  hasError: boolean = false;\n\n  private retryCount: number = 0;\n  private maxRetries: number = 2;\n\n  constructor(private mediaService: MediaService) {}\n\n  ngOnInit() {\n    this.loadImage();\n  }\n\n  ngOnDestroy() {\n    // Cleanup if needed\n  }\n\n  private loadImage() {\n    if (!this.src) {\n      this.setFallbackImage();\n      return;\n    }\n\n    // Get optimized image URL\n    let optimizedSrc = this.src;\n    if (this.width || this.height) {\n      optimizedSrc = this.mediaService.optimizeImageUrl(this.src, this.width, this.height, this.quality);\n    }\n\n    // Use media service to get safe URL\n    this.currentSrc = this.mediaService.getSafeImageUrl(optimizedSrc, this.fallbackType);\n\n    // Preload if requested\n    if (this.preload) {\n      this.preloadImage();\n    }\n  }\n\n  private preloadImage() {\n    const img = new Image();\n    img.onload = () => {\n      this.isLoading = false;\n      this.imageLoaded = true;\n    };\n    img.onerror = () => {\n      this.handleLoadError();\n    };\n    img.src = this.currentSrc;\n  }\n\n  onImageLoad() {\n    this.isLoading = false;\n    this.imageLoaded = true;\n    this.hasError = false;\n    this.retryCount = 0;\n    this.imageLoad.emit();\n  }\n\n  onImageError(event: Event) {\n    this.handleLoadError();\n    this.imageError.emit(event);\n  }\n\n  private handleLoadError() {\n    if (this.retryCount < this.maxRetries) {\n      this.retryCount++;\n      // Try with media service error handling\n      this.mediaService.handleImageError({ target: { src: this.currentSrc } } as any, this.fallbackType);\n      \n      // Get fallback URL\n      const fallbackUrl = this.mediaService.getReliableFallback(this.fallbackType);\n      if (this.currentSrc !== fallbackUrl) {\n        this.currentSrc = fallbackUrl;\n        return;\n      }\n    }\n\n    this.setFallbackImage();\n  }\n\n  private setFallbackImage() {\n    this.isLoading = false;\n    this.hasError = true;\n    this.currentSrc = this.mediaService.getReliableFallback(this.fallbackType);\n  }\n\n  // Public methods for external control\n  reload() {\n    this.retryCount = 0;\n    this.hasError = false;\n    this.isLoading = true;\n    this.imageLoaded = false;\n    this.loadImage();\n  }\n\n  getResponsiveUrls() {\n    return this.mediaService.getResponsiveImageUrls(this.src);\n  }\n}\n"], "mappings": "AAAA,SAAmCA,YAAY,QAA2B,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IAWtCC,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAE,SAAA,qBAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAM;;;;IADyBH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAqB;;;;;IAiBpDP,EAAA,CAAAC,cAAA,aAAgE;IAC9DD,EAAA,CAAAE,SAAA,kBAA6D;IAC7DF,EAAA,CAAAC,cAAA,WAAsB;IAAAD,EAAA,CAAAQ,MAAA,GAAkB;IAC1CR,EAD0C,CAAAG,YAAA,EAAI,EACxC;;;;IADkBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAI,YAAA,CAAkB;;;;;IAI1CV,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAW,YAAA,GAAyB;IAC3BX,EAAA,CAAAG,YAAA,EAAM;;;AAuFZ,OAAM,MAAOS,uBAAuB;EA6BlCC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IA5BvB,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,YAAY,GAA0C,MAAM;IAG5D,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,QAAQ,GAAY,IAAI;IACxB,KAAAC,UAAU,GAAY,IAAI;IAC1B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAd,WAAW,GAAW,SAAS;IAC/B,KAAAG,YAAY,GAAW,sBAAsB;IAC7C,KAAAY,SAAS,GAAyD,OAAO;IACzE,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,OAAO,GAAY,KAAK;IAEvB,KAAAC,SAAS,GAAG,IAAI9B,YAAY,EAAS;IACrC,KAAA+B,UAAU,GAAG,IAAI/B,YAAY,EAAS;IAEhD,KAAAgC,UAAU,GAAW,EAAE;IACvB,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,QAAQ,GAAY,KAAK;IAEjB,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,UAAU,GAAW,CAAC;EAEmB;EAEjDC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAC,WAAWA,CAAA;IACT;EAAA;EAGMD,SAASA,CAAA;IACf,IAAI,CAAC,IAAI,CAACrB,GAAG,EAAE;MACb,IAAI,CAACuB,gBAAgB,EAAE;MACvB;;IAGF;IACA,IAAIC,YAAY,GAAG,IAAI,CAACxB,GAAG;IAC3B,IAAI,IAAI,CAACyB,KAAK,IAAI,IAAI,CAACC,MAAM,EAAE;MAC7BF,YAAY,GAAG,IAAI,CAACzB,YAAY,CAAC4B,gBAAgB,CAAC,IAAI,CAAC3B,GAAG,EAAE,IAAI,CAACyB,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACvB,OAAO,CAAC;;IAGpG;IACA,IAAI,CAACW,UAAU,GAAG,IAAI,CAACf,YAAY,CAAC6B,eAAe,CAACJ,YAAY,EAAE,IAAI,CAACtB,YAAY,CAAC;IAEpF;IACA,IAAI,IAAI,CAACS,OAAO,EAAE;MAChB,IAAI,CAACkB,YAAY,EAAE;;EAEvB;EAEQA,YAAYA,CAAA;IAClB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;IACvBD,GAAG,CAACE,MAAM,GAAG,MAAK;MAChB,IAAI,CAACjB,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,WAAW,GAAG,IAAI;IACzB,CAAC;IACDc,GAAG,CAACG,OAAO,GAAG,MAAK;MACjB,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC;IACDJ,GAAG,CAAC9B,GAAG,GAAG,IAAI,CAACc,UAAU;EAC3B;EAEAqB,WAAWA,CAAA;IACT,IAAI,CAACpB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACN,SAAS,CAACwB,IAAI,EAAE;EACvB;EAEAC,YAAYA,CAACC,KAAY;IACvB,IAAI,CAACJ,eAAe,EAAE;IACtB,IAAI,CAACrB,UAAU,CAACuB,IAAI,CAACE,KAAK,CAAC;EAC7B;EAEQJ,eAAeA,CAAA;IACrB,IAAI,IAAI,CAAChB,UAAU,GAAG,IAAI,CAACC,UAAU,EAAE;MACrC,IAAI,CAACD,UAAU,EAAE;MACjB;MACA,IAAI,CAACnB,YAAY,CAACwC,gBAAgB,CAAC;QAAEC,MAAM,EAAE;UAAExC,GAAG,EAAE,IAAI,CAACc;QAAU;MAAE,CAAS,EAAE,IAAI,CAACZ,YAAY,CAAC;MAElG;MACA,MAAMuC,WAAW,GAAG,IAAI,CAAC1C,YAAY,CAAC2C,mBAAmB,CAAC,IAAI,CAACxC,YAAY,CAAC;MAC5E,IAAI,IAAI,CAACY,UAAU,KAAK2B,WAAW,EAAE;QACnC,IAAI,CAAC3B,UAAU,GAAG2B,WAAW;QAC7B;;;IAIJ,IAAI,CAAClB,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,CAACR,SAAS,GAAG,KAAK;IACtB,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACH,UAAU,GAAG,IAAI,CAACf,YAAY,CAAC2C,mBAAmB,CAAC,IAAI,CAACxC,YAAY,CAAC;EAC5E;EAEA;EACAyC,MAAMA,CAAA;IACJ,IAAI,CAACzB,UAAU,GAAG,CAAC;IACnB,IAAI,CAACD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACK,SAAS,EAAE;EAClB;EAEAuB,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC7C,YAAY,CAAC8C,sBAAsB,CAAC,IAAI,CAAC7C,GAAG,CAAC;EAC3D;;;uBAvHWH,uBAAuB,EAAAZ,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAvBnD,uBAAuB;MAAAoD,SAAA;MAAAC,MAAA;QAAAlD,GAAA;QAAAC,GAAA;QAAAC,YAAA;QAAAuB,KAAA;QAAAC,MAAA;QAAAvB,OAAA;QAAAC,QAAA;QAAAC,UAAA;QAAAC,gBAAA;QAAAd,WAAA;QAAAG,YAAA;QAAAY,SAAA;QAAAC,cAAA;QAAAC,UAAA;QAAAC,WAAA;QAAAC,OAAA;MAAA;MAAAwC,OAAA;QAAAvC,SAAA;QAAAC,UAAA;MAAA;MAAAuC,UAAA;MAAAC,QAAA,GAAApE,EAAA,CAAAqE,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UAnHhC7E,EAAA,CAAAC,cAAA,aAAgE;UAE9DD,EAAA,CAAA+E,UAAA,IAAAC,sCAAA,iBAA6D;UAK7DhF,EAAA,CAAAC,cAAA,aAUE;UAJAD,EADA,CAAAiF,UAAA,kBAAAC,qDAAA;YAAA,OAAQJ,GAAA,CAAA5B,WAAA,EAAa;UAAA,EAAC,mBAAAiC,sDAAAC,MAAA;YAAA,OACbN,GAAA,CAAA1B,YAAA,CAAAgC,MAAA,CAAoB;UAAA,EAAC;UANhCpF,EAAA,CAAAG,YAAA,EAUE;UASFH,EANA,CAAA+E,UAAA,IAAAM,sCAAA,iBAAgE,IAAAC,sCAAA,iBAMjB;UAGjDtF,EAAA,CAAAG,YAAA,EAAM;;;UA7BiCH,EAAA,CAAAuF,UAAA,CAAAT,GAAA,CAAAvD,cAAA,CAAwB;UAE/BvB,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAAK,UAAA,SAAAyE,GAAA,CAAAhD,SAAA,IAAAgD,GAAA,CAAA1D,UAAA,CAA6B;UAQzDpB,EAAA,CAAAI,SAAA,EAAoB;UAApBJ,EAAA,CAAAuF,UAAA,CAAAT,GAAA,CAAAtD,UAAA,CAAoB;UACpBxB,EAAA,CAAAwF,WAAA,eAAAV,GAAA,CAAAxD,SAAA,CAA8B;UAI9BtB,EADA,CAAAyF,WAAA,WAAAX,GAAA,CAAA/C,WAAA,CAA4B,UAAA+C,GAAA,CAAA9C,QAAA,CACJ;UACxBhC,EARA,CAAAK,UAAA,QAAAyE,GAAA,CAAAjD,UAAA,EAAA7B,EAAA,CAAA0F,aAAA,CAAkB,QAAAZ,GAAA,CAAA9D,GAAA,CACP,YAAA8D,GAAA,CAAA3D,QAAA,oBAO4B;UAIbnB,EAAA,CAAAI,SAAA,EAAkC;UAAlCJ,EAAA,CAAAK,UAAA,SAAAyE,GAAA,CAAA9C,QAAA,IAAA8C,GAAA,CAAAzD,gBAAA,CAAkC;UAMlCrB,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAK,UAAA,SAAAyE,GAAA,CAAArD,WAAA,CAAiB;;;qBA5BvC3B,YAAY,EAAA6F,EAAA,CAAAC,IAAA,EAAE7F,WAAW,EAAA8F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}