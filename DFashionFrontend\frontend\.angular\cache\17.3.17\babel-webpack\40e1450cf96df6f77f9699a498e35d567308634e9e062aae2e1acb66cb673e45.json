{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AnalyticsService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/admin`;\n  }\n  // Dashboard Statistics (Admin API)\n  getDashboardStats() {\n    return this.http.get(`${this.apiUrl}/dashboard`);\n  }\n  // Sales Analytics\n  getSalesData(period = '30d') {\n    return this.http.get(`${this.apiUrl}/sales?period=${period}`);\n  }\n  getSalesStats(period = '30d') {\n    return this.http.get(`${this.apiUrl}/sales/stats?period=${period}`);\n  }\n  // User Analytics\n  getUserAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/users?period=${period}`);\n  }\n  getUserGrowth(period = '12m') {\n    return this.http.get(`${this.apiUrl}/users/growth?period=${period}`);\n  }\n  getUserActivity(period = '7d') {\n    return this.http.get(`${this.apiUrl}/users/activity?period=${period}`);\n  }\n  // Product Analytics\n  getProductAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/products?period=${period}`);\n  }\n  getTopSellingProducts(limit = 10, period = '30d') {\n    return this.http.get(`${this.apiUrl}/products/top-selling?limit=${limit}&period=${period}`);\n  }\n  getProductPerformance(productId, period = '30d') {\n    return this.http.get(`${this.apiUrl}/products/${productId}/performance?period=${period}`);\n  }\n  // Order Analytics\n  getOrderAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/orders?period=${period}`);\n  }\n  getOrderTrends(period = '12m') {\n    return this.http.get(`${this.apiUrl}/orders/trends?period=${period}`);\n  }\n  // Revenue Analytics\n  getRevenueAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/revenue?period=${period}`);\n  }\n  getRevenueByCategory(period = '30d') {\n    return this.http.get(`${this.apiUrl}/revenue/by-category?period=${period}`);\n  }\n  getRevenueByVendor(period = '30d') {\n    return this.http.get(`${this.apiUrl}/revenue/by-vendor?period=${period}`);\n  }\n  // Traffic Analytics\n  getTrafficAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/traffic?period=${period}`);\n  }\n  getPageViews(period = '7d') {\n    return this.http.get(`${this.apiUrl}/traffic/page-views?period=${period}`);\n  }\n  // Conversion Analytics\n  getConversionRates(period = '30d') {\n    return this.http.get(`${this.apiUrl}/conversion?period=${period}`);\n  }\n  getFunnelAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/conversion/funnel?period=${period}`);\n  }\n  // Customer Analytics\n  getCustomerAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/customers?period=${period}`);\n  }\n  getCustomerLifetimeValue(period = '12m') {\n    return this.http.get(`${this.apiUrl}/customers/lifetime-value?period=${period}`);\n  }\n  getCustomerRetention(period = '12m') {\n    return this.http.get(`${this.apiUrl}/customers/retention?period=${period}`);\n  }\n  // Inventory Analytics\n  getInventoryAnalytics() {\n    return this.http.get(`${this.apiUrl}/inventory`);\n  }\n  getLowStockProducts(threshold = 10) {\n    return this.http.get(`${this.apiUrl}/inventory/low-stock?threshold=${threshold}`);\n  }\n  getStockMovement(period = '30d') {\n    return this.http.get(`${this.apiUrl}/inventory/movement?period=${period}`);\n  }\n  // Marketing Analytics\n  getMarketingAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/marketing?period=${period}`);\n  }\n  getCampaignPerformance(period = '30d') {\n    return this.http.get(`${this.apiUrl}/marketing/campaigns?period=${period}`);\n  }\n  // Financial Analytics\n  getFinancialAnalytics(period = '30d') {\n    return this.http.get(`${this.apiUrl}/financial?period=${period}`);\n  }\n  getProfitAnalysis(period = '30d') {\n    return this.http.get(`${this.apiUrl}/financial/profit?period=${period}`);\n  }\n  // Export Analytics\n  exportAnalyticsReport(type, period = '30d', format = 'csv') {\n    let params = new HttpParams().set('type', type).set('period', period).set('format', format);\n    return this.http.get(`${this.apiUrl}/export`, {\n      params,\n      responseType: 'blob'\n    });\n  }\n  // Real-time Analytics\n  getRealTimeStats() {\n    return this.http.get(`${this.apiUrl}/real-time`);\n  }\n  // Custom Analytics\n  getCustomAnalytics(query) {\n    return this.http.post(`${this.apiUrl}/custom`, query);\n  }\n  // Comparative Analytics\n  getComparativeAnalytics(periods) {\n    let params = new HttpParams();\n    periods.forEach(period => {\n      params = params.append('periods', period);\n    });\n    return this.http.get(`${this.apiUrl}/comparative`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function AnalyticsService_Factory(t) {\n      return new (t || AnalyticsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AnalyticsService,\n      factory: AnalyticsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "environment", "AnalyticsService", "constructor", "http", "apiUrl", "getDashboardStats", "get", "getSalesData", "period", "getSalesStats", "getUserAnalytics", "getUser<PERSON>rowth", "getUserActivity", "getProductAnalytics", "getTopSellingProducts", "limit", "getProductPerformance", "productId", "getOrderAnalytics", "getOrderTrends", "getRevenueAnalytics", "getRevenueByCategory", "getRevenueByVendor", "getTrafficAnalytics", "getPageViews", "getConversionRates", "getFunnelAnalytics", "getCustomerAnalytics", "getCustomerLifetimeValue", "getCustomerRetention", "getInventoryAnalytics", "getLowStockProducts", "threshold", "getStockMovement", "getMarketingAnalytics", "getCampaignPerformance", "getFinancialAnalytics", "getProfitAnalysis", "exportAnalyticsReport", "type", "format", "params", "set", "responseType", "getRealTimeStats", "getCustomAnalytics", "query", "post", "getComparativeAnalytics", "periods", "for<PERSON>ach", "append", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\admin\\services\\analytics.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../../environments/environment';\n\nexport interface DashboardStats {\n  totalUsers: number;\n  totalProducts: number;\n  totalOrders: number;\n  totalRevenue: number;\n  newUsersToday: number;\n  ordersToday: number;\n  revenueToday: number;\n  conversionRate: number;\n}\n\nexport interface SalesData {\n  date: string;\n  sales: number;\n  orders: number;\n  revenue: number;\n}\n\nexport interface UserAnalytics {\n  totalUsers: number;\n  activeUsers: number;\n  newUsers: number;\n  userGrowth: number;\n  usersByRole: { [key: string]: number };\n  usersByDepartment: { [key: string]: number };\n}\n\nexport interface ProductAnalytics {\n  totalProducts: number;\n  activeProducts: number;\n  featuredProducts: number;\n  productsByCategory: { [key: string]: number };\n  topSellingProducts: any[];\n  lowStockProducts: any[];\n}\n\nexport interface OrderAnalytics {\n  totalOrders: number;\n  pendingOrders: number;\n  completedOrders: number;\n  cancelledOrders: number;\n  averageOrderValue: number;\n  ordersByStatus: { [key: string]: number };\n  ordersByPaymentMethod: { [key: string]: number };\n}\n\nexport interface RevenueAnalytics {\n  totalRevenue: number;\n  monthlyRevenue: number;\n  revenueGrowth: number;\n  revenueByCategory: { [key: string]: number };\n  revenueByMonth: SalesData[];\n}\n\nexport interface TrafficAnalytics {\n  totalViews: number;\n  uniqueVisitors: number;\n  bounceRate: number;\n  averageSessionDuration: number;\n  topPages: any[];\n  trafficSources: { [key: string]: number };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AnalyticsService {\n  private apiUrl = `${environment.apiUrl}/admin`;\n\n  constructor(private http: HttpClient) {}\n\n  // Dashboard Statistics (Admin API)\n  getDashboardStats(): Observable<{success: boolean; data: any}> {\n    return this.http.get<{success: boolean; data: any}>(`${this.apiUrl}/dashboard`);\n  }\n\n\n\n  // Sales Analytics\n  getSalesData(period: string = '30d'): Observable<SalesData[]> {\n    return this.http.get<SalesData[]>(`${this.apiUrl}/sales?period=${period}`);\n  }\n\n  getSalesStats(period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/sales/stats?period=${period}`);\n  }\n\n  // User Analytics\n  getUserAnalytics(period: string = '30d'): Observable<UserAnalytics> {\n    return this.http.get<UserAnalytics>(`${this.apiUrl}/users?period=${period}`);\n  }\n\n  getUserGrowth(period: string = '12m'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/users/growth?period=${period}`);\n  }\n\n  getUserActivity(period: string = '7d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/users/activity?period=${period}`);\n  }\n\n  // Product Analytics\n  getProductAnalytics(period: string = '30d'): Observable<ProductAnalytics> {\n    return this.http.get<ProductAnalytics>(`${this.apiUrl}/products?period=${period}`);\n  }\n\n  getTopSellingProducts(limit: number = 10, period: string = '30d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/products/top-selling?limit=${limit}&period=${period}`);\n  }\n\n  getProductPerformance(productId: string, period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/products/${productId}/performance?period=${period}`);\n  }\n\n  // Order Analytics\n  getOrderAnalytics(period: string = '30d'): Observable<{success: boolean; data: any}> {\n    return this.http.get<{success: boolean; data: any}>(`${this.apiUrl}/orders?period=${period}`);\n  }\n\n  getOrderTrends(period: string = '12m'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/orders/trends?period=${period}`);\n  }\n\n  // Revenue Analytics\n  getRevenueAnalytics(period: string = '30d'): Observable<RevenueAnalytics> {\n    return this.http.get<RevenueAnalytics>(`${this.apiUrl}/revenue?period=${period}`);\n  }\n\n  getRevenueByCategory(period: string = '30d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/revenue/by-category?period=${period}`);\n  }\n\n  getRevenueByVendor(period: string = '30d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/revenue/by-vendor?period=${period}`);\n  }\n\n  // Traffic Analytics\n  getTrafficAnalytics(period: string = '30d'): Observable<TrafficAnalytics> {\n    return this.http.get<TrafficAnalytics>(`${this.apiUrl}/traffic?period=${period}`);\n  }\n\n  getPageViews(period: string = '7d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/traffic/page-views?period=${period}`);\n  }\n\n  // Conversion Analytics\n  getConversionRates(period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/conversion?period=${period}`);\n  }\n\n  getFunnelAnalytics(period: string = '30d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/conversion/funnel?period=${period}`);\n  }\n\n  // Customer Analytics\n  getCustomerAnalytics(period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/customers?period=${period}`);\n  }\n\n  getCustomerLifetimeValue(period: string = '12m'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/customers/lifetime-value?period=${period}`);\n  }\n\n  getCustomerRetention(period: string = '12m'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/customers/retention?period=${period}`);\n  }\n\n  // Inventory Analytics\n  getInventoryAnalytics(): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/inventory`);\n  }\n\n  getLowStockProducts(threshold: number = 10): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/inventory/low-stock?threshold=${threshold}`);\n  }\n\n  getStockMovement(period: string = '30d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/inventory/movement?period=${period}`);\n  }\n\n  // Marketing Analytics\n  getMarketingAnalytics(period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/marketing?period=${period}`);\n  }\n\n  getCampaignPerformance(period: string = '30d'): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/marketing/campaigns?period=${period}`);\n  }\n\n  // Financial Analytics\n  getFinancialAnalytics(period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/financial?period=${period}`);\n  }\n\n  getProfitAnalysis(period: string = '30d'): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/financial/profit?period=${period}`);\n  }\n\n  // Export Analytics\n  exportAnalyticsReport(type: string, period: string = '30d', format: 'csv' | 'excel' | 'pdf' = 'csv'): Observable<Blob> {\n    let params = new HttpParams()\n      .set('type', type)\n      .set('period', period)\n      .set('format', format);\n\n    return this.http.get(`${this.apiUrl}/export`, {\n      params,\n      responseType: 'blob'\n    });\n  }\n\n  // Real-time Analytics\n  getRealTimeStats(): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/real-time`);\n  }\n\n  // Custom Analytics\n  getCustomAnalytics(query: any): Observable<any> {\n    return this.http.post<any>(`${this.apiUrl}/custom`, query);\n  }\n\n  // Comparative Analytics\n  getComparativeAnalytics(periods: string[]): Observable<any> {\n    let params = new HttpParams();\n    periods.forEach(period => {\n      params = params.append('periods', period);\n    });\n\n    return this.http.get<any>(`${this.apiUrl}/comparative`, { params });\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,QAAQ,mCAAmC;;;AAoE/D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACI,MAAM,QAAQ;EAEP;EAEvC;EACAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAgC,GAAG,IAAI,CAACF,MAAM,YAAY,CAAC;EACjF;EAIA;EACAG,YAAYA,CAACC,MAAA,GAAiB,KAAK;IACjC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAc,GAAG,IAAI,CAACF,MAAM,iBAAiBI,MAAM,EAAE,CAAC;EAC5E;EAEAC,aAAaA,CAACD,MAAA,GAAiB,KAAK;IAClC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,uBAAuBI,MAAM,EAAE,CAAC;EAC1E;EAEA;EACAE,gBAAgBA,CAACF,MAAA,GAAiB,KAAK;IACrC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAgB,GAAG,IAAI,CAACF,MAAM,iBAAiBI,MAAM,EAAE,CAAC;EAC9E;EAEAG,aAAaA,CAACH,MAAA,GAAiB,KAAK;IAClC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,wBAAwBI,MAAM,EAAE,CAAC;EAC7E;EAEAI,eAAeA,CAACJ,MAAA,GAAiB,IAAI;IACnC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,0BAA0BI,MAAM,EAAE,CAAC;EAC/E;EAEA;EACAK,mBAAmBA,CAACL,MAAA,GAAiB,KAAK;IACxC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAmB,GAAG,IAAI,CAACF,MAAM,oBAAoBI,MAAM,EAAE,CAAC;EACpF;EAEAM,qBAAqBA,CAACC,KAAA,GAAgB,EAAE,EAAEP,MAAA,GAAiB,KAAK;IAC9D,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,+BAA+BW,KAAK,WAAWP,MAAM,EAAE,CAAC;EACpG;EAEAQ,qBAAqBA,CAACC,SAAiB,EAAET,MAAA,GAAiB,KAAK;IAC7D,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,aAAaa,SAAS,uBAAuBT,MAAM,EAAE,CAAC;EAChG;EAEA;EACAU,iBAAiBA,CAACV,MAAA,GAAiB,KAAK;IACtC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAgC,GAAG,IAAI,CAACF,MAAM,kBAAkBI,MAAM,EAAE,CAAC;EAC/F;EAEAW,cAAcA,CAACX,MAAA,GAAiB,KAAK;IACnC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,yBAAyBI,MAAM,EAAE,CAAC;EAC9E;EAEA;EACAY,mBAAmBA,CAACZ,MAAA,GAAiB,KAAK;IACxC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAmB,GAAG,IAAI,CAACF,MAAM,mBAAmBI,MAAM,EAAE,CAAC;EACnF;EAEAa,oBAAoBA,CAACb,MAAA,GAAiB,KAAK;IACzC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,+BAA+BI,MAAM,EAAE,CAAC;EACpF;EAEAc,kBAAkBA,CAACd,MAAA,GAAiB,KAAK;IACvC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,6BAA6BI,MAAM,EAAE,CAAC;EAClF;EAEA;EACAe,mBAAmBA,CAACf,MAAA,GAAiB,KAAK;IACxC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAmB,GAAG,IAAI,CAACF,MAAM,mBAAmBI,MAAM,EAAE,CAAC;EACnF;EAEAgB,YAAYA,CAAChB,MAAA,GAAiB,IAAI;IAChC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,8BAA8BI,MAAM,EAAE,CAAC;EACnF;EAEA;EACAiB,kBAAkBA,CAACjB,MAAA,GAAiB,KAAK;IACvC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,sBAAsBI,MAAM,EAAE,CAAC;EACzE;EAEAkB,kBAAkBA,CAAClB,MAAA,GAAiB,KAAK;IACvC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,6BAA6BI,MAAM,EAAE,CAAC;EAClF;EAEA;EACAmB,oBAAoBA,CAACnB,MAAA,GAAiB,KAAK;IACzC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,qBAAqBI,MAAM,EAAE,CAAC;EACxE;EAEAoB,wBAAwBA,CAACpB,MAAA,GAAiB,KAAK;IAC7C,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,oCAAoCI,MAAM,EAAE,CAAC;EACvF;EAEAqB,oBAAoBA,CAACrB,MAAA,GAAiB,KAAK;IACzC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,+BAA+BI,MAAM,EAAE,CAAC;EACpF;EAEA;EACAsB,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC3B,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,YAAY,CAAC;EACvD;EAEA2B,mBAAmBA,CAACC,SAAA,GAAoB,EAAE;IACxC,OAAO,IAAI,CAAC7B,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,kCAAkC4B,SAAS,EAAE,CAAC;EAC1F;EAEAC,gBAAgBA,CAACzB,MAAA,GAAiB,KAAK;IACrC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,8BAA8BI,MAAM,EAAE,CAAC;EACnF;EAEA;EACA0B,qBAAqBA,CAAC1B,MAAA,GAAiB,KAAK;IAC1C,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,qBAAqBI,MAAM,EAAE,CAAC;EACxE;EAEA2B,sBAAsBA,CAAC3B,MAAA,GAAiB,KAAK;IAC3C,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAQ,GAAG,IAAI,CAACF,MAAM,+BAA+BI,MAAM,EAAE,CAAC;EACpF;EAEA;EACA4B,qBAAqBA,CAAC5B,MAAA,GAAiB,KAAK;IAC1C,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,qBAAqBI,MAAM,EAAE,CAAC;EACxE;EAEA6B,iBAAiBA,CAAC7B,MAAA,GAAiB,KAAK;IACtC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,4BAA4BI,MAAM,EAAE,CAAC;EAC/E;EAEA;EACA8B,qBAAqBA,CAACC,IAAY,EAAE/B,MAAA,GAAiB,KAAK,EAAEgC,MAAA,GAAkC,KAAK;IACjG,IAAIC,MAAM,GAAG,IAAI1C,UAAU,EAAE,CAC1B2C,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC,CACjBG,GAAG,CAAC,QAAQ,EAAElC,MAAM,CAAC,CACrBkC,GAAG,CAAC,QAAQ,EAAEF,MAAM,CAAC;IAExB,OAAO,IAAI,CAACrC,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACF,MAAM,SAAS,EAAE;MAC5CqC,MAAM;MACNE,YAAY,EAAE;KACf,CAAC;EACJ;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACzC,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,YAAY,CAAC;EACvD;EAEA;EACAyC,kBAAkBA,CAACC,KAAU;IAC3B,OAAO,IAAI,CAAC3C,IAAI,CAAC4C,IAAI,CAAM,GAAG,IAAI,CAAC3C,MAAM,SAAS,EAAE0C,KAAK,CAAC;EAC5D;EAEA;EACAE,uBAAuBA,CAACC,OAAiB;IACvC,IAAIR,MAAM,GAAG,IAAI1C,UAAU,EAAE;IAC7BkD,OAAO,CAACC,OAAO,CAAC1C,MAAM,IAAG;MACvBiC,MAAM,GAAGA,MAAM,CAACU,MAAM,CAAC,SAAS,EAAE3C,MAAM,CAAC;IAC3C,CAAC,CAAC;IAEF,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,cAAc,EAAE;MAAEqC;IAAM,CAAE,CAAC;EACrE;;;uBAlKWxC,gBAAgB,EAAAmD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBtD,gBAAgB;MAAAuD,OAAA,EAAhBvD,gBAAgB,CAAAwD,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}