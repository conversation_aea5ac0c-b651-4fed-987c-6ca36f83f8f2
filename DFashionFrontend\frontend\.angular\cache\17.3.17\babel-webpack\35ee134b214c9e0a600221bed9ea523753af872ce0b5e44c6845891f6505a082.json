{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject, of, timer } from 'rxjs';\nimport { map, catchError, switchMap, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class RecommendationService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.apiUrl = 'http://********:5000/api'; // Direct IP for testing\n    this.userAnalytics$ = new BehaviorSubject(null);\n    this.realTimeRecommendations$ = new BehaviorSubject([]);\n    this.userBehavior$ = new BehaviorSubject(null);\n    // Start real-time recommendation updates\n    this.startRealTimeUpdates();\n  }\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId, limit = 10) {\n    const params = new HttpParams().set('userId', userId || '').set('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/recommendations/suggested`, {\n      params\n    }).pipe(map(response => response.success ? response.data : []), tap(recommendations => {\n      // Update real-time recommendations\n      this.realTimeRecommendations$.next(recommendations);\n    }), catchError(error => {\n      console.error('Error fetching suggested products:', error);\n      return this.getFallbackSuggestedProducts(limit);\n    }));\n  }\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category, limit = 10) {\n    console.log('📈 Loading trending products from API');\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n    return this.http.get(`${this.apiUrl}/recommendations/trending?${params.toString()}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching trending products:', error);\n      return this.getFallbackTrendingProducts(limit);\n    }));\n  }\n  // Similar Products - Based on Product\n  getSimilarProducts(productId, limit = 6) {\n    return this.http.get(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching similar products:', error);\n      return this.getFallbackSimilarProducts(limit);\n    }));\n  }\n  // Recently Viewed Products\n  getRecentlyViewed(userId, limit = 8) {\n    return this.http.get(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n      console.error('Error fetching recently viewed:', error);\n      return this.getFallbackRecentProducts(limit);\n    }));\n  }\n  // Track User Behavior for Analytics\n  trackProductView(productId, category, duration = 0, source = 'unknown') {\n    const headers = this.authService.getAuthHeaders();\n    return this.http.post(`${this.apiUrl}/recommendations/track-view`, {\n      productId,\n      category,\n      duration,\n      source,\n      timestamp: new Date()\n    }, {\n      headers\n    }).pipe(tap(response => {\n      console.log('Product view tracked:', response);\n    }), catchError(error => {\n      console.error('Error tracking product view:', error);\n      return of(null);\n    }));\n  }\n  // Track any user interaction\n  trackInteraction(type, targetId, targetType, metadata = {}) {\n    const headers = this.authService.getAuthHeaders();\n    return this.http.post(`${this.apiUrl}/recommendations/track-interaction`, {\n      type,\n      targetId,\n      targetType,\n      metadata: {\n        ...metadata,\n        timestamp: new Date(),\n        userAgent: navigator.userAgent,\n        url: window.location.href\n      }\n    }, {\n      headers\n    }).pipe(tap(response => {\n      console.log('Interaction tracked:', response);\n      // Update user behavior data\n      if (response?.success) {\n        this.updateUserBehavior(response);\n      }\n    }), catchError(error => {\n      console.error('Error tracking interaction:', error);\n      return of(null);\n    }));\n  }\n  trackPurchase(productId, category, price) {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(catchError(error => {\n      console.error('Error tracking purchase:', error);\n      return [];\n    }));\n  }\n  // User Analytics\n  getUserAnalytics(userId) {\n    if (!userId) {\n      return this.userAnalytics$.asObservable();\n    }\n    const headers = this.authService.getAuthHeaders();\n    return this.http.get(`${this.apiUrl}/recommendations/user-analytics/${userId}`, {\n      headers\n    }).pipe(map(response => response.success ? response.data : null), tap(analytics => {\n      if (analytics) {\n        this.userAnalytics$.next(analytics);\n      }\n    }), catchError(error => {\n      console.error('Error fetching user analytics:', error);\n      return of(this.getDefaultAnalytics(userId));\n    }));\n  }\n  // Get recommendation insights\n  getRecommendationInsights(userId) {\n    const headers = this.authService.getAuthHeaders();\n    return this.http.get(`${this.apiUrl}/recommendations/insights/${userId}`, {\n      headers\n    }).pipe(map(response => response.success ? response.data : null), catchError(error => {\n      console.error('Error fetching recommendation insights:', error);\n      return of(null);\n    }));\n  }\n  // Real-time recommendations observable\n  getRealTimeRecommendations() {\n    return this.realTimeRecommendations$.asObservable();\n  }\n  // User behavior observable\n  getUserBehavior() {\n    return this.userBehavior$.asObservable();\n  }\n  // Start real-time updates\n  startRealTimeUpdates() {\n    // Update recommendations every 5 minutes if user is active\n    timer(0, 5 * 60 * 1000).pipe(switchMap(() => {\n      return this.authService.getCurrentUser().pipe(switchMap(response => {\n        if (response?.user?._id) {\n          return this.getSuggestedProducts(response.user._id, 10);\n        }\n        return of([]);\n      }), catchError(() => of([])));\n    })).subscribe();\n    // Update user analytics every 10 minutes\n    timer(0, 10 * 60 * 1000).pipe(switchMap(() => {\n      return this.authService.getCurrentUser().pipe(switchMap(response => {\n        if (response?.user?._id) {\n          return this.getUserAnalytics(response.user._id);\n        }\n        return of(null);\n      }), catchError(() => of(null)));\n    })).subscribe();\n  }\n  // Update user behavior data\n  updateUserBehavior(response) {\n    if (response.userSegment || response.engagementLevel) {\n      const currentBehavior = this.userBehavior$.value || {};\n      this.userBehavior$.next({\n        ...currentBehavior,\n        userSegment: response.userSegment,\n        engagementLevel: response.engagementLevel,\n        lastUpdate: new Date()\n      });\n    }\n  }\n  // Convenience methods for common tracking actions\n  trackProductLike(productId, metadata = {}) {\n    return this.trackInteraction('product_like', productId, 'product', metadata);\n  }\n  trackProductShare(productId, platform, metadata = {}) {\n    return this.trackInteraction('product_share', productId, 'product', {\n      ...metadata,\n      platform\n    });\n  }\n  trackProductPurchase(productId, metadata = {}) {\n    return this.trackInteraction('product_purchase', productId, 'product', metadata);\n  }\n  trackCartAdd(productId, metadata = {}) {\n    return this.trackInteraction('cart_add', productId, 'product', metadata);\n  }\n  trackWishlistAdd(productId, metadata = {}) {\n    return this.trackInteraction('wishlist_add', productId, 'product', metadata);\n  }\n  trackSearch(query, filters = {}, results = 0) {\n    return this.trackInteraction('search', 'search', 'search', {\n      searchQuery: query,\n      filters,\n      resultsCount: results\n    });\n  }\n  trackCategoryBrowse(category, metadata = {}) {\n    return this.trackInteraction('category_browse', category, 'category', metadata);\n  }\n  trackVendorFollow(vendorId, metadata = {}) {\n    return this.trackInteraction('vendor_follow', vendorId, 'vendor', metadata);\n  }\n  trackPostView(postId, duration = 0, metadata = {}) {\n    return this.trackInteraction('post_view', postId, 'post', {\n      ...metadata,\n      duration\n    });\n  }\n  trackStoryView(storyId, duration = 0, metadata = {}) {\n    return this.trackInteraction('story_view', storyId, 'story', {\n      ...metadata,\n      duration\n    });\n  }\n  // Category-based Recommendations\n  getCategoryRecommendations(category, limit = 8) {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log(`🏷️ Loading ${category} recommendations (offline mode)`);\n    return this.getFallbackCategoryProducts(category, limit);\n    /* API version - uncomment when backend is available\n    return this.http.get<any>(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching category recommendations:', error);\n          return this.getFallbackCategoryProducts(category, limit);\n        })\n      );\n    */\n  }\n  // Fallback methods for offline/error scenarios - removed mock data\n  getFallbackSuggestedProducts(limit) {\n    // Return empty array - use real API data only\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n  getFallbackTrendingProducts(limit) {\n    // Return empty array - use real API data only\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n  getFallbackSimilarProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackRecentProducts(limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getFallbackCategoryProducts(category, limit) {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n  getDefaultAnalytics(userId) {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: {\n        min: 500,\n        max: 5000\n      },\n      brandPreferences: []\n    };\n  }\n  // Update user analytics locally\n  updateUserAnalytics(analytics) {\n    this.userAnalytics$.next(analytics);\n  }\n  // Get current user analytics\n  getCurrentUserAnalytics() {\n    return this.userAnalytics$.asObservable();\n  }\n  static {\n    this.ɵfac = function RecommendationService_Factory(t) {\n      return new (t || RecommendationService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RecommendationService,\n      factory: RecommendationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "Observable", "BehaviorSubject", "of", "timer", "map", "catchError", "switchMap", "tap", "RecommendationService", "constructor", "http", "authService", "apiUrl", "userAnalytics$", "realTimeRecommendations$", "userBehavior$", "startRealTimeUpdates", "getSuggestedProducts", "userId", "limit", "params", "set", "toString", "get", "pipe", "response", "success", "data", "recommendations", "next", "error", "console", "getFallbackSuggestedProducts", "getTrendingProducts", "category", "log", "URLSearchParams", "append", "getFallbackTrendingProducts", "getSimilarProducts", "productId", "getFallbackSimilarProducts", "getRecently<PERSON>iewed", "getFallbackRecentProducts", "trackProductView", "duration", "source", "headers", "getAuthHeaders", "post", "timestamp", "Date", "trackInteraction", "type", "targetId", "targetType", "metadata", "userAgent", "navigator", "url", "window", "location", "href", "updateUserBehavior", "trackPurchase", "price", "getUserAnalytics", "asObservable", "analytics", "getDefaultAnalytics", "getRecommendationInsights", "getRealTimeRecommendations", "getUserBehavior", "getCurrentUser", "user", "_id", "subscribe", "userSegment", "engagementLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "lastUpdate", "trackProductLike", "trackProductShare", "platform", "trackProductPurchase", "trackCartAdd", "trackWishlistAdd", "trackSearch", "query", "filters", "results", "searchQuery", "resultsCount", "trackCategoryBrowse", "trackVendorFollow", "vendorId", "trackPostView", "postId", "trackStoryView", "storyId", "getCategoryRecommendations", "getFallbackCategoryProducts", "observer", "complete", "viewHistory", "searchHistory", "purchaseHistory", "wishlistItems", "cartItems", "preferredCategories", "priceRange", "min", "max", "brandPreferences", "updateUserAnalytics", "getCurrentUserAnalytics", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\recommendation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject, of, timer } from 'rxjs';\nimport { map, catchError, switchMap, tap } from 'rxjs/operators';\nimport { AuthService } from './auth.service';\nimport { environment } from '../../../environments/environment';\n\nexport interface RecommendationProduct {\n  _id: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice: number;\n  discount: number;\n  images: Array<{ url: string; alt: string; isPrimary: boolean }>;\n  category: string;\n  subcategory: string;\n  brand: string;\n  rating: { average: number; count: number };\n  tags: string[];\n  isActive: boolean;\n  isFeatured: boolean;\n  recommendationScore?: number;\n  recommendationReason?: string;\n}\n\nexport interface TrendingProduct extends RecommendationProduct {\n  trendingScore: number;\n  trendingReason: string;\n  viewCount: number;\n  purchaseCount: number;\n  shareCount: number;\n  engagementRate: number;\n}\n\nexport interface UserAnalytics {\n  userId: string;\n  viewHistory: Array<{\n    productId: string;\n    category: string;\n    timestamp: Date;\n    duration: number;\n  }>;\n  searchHistory: Array<{\n    query: string;\n    category?: string;\n    timestamp: Date;\n    resultsClicked: number;\n  }>;\n  purchaseHistory: Array<{\n    productId: string;\n    category: string;\n    price: number;\n    timestamp: Date;\n  }>;\n  wishlistItems: string[];\n  cartItems: string[];\n  preferredCategories: string[];\n  priceRange: { min: number; max: number };\n  brandPreferences: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RecommendationService {\n  private apiUrl = 'http://********:5000/api'; // Direct IP for testing\n  private userAnalytics$ = new BehaviorSubject<UserAnalytics | null>(null);\n  private realTimeRecommendations$ = new BehaviorSubject<RecommendationProduct[]>([]);\n  private userBehavior$ = new BehaviorSubject<any>(null);\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {\n    // Start real-time recommendation updates\n    this.startRealTimeUpdates();\n  }\n\n  // Suggested for You - Personalized Recommendations\n  getSuggestedProducts(userId?: string, limit: number = 10): Observable<RecommendationProduct[]> {\n    const params = new HttpParams()\n      .set('userId', userId || '')\n      .set('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/suggested`, { params })\n      .pipe(\n        map(response => response.success ? response.data : []),\n        tap(recommendations => {\n          // Update real-time recommendations\n          this.realTimeRecommendations$.next(recommendations);\n        }),\n        catchError(error => {\n          console.error('Error fetching suggested products:', error);\n          return this.getFallbackSuggestedProducts(limit);\n        })\n      );\n  }\n\n  // Trending Products - Based on Analytics\n  getTrendingProducts(category?: string, limit: number = 10): Observable<TrendingProduct[]> {\n    console.log('📈 Loading trending products from API');\n    const params = new URLSearchParams();\n    if (category) params.append('category', category);\n    params.append('limit', limit.toString());\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/trending?${params.toString()}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching trending products:', error);\n          return this.getFallbackTrendingProducts(limit);\n        })\n      );\n  }\n\n  // Similar Products - Based on Product\n  getSimilarProducts(productId: string, limit: number = 6): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching similar products:', error);\n          return this.getFallbackSimilarProducts(limit);\n        })\n      );\n  }\n\n  // Recently Viewed Products\n  getRecentlyViewed(userId: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    return this.http.get<any>(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching recently viewed:', error);\n          return this.getFallbackRecentProducts(limit);\n        })\n      );\n  }\n\n  // Track User Behavior for Analytics\n  trackProductView(productId: string, category: string, duration: number = 0, source: string = 'unknown'): Observable<any> {\n    const headers = this.authService.getAuthHeaders();\n\n    return this.http.post(`${this.apiUrl}/recommendations/track-view`, {\n      productId,\n      category,\n      duration,\n      source,\n      timestamp: new Date()\n    }, { headers }).pipe(\n      tap(response => {\n        console.log('Product view tracked:', response);\n      }),\n      catchError(error => {\n        console.error('Error tracking product view:', error);\n        return of(null);\n      })\n    );\n  }\n\n  // Track any user interaction\n  trackInteraction(type: string, targetId: string, targetType: string, metadata: any = {}): Observable<any> {\n    const headers = this.authService.getAuthHeaders();\n\n    return this.http.post(`${this.apiUrl}/recommendations/track-interaction`, {\n      type,\n      targetId,\n      targetType,\n      metadata: {\n        ...metadata,\n        timestamp: new Date(),\n        userAgent: navigator.userAgent,\n        url: window.location.href\n      }\n    }, { headers }).pipe(\n      tap(response => {\n        console.log('Interaction tracked:', response);\n        // Update user behavior data\n        if ((response as any)?.success) {\n          this.updateUserBehavior(response);\n        }\n      }),\n      catchError(error => {\n        console.error('Error tracking interaction:', error);\n        return of(null);\n      })\n    );\n  }\n\n\n\n  trackPurchase(productId: string, category: string, price: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n      productId,\n      category,\n      price,\n      timestamp: new Date()\n    }).pipe(\n      catchError(error => {\n        console.error('Error tracking purchase:', error);\n        return [];\n      })\n    );\n  }\n\n  // User Analytics\n  getUserAnalytics(userId?: string): Observable<any> {\n    if (!userId) {\n      return this.userAnalytics$.asObservable();\n    }\n\n    const headers = this.authService.getAuthHeaders();\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/user-analytics/${userId}`, { headers })\n      .pipe(\n        map(response => response.success ? response.data : null),\n        tap(analytics => {\n          if (analytics) {\n            this.userAnalytics$.next(analytics);\n          }\n        }),\n        catchError(error => {\n          console.error('Error fetching user analytics:', error);\n          return of(this.getDefaultAnalytics(userId));\n        })\n      );\n  }\n\n  // Get recommendation insights\n  getRecommendationInsights(userId: string): Observable<any> {\n    const headers = this.authService.getAuthHeaders();\n\n    return this.http.get<any>(`${this.apiUrl}/recommendations/insights/${userId}`, { headers })\n      .pipe(\n        map(response => response.success ? response.data : null),\n        catchError(error => {\n          console.error('Error fetching recommendation insights:', error);\n          return of(null);\n        })\n      );\n  }\n\n  // Real-time recommendations observable\n  getRealTimeRecommendations(): Observable<RecommendationProduct[]> {\n    return this.realTimeRecommendations$.asObservable();\n  }\n\n  // User behavior observable\n  getUserBehavior(): Observable<any> {\n    return this.userBehavior$.asObservable();\n  }\n\n  // Start real-time updates\n  private startRealTimeUpdates(): void {\n    // Update recommendations every 5 minutes if user is active\n    timer(0, 5 * 60 * 1000).pipe(\n      switchMap(() => {\n        return this.authService.getCurrentUser().pipe(\n          switchMap(response => {\n            if (response?.user?._id) {\n              return this.getSuggestedProducts(response.user._id, 10);\n            }\n            return of([]);\n          }),\n          catchError(() => of([]))\n        );\n      })\n    ).subscribe();\n\n    // Update user analytics every 10 minutes\n    timer(0, 10 * 60 * 1000).pipe(\n      switchMap(() => {\n        return this.authService.getCurrentUser().pipe(\n          switchMap(response => {\n            if (response?.user?._id) {\n              return this.getUserAnalytics(response.user._id);\n            }\n            return of(null);\n          }),\n          catchError(() => of(null))\n        );\n      })\n    ).subscribe();\n  }\n\n  // Update user behavior data\n  private updateUserBehavior(response: any): void {\n    if (response.userSegment || response.engagementLevel) {\n      const currentBehavior = this.userBehavior$.value || {};\n      this.userBehavior$.next({\n        ...currentBehavior,\n        userSegment: response.userSegment,\n        engagementLevel: response.engagementLevel,\n        lastUpdate: new Date()\n      });\n    }\n  }\n\n  // Convenience methods for common tracking actions\n  trackProductLike(productId: string, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('product_like', productId, 'product', metadata);\n  }\n\n  trackProductShare(productId: string, platform: string, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('product_share', productId, 'product', {\n      ...metadata,\n      platform\n    });\n  }\n\n  trackProductPurchase(productId: string, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('product_purchase', productId, 'product', metadata);\n  }\n\n  trackCartAdd(productId: string, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('cart_add', productId, 'product', metadata);\n  }\n\n  trackWishlistAdd(productId: string, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('wishlist_add', productId, 'product', metadata);\n  }\n\n  trackSearch(query: string, filters: any = {}, results: number = 0): Observable<any> {\n    return this.trackInteraction('search', 'search', 'search', {\n      searchQuery: query,\n      filters,\n      resultsCount: results\n    });\n  }\n\n  trackCategoryBrowse(category: string, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('category_browse', category, 'category', metadata);\n  }\n\n  trackVendorFollow(vendorId: string, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('vendor_follow', vendorId, 'vendor', metadata);\n  }\n\n  trackPostView(postId: string, duration: number = 0, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('post_view', postId, 'post', {\n      ...metadata,\n      duration\n    });\n  }\n\n  trackStoryView(storyId: string, duration: number = 0, metadata: any = {}): Observable<any> {\n    return this.trackInteraction('story_view', storyId, 'story', {\n      ...metadata,\n      duration\n    });\n  }\n\n  // Category-based Recommendations\n  getCategoryRecommendations(category: string, limit: number = 8): Observable<RecommendationProduct[]> {\n    // For demo purposes, return fallback data immediately to avoid API calls\n    console.log(`🏷️ Loading ${category} recommendations (offline mode)`);\n    return this.getFallbackCategoryProducts(category, limit);\n\n    /* API version - uncomment when backend is available\n    return this.http.get<any>(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`)\n      .pipe(\n        map(response => response.success ? response.data : []),\n        catchError(error => {\n          console.error('Error fetching category recommendations:', error);\n          return this.getFallbackCategoryProducts(category, limit);\n        })\n      );\n    */\n  }\n\n  // Fallback methods for offline/error scenarios - removed mock data\n  private getFallbackSuggestedProducts(limit: number): Observable<RecommendationProduct[]> {\n    // Return empty array - use real API data only\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n\n  private getFallbackTrendingProducts(limit: number): Observable<TrendingProduct[]> {\n    // Return empty array - use real API data only\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n\n  private getFallbackSimilarProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackRecentProducts(limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getFallbackCategoryProducts(category: string, limit: number): Observable<RecommendationProduct[]> {\n    return this.getFallbackSuggestedProducts(limit);\n  }\n\n  private getDefaultAnalytics(userId: string): UserAnalytics {\n    return {\n      userId,\n      viewHistory: [],\n      searchHistory: [],\n      purchaseHistory: [],\n      wishlistItems: [],\n      cartItems: [],\n      preferredCategories: ['women', 'men', 'accessories'],\n      priceRange: { min: 500, max: 5000 },\n      brandPreferences: []\n    };\n  }\n\n  // Update user analytics locally\n  updateUserAnalytics(analytics: UserAnalytics): void {\n    this.userAnalytics$.next(analytics);\n  }\n\n  // Get current user analytics\n  getCurrentUserAnalytics(): Observable<UserAnalytics | null> {\n    return this.userAnalytics$.asObservable();\n  }\n}\n"], "mappings": "AACA,SAAqBA,UAAU,QAAQ,sBAAsB;AAC7D,SAASC,UAAU,EAAEC,eAAe,EAAEC,EAAE,EAAEC,KAAK,QAAQ,MAAM;AAC7D,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;;;;AA8DhE,OAAM,MAAOC,qBAAqB;EAMhCC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAPb,KAAAC,MAAM,GAAG,0BAA0B,CAAC,CAAC;IACrC,KAAAC,cAAc,GAAG,IAAIZ,eAAe,CAAuB,IAAI,CAAC;IAChE,KAAAa,wBAAwB,GAAG,IAAIb,eAAe,CAA0B,EAAE,CAAC;IAC3E,KAAAc,aAAa,GAAG,IAAId,eAAe,CAAM,IAAI,CAAC;IAMpD;IACA,IAAI,CAACe,oBAAoB,EAAE;EAC7B;EAEA;EACAC,oBAAoBA,CAACC,MAAe,EAAEC,KAAA,GAAgB,EAAE;IACtD,MAAMC,MAAM,GAAG,IAAIrB,UAAU,EAAE,CAC5BsB,GAAG,CAAC,QAAQ,EAAEH,MAAM,IAAI,EAAE,CAAC,CAC3BG,GAAG,CAAC,OAAO,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC;IAEjC,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACX,MAAM,4BAA4B,EAAE;MAAEQ;IAAM,CAAE,CAAC,CAC9EI,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDpB,GAAG,CAACqB,eAAe,IAAG;MACpB;MACA,IAAI,CAACd,wBAAwB,CAACe,IAAI,CAACD,eAAe,CAAC;IACrD,CAAC,CAAC,EACFvB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,IAAI,CAACE,4BAA4B,CAACb,KAAK,CAAC;IACjD,CAAC,CAAC,CACH;EACL;EAEA;EACAc,mBAAmBA,CAACC,QAAiB,EAAEf,KAAA,GAAgB,EAAE;IACvDY,OAAO,CAACI,GAAG,CAAC,uCAAuC,CAAC;IACpD,MAAMf,MAAM,GAAG,IAAIgB,eAAe,EAAE;IACpC,IAAIF,QAAQ,EAAEd,MAAM,CAACiB,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;IACjDd,MAAM,CAACiB,MAAM,CAAC,OAAO,EAAElB,KAAK,CAACG,QAAQ,EAAE,CAAC;IAExC,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACX,MAAM,6BAA6BQ,MAAM,CAACE,QAAQ,EAAE,EAAE,CAAC,CACtFE,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDtB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI,CAACQ,2BAA2B,CAACnB,KAAK,CAAC;IAChD,CAAC,CAAC,CACH;EACL;EAEA;EACAoB,kBAAkBA,CAACC,SAAiB,EAAErB,KAAA,GAAgB,CAAC;IACrD,OAAO,IAAI,CAACT,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACX,MAAM,4BAA4B4B,SAAS,UAAUrB,KAAK,EAAE,CAAC,CAC5FK,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDtB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI,CAACW,0BAA0B,CAACtB,KAAK,CAAC;IAC/C,CAAC,CAAC,CACH;EACL;EAEA;EACAuB,iBAAiBA,CAACxB,MAAc,EAAEC,KAAA,GAAgB,CAAC;IACjD,OAAO,IAAI,CAACT,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACX,MAAM,2BAA2BM,MAAM,UAAUC,KAAK,EAAE,CAAC,CACxFK,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,EAAE,CAAC,EACtDtB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,IAAI,CAACa,yBAAyB,CAACxB,KAAK,CAAC;IAC9C,CAAC,CAAC,CACH;EACL;EAEA;EACAyB,gBAAgBA,CAACJ,SAAiB,EAAEN,QAAgB,EAAEW,QAAA,GAAmB,CAAC,EAAEC,MAAA,GAAiB,SAAS;IACpG,MAAMC,OAAO,GAAG,IAAI,CAACpC,WAAW,CAACqC,cAAc,EAAE;IAEjD,OAAO,IAAI,CAACtC,IAAI,CAACuC,IAAI,CAAC,GAAG,IAAI,CAACrC,MAAM,6BAA6B,EAAE;MACjE4B,SAAS;MACTN,QAAQ;MACRW,QAAQ;MACRC,MAAM;MACNI,SAAS,EAAE,IAAIC,IAAI;KACpB,EAAE;MAAEJ;IAAO,CAAE,CAAC,CAACvB,IAAI,CAClBjB,GAAG,CAACkB,QAAQ,IAAG;MACbM,OAAO,CAACI,GAAG,CAAC,uBAAuB,EAAEV,QAAQ,CAAC;IAChD,CAAC,CAAC,EACFpB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO5B,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACH;EAEA;EACAkD,gBAAgBA,CAACC,IAAY,EAAEC,QAAgB,EAAEC,UAAkB,EAAEC,QAAA,GAAgB,EAAE;IACrF,MAAMT,OAAO,GAAG,IAAI,CAACpC,WAAW,CAACqC,cAAc,EAAE;IAEjD,OAAO,IAAI,CAACtC,IAAI,CAACuC,IAAI,CAAC,GAAG,IAAI,CAACrC,MAAM,oCAAoC,EAAE;MACxEyC,IAAI;MACJC,QAAQ;MACRC,UAAU;MACVC,QAAQ,EAAE;QACR,GAAGA,QAAQ;QACXN,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBM,SAAS,EAAEC,SAAS,CAACD,SAAS;QAC9BE,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;;KAExB,EAAE;MAAEf;IAAO,CAAE,CAAC,CAACvB,IAAI,CAClBjB,GAAG,CAACkB,QAAQ,IAAG;MACbM,OAAO,CAACI,GAAG,CAAC,sBAAsB,EAAEV,QAAQ,CAAC;MAC7C;MACA,IAAKA,QAAgB,EAAEC,OAAO,EAAE;QAC9B,IAAI,CAACqC,kBAAkB,CAACtC,QAAQ,CAAC;;IAErC,CAAC,CAAC,EACFpB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO5B,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACH;EAIA8D,aAAaA,CAACxB,SAAiB,EAAEN,QAAgB,EAAE+B,KAAa;IAC9D,OAAO,IAAI,CAACvD,IAAI,CAACuC,IAAI,CAAC,GAAG,IAAI,CAACrC,MAAM,2BAA2B,EAAE;MAC/D4B,SAAS;MACTN,QAAQ;MACR+B,KAAK;MACLf,SAAS,EAAE,IAAIC,IAAI;KACpB,CAAC,CAAC3B,IAAI,CACLnB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEA;EACAoC,gBAAgBA,CAAChD,MAAe;IAC9B,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,IAAI,CAACL,cAAc,CAACsD,YAAY,EAAE;;IAG3C,MAAMpB,OAAO,GAAG,IAAI,CAACpC,WAAW,CAACqC,cAAc,EAAE;IAEjD,OAAO,IAAI,CAACtC,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACX,MAAM,mCAAmCM,MAAM,EAAE,EAAE;MAAE6B;IAAO,CAAE,CAAC,CAC9FvB,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAAC,EACxDpB,GAAG,CAAC6D,SAAS,IAAG;MACd,IAAIA,SAAS,EAAE;QACb,IAAI,CAACvD,cAAc,CAACgB,IAAI,CAACuC,SAAS,CAAC;;IAEvC,CAAC,CAAC,EACF/D,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO5B,EAAE,CAAC,IAAI,CAACmE,mBAAmB,CAACnD,MAAM,CAAC,CAAC;IAC7C,CAAC,CAAC,CACH;EACL;EAEA;EACAoD,yBAAyBA,CAACpD,MAAc;IACtC,MAAM6B,OAAO,GAAG,IAAI,CAACpC,WAAW,CAACqC,cAAc,EAAE;IAEjD,OAAO,IAAI,CAACtC,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACX,MAAM,6BAA6BM,MAAM,EAAE,EAAE;MAAE6B;IAAO,CAAE,CAAC,CACxFvB,IAAI,CACHpB,GAAG,CAACqB,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAGD,QAAQ,CAACE,IAAI,GAAG,IAAI,CAAC,EACxDtB,UAAU,CAACyB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO5B,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;EACAqE,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAACzD,wBAAwB,CAACqD,YAAY,EAAE;EACrD;EAEA;EACAK,eAAeA,CAAA;IACb,OAAO,IAAI,CAACzD,aAAa,CAACoD,YAAY,EAAE;EAC1C;EAEA;EACQnD,oBAAoBA,CAAA;IAC1B;IACAb,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACqB,IAAI,CAC1BlB,SAAS,CAAC,MAAK;MACb,OAAO,IAAI,CAACK,WAAW,CAAC8D,cAAc,EAAE,CAACjD,IAAI,CAC3ClB,SAAS,CAACmB,QAAQ,IAAG;QACnB,IAAIA,QAAQ,EAAEiD,IAAI,EAAEC,GAAG,EAAE;UACvB,OAAO,IAAI,CAAC1D,oBAAoB,CAACQ,QAAQ,CAACiD,IAAI,CAACC,GAAG,EAAE,EAAE,CAAC;;QAEzD,OAAOzE,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,EACFG,UAAU,CAAC,MAAMH,EAAE,CAAC,EAAE,CAAC,CAAC,CACzB;IACH,CAAC,CAAC,CACH,CAAC0E,SAAS,EAAE;IAEb;IACAzE,KAAK,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACqB,IAAI,CAC3BlB,SAAS,CAAC,MAAK;MACb,OAAO,IAAI,CAACK,WAAW,CAAC8D,cAAc,EAAE,CAACjD,IAAI,CAC3ClB,SAAS,CAACmB,QAAQ,IAAG;QACnB,IAAIA,QAAQ,EAAEiD,IAAI,EAAEC,GAAG,EAAE;UACvB,OAAO,IAAI,CAACT,gBAAgB,CAACzC,QAAQ,CAACiD,IAAI,CAACC,GAAG,CAAC;;QAEjD,OAAOzE,EAAE,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC,EACFG,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAI,CAAC,CAAC,CAC3B;IACH,CAAC,CAAC,CACH,CAAC0E,SAAS,EAAE;EACf;EAEA;EACQb,kBAAkBA,CAACtC,QAAa;IACtC,IAAIA,QAAQ,CAACoD,WAAW,IAAIpD,QAAQ,CAACqD,eAAe,EAAE;MACpD,MAAMC,eAAe,GAAG,IAAI,CAAChE,aAAa,CAACiE,KAAK,IAAI,EAAE;MACtD,IAAI,CAACjE,aAAa,CAACc,IAAI,CAAC;QACtB,GAAGkD,eAAe;QAClBF,WAAW,EAAEpD,QAAQ,CAACoD,WAAW;QACjCC,eAAe,EAAErD,QAAQ,CAACqD,eAAe;QACzCG,UAAU,EAAE,IAAI9B,IAAI;OACrB,CAAC;;EAEN;EAEA;EACA+B,gBAAgBA,CAAC1C,SAAiB,EAAEgB,QAAA,GAAgB,EAAE;IACpD,OAAO,IAAI,CAACJ,gBAAgB,CAAC,cAAc,EAAEZ,SAAS,EAAE,SAAS,EAAEgB,QAAQ,CAAC;EAC9E;EAEA2B,iBAAiBA,CAAC3C,SAAiB,EAAE4C,QAAgB,EAAE5B,QAAA,GAAgB,EAAE;IACvE,OAAO,IAAI,CAACJ,gBAAgB,CAAC,eAAe,EAAEZ,SAAS,EAAE,SAAS,EAAE;MAClE,GAAGgB,QAAQ;MACX4B;KACD,CAAC;EACJ;EAEAC,oBAAoBA,CAAC7C,SAAiB,EAAEgB,QAAA,GAAgB,EAAE;IACxD,OAAO,IAAI,CAACJ,gBAAgB,CAAC,kBAAkB,EAAEZ,SAAS,EAAE,SAAS,EAAEgB,QAAQ,CAAC;EAClF;EAEA8B,YAAYA,CAAC9C,SAAiB,EAAEgB,QAAA,GAAgB,EAAE;IAChD,OAAO,IAAI,CAACJ,gBAAgB,CAAC,UAAU,EAAEZ,SAAS,EAAE,SAAS,EAAEgB,QAAQ,CAAC;EAC1E;EAEA+B,gBAAgBA,CAAC/C,SAAiB,EAAEgB,QAAA,GAAgB,EAAE;IACpD,OAAO,IAAI,CAACJ,gBAAgB,CAAC,cAAc,EAAEZ,SAAS,EAAE,SAAS,EAAEgB,QAAQ,CAAC;EAC9E;EAEAgC,WAAWA,CAACC,KAAa,EAAEC,OAAA,GAAe,EAAE,EAAEC,OAAA,GAAkB,CAAC;IAC/D,OAAO,IAAI,CAACvC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;MACzDwC,WAAW,EAAEH,KAAK;MAClBC,OAAO;MACPG,YAAY,EAAEF;KACf,CAAC;EACJ;EAEAG,mBAAmBA,CAAC5D,QAAgB,EAAEsB,QAAA,GAAgB,EAAE;IACtD,OAAO,IAAI,CAACJ,gBAAgB,CAAC,iBAAiB,EAAElB,QAAQ,EAAE,UAAU,EAAEsB,QAAQ,CAAC;EACjF;EAEAuC,iBAAiBA,CAACC,QAAgB,EAAExC,QAAA,GAAgB,EAAE;IACpD,OAAO,IAAI,CAACJ,gBAAgB,CAAC,eAAe,EAAE4C,QAAQ,EAAE,QAAQ,EAAExC,QAAQ,CAAC;EAC7E;EAEAyC,aAAaA,CAACC,MAAc,EAAErD,QAAA,GAAmB,CAAC,EAAEW,QAAA,GAAgB,EAAE;IACpE,OAAO,IAAI,CAACJ,gBAAgB,CAAC,WAAW,EAAE8C,MAAM,EAAE,MAAM,EAAE;MACxD,GAAG1C,QAAQ;MACXX;KACD,CAAC;EACJ;EAEAsD,cAAcA,CAACC,OAAe,EAAEvD,QAAA,GAAmB,CAAC,EAAEW,QAAA,GAAgB,EAAE;IACtE,OAAO,IAAI,CAACJ,gBAAgB,CAAC,YAAY,EAAEgD,OAAO,EAAE,OAAO,EAAE;MAC3D,GAAG5C,QAAQ;MACXX;KACD,CAAC;EACJ;EAEA;EACAwD,0BAA0BA,CAACnE,QAAgB,EAAEf,KAAA,GAAgB,CAAC;IAC5D;IACAY,OAAO,CAACI,GAAG,CAAC,eAAeD,QAAQ,iCAAiC,CAAC;IACrE,OAAO,IAAI,CAACoE,2BAA2B,CAACpE,QAAQ,EAAEf,KAAK,CAAC;IAExD;;;;;;;;;;EAUF;EAEA;EACQa,4BAA4BA,CAACb,KAAa;IAChD;IACA,OAAO,IAAInB,UAAU,CAACuG,QAAQ,IAAG;MAC/BA,QAAQ,CAAC1E,IAAI,CAAC,EAAE,CAAC;MACjB0E,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQlE,2BAA2BA,CAACnB,KAAa;IAC/C;IACA,OAAO,IAAInB,UAAU,CAACuG,QAAQ,IAAG;MAC/BA,QAAQ,CAAC1E,IAAI,CAAC,EAAE,CAAC;MACjB0E,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEQ/D,0BAA0BA,CAACtB,KAAa;IAC9C,OAAO,IAAI,CAACa,4BAA4B,CAACb,KAAK,CAAC;EACjD;EAEQwB,yBAAyBA,CAACxB,KAAa;IAC7C,OAAO,IAAI,CAACa,4BAA4B,CAACb,KAAK,CAAC;EACjD;EAEQmF,2BAA2BA,CAACpE,QAAgB,EAAEf,KAAa;IACjE,OAAO,IAAI,CAACa,4BAA4B,CAACb,KAAK,CAAC;EACjD;EAEQkD,mBAAmBA,CAACnD,MAAc;IACxC,OAAO;MACLA,MAAM;MACNuF,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MACjBC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,mBAAmB,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC;MACpDC,UAAU,EAAE;QAAEC,GAAG,EAAE,GAAG;QAAEC,GAAG,EAAE;MAAI,CAAE;MACnCC,gBAAgB,EAAE;KACnB;EACH;EAEA;EACAC,mBAAmBA,CAAC/C,SAAwB;IAC1C,IAAI,CAACvD,cAAc,CAACgB,IAAI,CAACuC,SAAS,CAAC;EACrC;EAEA;EACAgD,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACvG,cAAc,CAACsD,YAAY,EAAE;EAC3C;;;uBArWW3D,qBAAqB,EAAA6G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAArBlH,qBAAqB;MAAAmH,OAAA,EAArBnH,qBAAqB,CAAAoH,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}