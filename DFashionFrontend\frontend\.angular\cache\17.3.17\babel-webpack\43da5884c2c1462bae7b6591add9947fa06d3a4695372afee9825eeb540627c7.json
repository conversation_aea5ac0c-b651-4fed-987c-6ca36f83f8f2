{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nfunction VendorProductsComponent_div_7_div_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r2.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction VendorProductsComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"img\", 10);\n    i0.ɵɵelementStart(3, \"div\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 13);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 14)(11, \"span\", 15);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, VendorProductsComponent_div_7_div_1_span_14_Template, 3, 4, \"span\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 17)(16, \"span\");\n    i0.ɵɵelement(17, \"i\", 18);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵelement(20, \"i\", 19);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵelement(23, \"i\", 20);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 21)(26, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function VendorProductsComponent_div_7_div_1_Template_button_click_26_listener() {\n      const product_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.editProduct(product_r2));\n    });\n    i0.ɵɵelement(27, \"i\", 23);\n    i0.ɵɵtext(28, \" Edit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function VendorProductsComponent_div_7_div_1_Template_button_click_29_listener() {\n      const product_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deleteProduct(product_r2));\n    });\n    i0.ɵɵelement(30, \"i\", 25);\n    i0.ɵɵtext(31, \" Delete \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getImageUrl(product_r2.images[0]), i0.ɵɵsanitizeUrl)(\"alt\", product_r2.name);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(product_r2.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r2.status, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r2.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(13, 12, product_r2.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r2.originalPrice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", product_r2.views || 0, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", product_r2.orders || 0, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", product_r2.rating || 0, \"\");\n  }\n}\nfunction VendorProductsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, VendorProductsComponent_div_7_div_1_Template, 32, 15, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.products);\n  }\n}\nfunction VendorProductsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵelement(2, \"i\", 29);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"No products yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Start by adding your first product\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 2);\n    i0.ɵɵtext(8, \"Add Product\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let VendorProductsComponent = /*#__PURE__*/(() => {\n  class VendorProductsComponent {\n    constructor() {\n      this.products = [];\n    }\n    ngOnInit() {\n      this.loadProducts();\n    }\n    loadProducts() {\n      // Load vendor products from API\n      this.products = [];\n    }\n    getImageUrl(image) {\n      if (typeof image === 'string') {\n        return image;\n      }\n      return image?.url || '/assets/images/placeholder.jpg';\n    }\n    editProduct(product) {\n      // TODO: Navigate to edit product page\n      console.log('Edit product:', product);\n    }\n    deleteProduct(product) {\n      if (confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n        // TODO: Implement delete API call\n        this.products = this.products.filter(p => p._id !== product._id);\n      }\n    }\n    static {\n      this.ɵfac = function VendorProductsComponent_Factory(t) {\n        return new (t || VendorProductsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VendorProductsComponent,\n        selectors: [[\"app-vendor-products\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 9,\n        vars: 2,\n        consts: [[1, \"vendor-products-container\"], [1, \"header\"], [\"routerLink\", \"/vendor/products/create\", 1, \"btn-primary\"], [1, \"fas\", \"fa-plus\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"products-grid\"], [\"class\", \"product-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [1, \"product-status\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-stats\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"fas\", \"fa-star\"], [1, \"product-actions\"], [1, \"btn-edit\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn-delete\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"original-price\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"fas\", \"fa-box-open\"]],\n        template: function VendorProductsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"My Products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"a\", 2);\n            i0.ɵɵelement(5, \"i\", 3);\n            i0.ɵɵtext(6, \" Add Product \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(7, VendorProductsComponent_div_7_Template, 2, 1, \"div\", 4)(8, VendorProductsComponent_div_8_Template, 9, 0, \"div\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.products.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.products.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.DecimalPipe, RouterModule, i2.RouterLink],\n        styles: [\".vendor-products-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:24px}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000001a;transition:transform .2s}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 4px 16px #00000026}.product-image[_ngcontent-%COMP%]{position:relative;height:200px;overflow:hidden}.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.product-status[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;padding:4px 8px;border-radius:12px;font-size:.75rem;font-weight:500;text-transform:uppercase}.product-status.active[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.product-status.inactive[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.product-info[_ngcontent-%COMP%]{padding:16px}.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:4px}.product-brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:8px}.product-price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.current-price[_ngcontent-%COMP%]{font-weight:600;color:#333}.original-price[_ngcontent-%COMP%]{text-decoration:line-through;color:#999;font-size:.9rem}.product-stats[_ngcontent-%COMP%]{display:flex;gap:16px;font-size:.85rem;color:#666}.product-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.product-actions[_ngcontent-%COMP%]{display:flex;gap:8px;padding:16px;border-top:1px solid #f0f0f0}.btn-edit[_ngcontent-%COMP%], .btn-delete[_ngcontent-%COMP%]{flex:1;padding:8px 16px;border:none;border-radius:6px;font-size:.85rem;cursor:pointer;transition:all .2s}.btn-edit[_ngcontent-%COMP%]{background:#f8f9fa;color:#495057}.btn-edit[_ngcontent-%COMP%]:hover{background:#e9ecef}.btn-delete[_ngcontent-%COMP%]{background:#fee;color:#dc3545}.btn-delete[_ngcontent-%COMP%]:hover{background:#fdd}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff;padding:12px 24px;border-radius:6px;text-decoration:none;font-weight:500;display:inline-flex;align-items:center;gap:8px;transition:background .2s}.btn-primary[_ngcontent-%COMP%]:hover{background:#0056b3}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#ddd;margin-bottom:20px}.empty-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:10px}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:30px}@media (max-width: 768px){.header[_ngcontent-%COMP%]{flex-direction:column;gap:16px;align-items:stretch}.products-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return VendorProductsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}