{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction AdminLoginComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"email\"), \" \");\n  }\n}\nfunction AdminLoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction AdminLoginComponent_mat_spinner_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 21);\n  }\n}\nfunction AdminLoginComponent_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminLoginComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Signing In...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AdminLoginComponent {\n  constructor(fb, authService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/admin/dashboard']);\n    }\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const {\n        email,\n        password\n      } = this.loginForm.value;\n      this.authService.login(email, password).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.success) {\n            this.snackBar.open('Login successful!', 'Close', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.router.navigate(['/admin/dashboard']);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          const errorMessage = error.error?.message || 'Login failed. Please try again.';\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getErrorMessage(fieldName) {\n    const control = this.loginForm.get(fieldName);\n    if (control?.hasError('required')) {\n      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Password must be at least 6 characters long';\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function AdminLoginComponent_Factory(t) {\n      return new (t || AdminLoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AdminAuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminLoginComponent,\n      selectors: [[\"app-admin-login\"]],\n      decls: 42,\n      vars: 11,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"logo-icon\"], [1, \"subtitle\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", \"full-width\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"login-spinner\", 4, \"ngIf\"], [1, \"login-footer\"], [1, \"footer-links\"], [\"href\", \"#\", 1, \"footer-link\"], [1, \"separator\"], [1, \"login-background\"], [1, \"bg-pattern\"], [\"diameter\", \"20\", 1, \"login-spinner\"]],\n      template: function AdminLoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"shopping_bag\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"DFashion Admin\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\", 5);\n          i0.ɵɵtext(9, \"Sign in to your admin account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function AdminLoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"mat-form-field\", 7)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 8);\n          i0.ɵɵelementStart(15, \"mat-icon\", 9);\n          i0.ɵɵtext(16, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, AdminLoginComponent_mat_error_17_Template, 2, 1, \"mat-error\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 7)(19, \"mat-label\");\n          i0.ɵɵtext(20, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 11);\n          i0.ɵɵelementStart(22, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AdminLoginComponent_Template_button_click_22_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(23, \"mat-icon\");\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, AdminLoginComponent_mat_error_25_Template, 2, 1, \"mat-error\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 13);\n          i0.ɵɵtemplate(27, AdminLoginComponent_mat_spinner_27_Template, 1, 0, \"mat-spinner\", 14)(28, AdminLoginComponent_span_28_Template, 2, 0, \"span\", 10)(29, AdminLoginComponent_span_29_Template, 2, 0, \"span\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 15)(31, \"p\");\n          i0.ɵɵtext(32, \"\\u00A9 2024 DFashion. All rights reserved.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 16)(34, \"a\", 17);\n          i0.ɵɵtext(35, \"Privacy Policy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"span\", 18);\n          i0.ɵɵtext(37, \"\\u2022\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"a\", 17);\n          i0.ɵɵtext(39, \"Terms of Service\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"div\", 19);\n          i0.ɵɵelement(41, \"div\", 20);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatIcon, i7.MatButton, i7.MatIconButton, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatProgressSpinner],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  position: relative;\\n  overflow: hidden;\\n  padding: 2rem;\\n}\\n.login-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\\n  animation: _ngcontent-%COMP%_backgroundShift 20s ease-in-out infinite;\\n}\\n\\n.login-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 0;\\n}\\n.login-background[_ngcontent-%COMP%]   .bg-pattern[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  animation: _ngcontent-%COMP%_float 20s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(180deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_backgroundShift {\\n  0%, 100% {\\n    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\\n  }\\n  50% {\\n    background: radial-gradient(circle at 80% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 20% 80%, rgba(255, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 60% 60%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\\n  }\\n}\\n.login-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.98);\\n  -webkit-backdrop-filter: blur(25px);\\n          backdrop-filter: blur(25px);\\n  border-radius: 28px;\\n  box-shadow: 0 40px 80px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);\\n  padding: 3.5rem;\\n  width: 100%;\\n  max-width: 540px;\\n  position: relative;\\n  z-index: 1;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n.login-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  border-radius: 28px;\\n  pointer-events: none;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  color: #667eea;\\n}\\n.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  letter-spacing: -0.02em;\\n}\\n.login-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n  font-size: 1rem;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%] {\\n  height: 56px;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  margin-top: 1.5rem;\\n  position: relative;\\n  border-radius: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n  color: white;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]   .login-spinner[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.demo-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 1.5rem 0 0.5rem 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-description[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 1rem;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 0.75rem;\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  background: rgba(255, 255, 255, 0.8);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);\\n  border-color: #667eea;\\n  background: rgba(255, 255, 255, 0.95);\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem !important;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .account-role[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 0.95rem;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .account-department[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 0.85rem;\\n  margin: 0.25rem 0;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .account-email[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  opacity: 0.7;\\n}\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.85rem;\\n}\\n.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%]:hover {\\n  color: #764ba2;\\n  text-decoration: underline;\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%] {\\n  color: #ccc;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(180deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200px 0;\\n  }\\n  100% {\\n    background-position: calc(200px + 100%) 0;\\n  }\\n}\\n.login-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  background-size: 200px 100%;\\n  animation: _ngcontent-%COMP%_shimmer 3s infinite;\\n  border-radius: 24px;\\n  pointer-events: none;\\n}\\n\\n@media (max-width: 600px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n    max-width: none;\\n    border-radius: 20px;\\n  }\\n  .login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .demo-accounts[_ngcontent-%COMP%] {\\n    max-height: 250px;\\n  }\\n  .login-button[_ngcontent-%COMP%] {\\n    height: 52px;\\n    font-size: 1rem;\\n  }\\n}\\n  .success-snackbar {\\n  background-color: #4caf50 !important;\\n  color: white !important;\\n}\\n  .error-snackbar {\\n  background-color: #f44336 !important;\\n  color: white !important;\\n}\\n\\n  .mat-form-field-appearance-outline .mat-form-field-outline {\\n  color: #e0e0e0;\\n}\\n  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {\\n  color: #667eea;\\n}\\n  .mat-form-field-appearance-outline .mat-form-field-label {\\n  color: #666;\\n}\\n  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {\\n  color: #667eea;\\n}\\n  .mat-primary .mat-button-focus-overlay {\\n  background-color: #667eea;\\n}\\n\\n.login-spinner[_ngcontent-%COMP%]     circle {\\n  stroke: white;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getErrorMessage", "ɵɵelement", "AdminLoginComponent", "constructor", "fb", "authService", "router", "snackBar", "isLoading", "hidePassword", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "isAuthenticated", "navigate", "onSubmit", "valid", "value", "login", "subscribe", "next", "response", "success", "open", "duration", "panelClass", "error", "errorMessage", "message", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "<PERSON><PERSON><PERSON><PERSON>", "char<PERSON>t", "toUpperCase", "slice", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AdminAuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "AdminLoginComponent_Template", "rf", "ctx", "ɵɵlistener", "AdminLoginComponent_Template_form_ngSubmit_10_listener", "ɵɵtemplate", "AdminLoginComponent_mat_error_17_Template", "AdminLoginComponent_Template_button_click_22_listener", "AdminLoginComponent_mat_error_25_Template", "AdminLoginComponent_mat_spinner_27_Template", "AdminLoginComponent_span_28_Template", "AdminLoginComponent_span_29_Template", "ɵɵproperty", "tmp_1_0", "invalid", "touched", "ɵɵtextInterpolate", "tmp_6_0"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\admin\\auth\\admin-login.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\admin\\auth\\admin-login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AdminAuthService } from '../services/admin-auth.service';\n\n@Component({\n  selector: 'app-admin-login',\n  templateUrl: './admin-login.component.html',\n  styleUrls: ['./admin-login.component.scss']\n})\nexport class AdminLoginComponent implements OnInit {\n  loginForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AdminAuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/admin/dashboard']);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.isLoading = true;\n      const { email, password } = this.loginForm.value;\n\n      this.authService.login(email, password).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.success) {\n            this.snackBar.open('Login successful!', 'Close', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.router.navigate(['/admin/dashboard']);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          const errorMessage = error.error?.message || 'Login failed. Please try again.';\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getErrorMessage(fieldName: string): string {\n    const control = this.loginForm.get(fieldName);\n    if (control?.hasError('required')) {\n      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      return 'Password must be at least 6 characters long';\n    }\n    return '';\n  }\n}\n", "<div class=\"login-container\">\n  <div class=\"login-card\">\n    <!-- Header -->\n    <div class=\"login-header\">\n      <div class=\"logo\">\n        <mat-icon class=\"logo-icon\">shopping_bag</mat-icon>\n        <h1>DFashion Admin</h1>\n      </div>\n      <p class=\"subtitle\">Sign in to your admin account</p>\n    </div>\n\n    <!-- Login Form -->\n    <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n      <!-- Email Field -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\n        <mat-label>Email Address</mat-label>\n        <input matInput \n               type=\"email\" \n               formControlName=\"email\"\n               placeholder=\"Enter your email\"\n               autocomplete=\"email\">\n        <mat-icon matSuffix>email</mat-icon>\n        <mat-error *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\">\n          {{ getErrorMessage('email') }}\n        </mat-error>\n      </mat-form-field>\n\n      <!-- Password Field -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\n        <mat-label>Password</mat-label>\n        <input matInput \n               [type]=\"hidePassword ? 'password' : 'text'\"\n               formControlName=\"password\"\n               placeholder=\"Enter your password\"\n               autocomplete=\"current-password\">\n        <button mat-icon-button \n                matSuffix \n                type=\"button\"\n                (click)=\"hidePassword = !hidePassword\"\n                [attr.aria-label]=\"'Hide password'\"\n                [attr.aria-pressed]=\"hidePassword\">\n          <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n        </button>\n        <mat-error *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\n          {{ getErrorMessage('password') }}\n        </mat-error>\n      </mat-form-field>\n\n      <!-- Login Button -->\n      <button mat-raised-button \n              color=\"primary\" \n              type=\"submit\"\n              class=\"login-button full-width\"\n              [disabled]=\"isLoading\">\n        <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"login-spinner\"></mat-spinner>\n        <span *ngIf=\"!isLoading\">Sign In</span>\n        <span *ngIf=\"isLoading\">Signing In...</span>\n      </button>\n    </form>\n\n\n\n    <!-- Footer -->\n    <div class=\"login-footer\">\n      <p>&copy; 2024 DFashion. All rights reserved.</p>\n      <div class=\"footer-links\">\n        <a href=\"#\" class=\"footer-link\">Privacy Policy</a>\n        <span class=\"separator\">•</span>\n        <a href=\"#\" class=\"footer-link\">Terms of Service</a>\n      </div>\n    </div>\n  </div>\n\n  <!-- Background -->\n  <div class=\"login-background\">\n    <div class=\"bg-pattern\"></div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;ICqB3DC,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,eACF;;;;;IAmBAP,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,kBACF;;;;;IASAP,EAAA,CAAAQ,SAAA,sBAAiF;;;;;IACjFR,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACvCH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD7CpD,OAAM,MAAOM,mBAAmB;EAM9BC,YACUC,EAAe,EACfC,WAA6B,EAC7BC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IASjB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACoB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACX,WAAW,CAACY,eAAe,EAAE,EAAE;MACtC,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;EAE9C;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;MACxB,IAAI,CAACZ,SAAS,GAAG,IAAI;MACrB,MAAM;QAAEI,KAAK;QAAEE;MAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACW,KAAK;MAEhD,IAAI,CAAChB,WAAW,CAACiB,KAAK,CAACV,KAAK,EAAEE,QAAQ,CAAC,CAACS,SAAS,CAAC;QAChDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACjB,SAAS,GAAG,KAAK;UACtB,IAAIiB,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;cAC/CC,QAAQ,EAAE,IAAI;cACdC,UAAU,EAAE,CAAC,kBAAkB;aAChC,CAAC;YACF,IAAI,CAACvB,MAAM,CAACY,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;QAE9C,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,MAAMuB,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEE,OAAO,IAAI,iCAAiC;UAC9E,IAAI,CAACzB,QAAQ,CAACoB,IAAI,CAACI,YAAY,EAAE,OAAO,EAAE;YACxCH,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;QACJ;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACI,oBAAoB,EAAE;;EAE/B;EAIQA,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzB,SAAS,CAAC0B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAzC,eAAeA,CAAC0C,SAAiB;IAC/B,MAAMH,OAAO,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,GAAG,CAACE,SAAS,CAAC;IAC7C,IAAIH,OAAO,EAAEI,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAGD,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC,cAAc;;IAEhF,IAAIP,OAAO,EAAEI,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,oCAAoC;;IAE7C,IAAIJ,OAAO,EAAEI,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,OAAO,6CAA6C;;IAEtD,OAAO,EAAE;EACX;;;uBA5EWzC,mBAAmB,EAAAT,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxD,EAAA,CAAAsD,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1D,EAAA,CAAAsD,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA5D,EAAA,CAAAsD,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnBrD,mBAAmB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNxBrE,EALR,CAAAC,cAAA,aAA6B,aACH,aAEI,aACN,kBACY;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UACpBF,EADoB,CAAAG,YAAA,EAAK,EACnB;UACNH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,oCAA6B;UACnDF,EADmD,CAAAG,YAAA,EAAI,EACjD;UAGNH,EAAA,CAAAC,cAAA,eAAyE;UAA3CD,EAAA,CAAAuE,UAAA,sBAAAC,uDAAA;YAAA,OAAYF,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC;UAGjD1B,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAQ,SAAA,gBAI4B;UAC5BR,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAyE,UAAA,KAAAC,yCAAA,wBAAsF;UAGxF1E,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAQ,SAAA,iBAIuC;UACvCR,EAAA,CAAAC,cAAA,kBAK2C;UAFnCD,EAAA,CAAAuE,UAAA,mBAAAI,sDAAA;YAAA,OAAAL,GAAA,CAAAtD,YAAA,IAAAsD,GAAA,CAAAtD,YAAA;UAAA,EAAsC;UAG5ChB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAoD;UAChEF,EADgE,CAAAG,YAAA,EAAW,EAClE;UACTH,EAAA,CAAAyE,UAAA,KAAAG,yCAAA,wBAA4F;UAG9F5E,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAC,cAAA,kBAI+B;UAG7BD,EAFA,CAAAyE,UAAA,KAAAI,2CAAA,0BAAmE,KAAAC,oCAAA,mBAC1C,KAAAC,oCAAA,mBACD;UAE5B/E,EADE,CAAAG,YAAA,EAAS,EACJ;UAMLH,EADF,CAAAC,cAAA,eAA0B,SACrB;UAAAD,EAAA,CAAAE,MAAA,kDAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE/CH,EADF,CAAAC,cAAA,eAA0B,aACQ;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClDH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAGtDF,EAHsD,CAAAG,YAAA,EAAI,EAChD,EACF,EACF;UAGNH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAQ,SAAA,eAA8B;UAElCR,EADE,CAAAG,YAAA,EAAM,EACF;;;;;UAjEIH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAAgF,UAAA,cAAAV,GAAA,CAAArD,SAAA,CAAuB;UAUbjB,EAAA,CAAAI,SAAA,GAAwE;UAAxEJ,EAAA,CAAAgF,UAAA,WAAAC,OAAA,GAAAX,GAAA,CAAArD,SAAA,CAAA8B,GAAA,4BAAAkC,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAX,GAAA,CAAArD,SAAA,CAAA8B,GAAA,4BAAAkC,OAAA,CAAAE,OAAA,EAAwE;UAS7EnF,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAgF,UAAA,SAAAV,GAAA,CAAAtD,YAAA,uBAA2C;UAQ1ChB,EAAA,CAAAI,SAAA,EAAmC;;UAE/BJ,EAAA,CAAAI,SAAA,GAAoD;UAApDJ,EAAA,CAAAoF,iBAAA,CAAAd,GAAA,CAAAtD,YAAA,mCAAoD;UAEpDhB,EAAA,CAAAI,SAAA,EAA8E;UAA9EJ,EAAA,CAAAgF,UAAA,WAAAK,OAAA,GAAAf,GAAA,CAAArD,SAAA,CAAA8B,GAAA,+BAAAsC,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAf,GAAA,CAAArD,SAAA,CAAA8B,GAAA,+BAAAsC,OAAA,CAAAF,OAAA,EAA8E;UAUpFnF,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAAgF,UAAA,aAAAV,GAAA,CAAAvD,SAAA,CAAsB;UACdf,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAgF,UAAA,SAAAV,GAAA,CAAAvD,SAAA,CAAe;UACtBf,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAgF,UAAA,UAAAV,GAAA,CAAAvD,SAAA,CAAgB;UAChBf,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAgF,UAAA,SAAAV,GAAA,CAAAvD,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}