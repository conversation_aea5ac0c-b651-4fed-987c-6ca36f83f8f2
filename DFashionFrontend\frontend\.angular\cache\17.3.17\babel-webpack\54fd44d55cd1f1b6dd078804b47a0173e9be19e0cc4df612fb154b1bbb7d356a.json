{"ast": null, "code": "import { BehaviorSubject, Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport let CartNewService = /*#__PURE__*/(() => {\n  class CartNewService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n      this.apiUrl = `${environment.apiUrl}/cart-new`;\n      this.cartSubject = new BehaviorSubject(null);\n      this.cartSummarySubject = new BehaviorSubject({\n        totalItems: 0,\n        totalAmount: 0,\n        totalSavings: 0,\n        itemCount: 0\n      });\n      this.cart$ = this.cartSubject.asObservable();\n      this.cartSummary$ = this.cartSummarySubject.asObservable();\n      // Load cart when user logs in\n      this.authService.currentUser$.subscribe(user => {\n        if (user && user.role === 'customer') {\n          this.loadCart();\n        } else {\n          this.clearLocalCart();\n        }\n      });\n    }\n    get currentCart() {\n      return this.cartSubject.value;\n    }\n    get cartItemCount() {\n      return this.cartSummarySubject.value.totalItems;\n    }\n    loadCart() {\n      if (!this.authService.requireCustomerAuth('access cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.get(`${this.apiUrl}`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.cartSubject.next(response.cart);\n          this.cartSummarySubject.next(response.summary);\n        }\n      }));\n    }\n    addToCart(productId, quantity = 1, size, color, addedFrom = 'manual', notes) {\n      if (!this.authService.requireCustomerAuth('add items to cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      const payload = {\n        productId,\n        quantity,\n        size,\n        color,\n        addedFrom,\n        notes\n      };\n      return this.http.post(`${this.apiUrl}/add`, payload, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.cartSubject.next(response.cart);\n          this.cartSummarySubject.next(response.summary);\n          this.showSuccessMessage(response.message);\n        }\n      }));\n    }\n    updateCartItem(itemId, updates) {\n      if (!this.authService.requireCustomerAuth('update cart items')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.put(`${this.apiUrl}/update/${itemId}`, updates, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.cartSubject.next(response.cart);\n          this.cartSummarySubject.next(response.summary);\n          this.showSuccessMessage(response.message);\n        }\n      }));\n    }\n    removeFromCart(itemId) {\n      if (!this.authService.requireCustomerAuth('remove items from cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.delete(`${this.apiUrl}/remove/${itemId}`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.cartSubject.next(response.cart);\n          this.cartSummarySubject.next(response.summary);\n          this.showSuccessMessage(response.message);\n        }\n      }));\n    }\n    clearCart() {\n      if (!this.authService.requireCustomerAuth('clear cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.delete(`${this.apiUrl}/clear`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.cartSubject.next(response.cart);\n          this.cartSummarySubject.next(response.summary);\n          this.showSuccessMessage(response.message);\n        }\n      }));\n    }\n    getCartByVendors() {\n      if (!this.authService.requireCustomerAuth('access cart')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.get(`${this.apiUrl}/vendors`, {\n        headers: this.authService.getAuthHeaders()\n      });\n    }\n    moveToWishlist(itemId) {\n      if (!this.authService.requireCustomerAuth('move items to wishlist')) {\n        return new Observable(observer => observer.error('Authentication required'));\n      }\n      return this.http.post(`${this.apiUrl}/move-to-wishlist/${itemId}`, {}, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(tap(response => {\n        if (response.success) {\n          this.loadCart().subscribe(); // Refresh cart\n          this.showSuccessMessage(response.message);\n        }\n      }));\n    }\n    // Quick add methods for different sources\n    addFromPost(productId, quantity = 1, size, color) {\n      return this.addToCart(productId, quantity, size, color, 'post');\n    }\n    addFromStory(productId, quantity = 1, size, color) {\n      return this.addToCart(productId, quantity, size, color, 'story');\n    }\n    addFromProduct(productId, quantity = 1, size, color) {\n      return this.addToCart(productId, quantity, size, color, 'product');\n    }\n    addFromWishlist(productId, quantity = 1, size, color) {\n      return this.addToCart(productId, quantity, size, color, 'wishlist');\n    }\n    // Helper methods\n    isInCart(productId, size, color) {\n      const cart = this.currentCart;\n      if (!cart) return false;\n      return cart.items.some(item => item.product._id === productId && item.size === size && item.color === color);\n    }\n    getCartItemQuantity(productId, size, color) {\n      const cart = this.currentCart;\n      if (!cart) return 0;\n      const item = cart.items.find(item => item.product._id === productId && item.size === size && item.color === color);\n      return item ? item.quantity : 0;\n    }\n    getTotalSavings() {\n      return this.cartSummarySubject.value.totalSavings;\n    }\n    clearLocalCart() {\n      this.cartSubject.next(null);\n      this.cartSummarySubject.next({\n        totalItems: 0,\n        totalAmount: 0,\n        totalSavings: 0,\n        itemCount: 0\n      });\n    }\n    showSuccessMessage(message) {\n      // TODO: Implement proper toast/notification system\n      console.log('Cart Success:', message);\n    }\n    // Utility methods for cart calculations\n    calculateItemTotal(item) {\n      return item.price * item.quantity;\n    }\n    calculateItemSavings(item) {\n      if (!item.originalPrice || item.originalPrice <= item.price) return 0;\n      return (item.originalPrice - item.price) * item.quantity;\n    }\n    getDiscountPercentage(item) {\n      if (!item.originalPrice || item.originalPrice <= item.price) return 0;\n      return Math.round((item.originalPrice - item.price) / item.originalPrice * 100);\n    }\n    static {\n      this.ɵfac = function CartNewService_Factory(t) {\n        return new (t || CartNewService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CartNewService,\n        factory: CartNewService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CartNewService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}